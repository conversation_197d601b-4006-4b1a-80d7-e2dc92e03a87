steps:
  # Get cached node_modules (if exists)
  - id: get-cache
    name: "gcr.io/cloud-builders/gsutil"
    entrypoint: "bash"
    args:
      - "-c"
      - |
        set -euo pipefail
        echo "Checking for cache in gs://${_PROJECT_ID}_cloudbuild/npm/cache/cache.tar.gz"
        gsutil -m cp "gs://${_PROJECT_ID}_cloudbuild/npm/cache/cache.tar.gz" /tmp/cache.tar.gz || echo "Cache archive not found!"
        if [[ -f /tmp/cache.tar.gz ]]; then
          tar -xzf /tmp/cache.tar.gz
          echo "Cache restored"
        fi

  # Install dependencies
  - id: npm-install
    name: "node:${_NODE_VERSION}"
    entrypoint: "npm"
    args: ["ci"]
    dir: "."

  # Cache node_modules for future builds
  - id: cache-node-dependencies
    name: "gcr.io/cloud-builders/gsutil"
    waitFor: ["npm-install"]
    entrypoint: "bash"
    args:
      - "-c"
      - |
        set -euo pipefail
        echo "Caching node_modules to gs://${_PROJECT_ID}_cloudbuild/npm/cache/cache.tar.gz"
        tar -czf /tmp/cache.tar.gz ./node_modules
        gsutil -m cp /tmp/cache.tar.gz "gs://${_PROJECT_ID}_cloudbuild/npm/cache/cache.tar.gz"
        echo "Cache saved"

  # Run unit tests
  - name: "node:${_NODE_VERSION}"
    entrypoint: "npm"
    args: ["run", "test:unit"]
    dir: "./api"

  # Get environment secrets
  - id: get-env
    name: "gcr.io/cloud-builders/gcloud"
    entrypoint: "bash"
    args:
      - "-c"
      - |
        set -euo pipefail
        echo "Fetching env secrets for ${_ENV_NAME}"
        mkdir -p ./api
        gcloud secrets versions access latest --secret=${_ENV_SECRET} > ./api/.env.${_ENV_NAME}
        echo "Env file created"

  - id: get-sentryclirc
    name: "gcr.io/cloud-builders/gcloud"
    entrypoint: "bash"
    args:
      - "-c"
      - |
        set -euo pipefail
        echo "Fetching sentryclirc"
        gcloud secrets versions access latest --secret=sentryclirc > ./api/.sentryclirc
        echo "Sentry config created"

  # Build Docker image with Kaniko
  - id: build
    name: "gcr.io/kaniko-project/executor:latest"
    args:
      - --destination=${_ARTIFACT_REGISTRY}/api:${COMMIT_SHA}
      - --dockerfile=./api/Dockerfile.${_DOCKERFILE_SUFFIX}
      - --cache=${_USE_KANIKO_CACHE}
      - --cache-ttl=24h
      - --context=./
    waitFor: ["get-env", "get-sentryclirc"]

  - id: run integration tests
    name: "gcr.io/cloud-builders/docker"
    entrypoint: "bash"
    args:
      - -c
      - |
        if [ "${_ENV_NAME}" != "dev" ]; then
          echo "ENV_NAME is ${_ENV_NAME}, skipping integration tests (only runs for dev)"
          exit 0
        fi

        echo "ENV_NAME is ${_ENV_NAME}, running integration tests"
        set -euo pipefail
        echo "Starting integration tests with Docker Compose"
        mv ./api/.env.${_ENV_NAME} ./api/.env.local

        if grep -q "^DATABASE_URL=" ./api/.env.local; then
          sed -i 's|^DATABASE_URL=.*|DATABASE_URL=**************************************/local|' ./api/.env.local
        else
          echo "DATABASE_URL=**************************************/local" >> ./api/.env.local
        fi

        docker-compose -f docker-compose.integration.yaml up --build --abort-on-container-exit --exit-code-from integration
    timeout: "600s"

  # Deploy to Cloud Run services
  - id: deploy-api
    name: "gcr.io/cloud-builders/gcloud"
    args:
      - "run"
      - "deploy"
      - "api${_SERVICE_NAME_POSTFIX}"
      - "--image=${_ARTIFACT_REGISTRY}/api:${COMMIT_SHA}"
      - "--set-cloudsql-instances=${_DB_INSTANCE}"
      - "--region=us-central1"
      - "--project=${_PROJECT_ID}"
      - "--platform=managed"
      - "--quiet"
      - "--update-secrets=DATABASE_URL=DATABASE_URL_API${_ENV_POSTFIX}:latest,NODE_OPTIONS=NODE_OPTIONS:latest,PROJECT_ID=PROJECT_ID:latest,GCLOUD_LOGGING=GCLOUD_LOGGING:latest"
      - "--update-env-vars=RUNTIME=${_RUNTIME}"

    waitFor: ["build", "run integration tests"]

  - id: deploy-task
    name: "gcr.io/cloud-builders/gcloud"
    args:
      - "run"
      - "deploy"
      - "task${_SERVICE_NAME_POSTFIX}"
      - "--image=${_ARTIFACT_REGISTRY}/api:${COMMIT_SHA}"
      - "--set-cloudsql-instances=${_DB_INSTANCE}"
      - "--region=us-central1"
      - "--project=${_PROJECT_ID}"
      - "--platform=managed"
      - "--quiet"
      - "--update-secrets=DATABASE_URL=DATABASE_URL_TASK${_ENV_POSTFIX}:latest,PROJECT_ID=PROJECT_ID:latest,NODE_OPTIONS=NODE_OPTIONS:latest,GCLOUD_LOGGING=GCLOUD_LOGGING:latest"
      - "--update-env-vars=RUNTIME=${_RUNTIME}"
    waitFor: ["build", "run integration tests"]

  - id: deploy-worker
    name: "gcr.io/cloud-builders/gcloud"
    args:
      - "run"
      - "deploy"
      - "commission-worker${_SERVICE_NAME_POSTFIX}"
      - "--image=${_ARTIFACT_REGISTRY}/api:${COMMIT_SHA}"
      - "--set-cloudsql-instances=${_DB_INSTANCE}"
      - "--region=us-central1"
      - "--project=${_PROJECT_ID}"
      - "--platform=managed"
      - "--quiet"
      - "--update-secrets=DATABASE_URL=DATABASE_URL_COMMISSION${_ENV_POSTFIX}:latest,NODE_OPTIONS=NODE_OPTIONS:latest,PROJECT_ID=PROJECT_ID:latest,GCLOUD_LOGGING=GCLOUD_LOGGING:latest"
      - "--update-env-vars=RUNTIME=${_RUNTIME}"
    waitFor: ["build", "run integration tests"]

  # Database migrations
  - id: migrate
    name: "gcr.io/cloud-builders/npm"
    env:
      - "NODE_ENV=production"
      - "DATABASE_URL=${_DATABASE_URL_LOCALHOST}"
    entrypoint: "bash"
    args:
      - "-c"
      - |
        set -euo pipefail
        echo "Starting database migration"
        wget -q https://dl.google.com/cloudsql/cloud_sql_proxy.linux.amd64 -O cloud_sql_proxy
        chmod +x cloud_sql_proxy
        ./cloud_sql_proxy -instances=${_DB_INSTANCE}=tcp:${_DB_MIGRATION_PORT} &
        sleep 5
        npx prisma migrate deploy
        echo "Migrations complete"
    timeout: "1300s"
    dir: "./api"
    waitFor: ["build", "run integration tests"]

options:
  pool:
    name: "projects/${_PROJECT_ID}/locations/us-central1/workerPools/Default"
  logging: CLOUD_LOGGING_ONLY
