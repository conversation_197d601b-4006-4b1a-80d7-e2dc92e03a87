# release

## Releases on 2025-07-14

### Version 4.27.0
<details>

### Minor Changes
 - Added support for retro payment allocation

### Patch Changes
 - Updated the statement amount extractor to exclude date-like values such as 06.16 or 6.16, also enable extracting the negative amounts.
 - Added pagination functionality to the agents transactions
</details>

### Version 4.26.3
<details>

### Patch Changes
 - Added support for AI extraction(Gemini, ChatGPT, Claude, etc.) and enabled the ability to choose between multiple extraction methods in document processing.
 - Added tooltips on document status fields to provide more detailed information about document processing results and errors.
</details>

### Version 4.26.2
<details>

### Patch Changes
 - Skip requirement for comp grid criteria if calc method doesn't require comp grid rates.
</details>

## Releases on 2025-07-11

### Version 4.26.1
<details>

### Patch Changes
 - Producers will now only see commissions in paid or approved state.
 - Data actions tool: Add support to 'is not empty' operator for string fields (e.g. Group name field).
 - Only sync policies with state = inforce, and sync commPrem & commAnnPrem
 - Added locks to Views and Fields which are not editable.
 - Allied payment allocation improvements including UI & allocation adjustment
</details>

### Version 4.26.0
<details>

### Minor Changes
 - Fix sorting params are not added to the url query when user sort any column.

### Patch Changes
 - Remove users with deleted access from Admin > Accounts
</details>

## Releases on 2025-07-10

### Version 4.25.1
<details>

### Patch Changes
 - Sync transaction type , group name and sales vps for DMI account
</details>

### Version 4.25.0
<details>

### Minor Changes
 - On policy edit form, add search capability to geo state field.
</details>

## Releases on 2025-07-09

### Version 4.24.1
<details>

### Patch Changes
 - - Update TWC agent payout rate calculation to average result.
  - Fix invalid hook call in JsonStringToggle formatter
  - Add loading skeletons to DynamicSelect
  - Set max width in value on FieldConfig component to reduce overflow (still can happen)
  - Don't retry permission denied requests
 - Allow users to reset payment allocations
 - UI adjustment for grouped commissions & payment allocations
 - Disable member count syncing feature for risk tag
 - Fix creating / saving commissions failing due to transaction_type formatter returning object.
</details>

### Version 4.24.0
<details>

### Minor Changes
 - Fix the pagination not working on many pages.
 - Introduced bulk-add and batch-update functionalities to the commissions page.

### Patch Changes
 - Fix the issue where the commission amount couldn’t be removed due to dependency checks, and optimize the loading UI for document type selection.
 - Fix companies using potential_match column that doesn't exist in our schema caused by: 1. Ignoring the potential_match field 2. Reverting deprecated code
 - Display the carrier grid level name and the house payout grid level name for single carrier mode comp profiles
 - Fix the commission calculation failure with different calc basis
 - Include relational fields for params in data update criteria and actions. Avoid using asynchronous logic for executing custom rules.
 - Fixes for comp reports not being approved
 - Fix some issue on processor part: 1.missing data in company and some fields. 2. Crash when open crate a new processors.
 - Fix grouping failure caused by null premium_amount & split_percent
</details>

## Releases on 2025-07-07

### Version 4.23.3
<details>

### Patch Changes
 - Fix handling of commission basis for is_virutal commission records by not adding child commission_amounts.
</details>

### Version 4.23.2
<details>

### Patch Changes
 - Show multiplier for a multiplier for each comp profile in match criteria when the calculation method is either "Pay commission to grid level" or "Share override to grid level" under enable com grid for setting
</details>

### Version 4.23.1
<details>

### Patch Changes
 - Only calculate renewal profiles if the statement’s compensation type is Renewal Commission.
 - Improves performance by minimizing database load for all required data in comp calc.
</details>

## Releases on 2025-07-06

### Version 4.23.0
<details>

### Minor Changes
 - Support payment allocation which can be enabled by setting FeatureFlags {paymentAllocation: true} in account configs

### Patch Changes
 - Fix for auto populate house rate and rate when creating new lines in comp grid viewer for grids with only carrier rate.
 - Fixed bug for data actions tool where a deleted action still takes effect on data preview
</details>

### Version 4.22.7
<details>

### Patch Changes
 - Fix issue where created_by is missing when uploading a document.
 - Fix companies unable to update document profile mappings due to unique constraint violation in "companies_document_profiles".
 - Fix file upload API failing due to missing service configuration
</details>

## Releases on 2025-07-04

### Version 4.22.6
<details>

### Patch Changes
 - Fixed agent transaction save failure when all transactions had been deleted
</details>

### Version 4.22.5
<details>

### Patch Changes
 - Reduce classification confidence threshold to 0.8 to allow more files result.
 - In admin accounts, sort users by active first. Add user state formatter.
 - `Total premium` now available in saved report group views and exports.
</details>

### Version 4.22.4
<details>

### Patch Changes
 - Not calc receivables for sale reps.
</details>

### Version 4.22.3
<details>

### Patch Changes
 - Support specifying a multiplier for each comp profile in match criteria when the calculation method is either "Pay commission to grid level" or "Share override to grid level".
 - Hotfix the document processing page crash issue
 - Show virtual & virtual type in commission data view
</details>

## Releases on 2025-07-03

### Version 4.22.2
<details>

### Patch Changes
 - Fix for search bar not working on multiple app pages.
</details>

### Version 4.22.1
<details>

### Patch Changes
 - Allow user to select manual grouping calculation method
 - Fixed "Hide commissions with payout" filter on Commissions page that was not returning expected results
 - Fix the issue where the page crashes when selecting an existing mapping.
</details>

## Releases on 2025-07-02

### Version 4.22.0
<details>

### Minor Changes
 - Update Companies & Global Companies Page: 1. Link Suggestions: Added a feature to suggest potential account companies that can be linked to each global company. It shows how many unlinked companies are available and allows linking them directly. 2. Merge Function: Introduced a merge feature that lets users merge selected fields from one global company into another, helping maintain cleaner and more consistent data.

### Patch Changes
 - Fixed an error that occurred when updating data for the 'state' field
 - Correctly populate fields value into generated grouped statement, including new_commission_rate, contacts, split_percentage, agent_commission_payout_rate and agent_payout_rate
 - Optimise the Companies / Global Companies page:
  1. Fixed the issue preventing global companies from being updated.
  2. Optimized the global companies search function by removing irrelevant search options.
  3. Improved the UI for processors and profiles in the Companies / Global Companies page by adding item counts and collapse functionality.
 - Replace comp reports data source from snapshotdata to accounting transactions in new endpoint and new FE component in '.../comp-reports/report_str_id' route.
 - Support running groupings with rules event with no default grouping settings
 - Trigger multi-company mode when multiple documents are uploaded and no classification results are available. And include company and document type details in the upload preview page. Also align the right of type selector.
 - Fix for comp calc not getting results for Hitchings account
 - Allow user to override policy splits
 - Fix the bulk edit issue that when we update the any field the date field(like deposit date) will be auto updated
 - Fix the bug of paying current agent if hierarchy processing is none
  Fix the bug of associate current agents with the appropriate compensation profile when the "Apply to Downlines" option is selected for an upline agent in the comp profile settings.
  Fetch all comp profiles for comp profile matching service
 - In the past, importing a file would automatically create a new mapping, often resulting in many similar or duplicate mappings. Now, users must now click the ‘Save’ button to confirm whether they want to keep the mapping.
</details>

## Releases on 2025-07-01

### Version 4.21.5
<details>

### Patch Changes
 - Enhance the data update tool to support executing queries independently of custom code logic.
 - Fixed grouping failures due to foreign key constraint violations
</details>

