import { customViewDefault } from 'common/constants/account_role_settings';

import { ContactSettings, Roles, ViewsAndFieldsPageSettings } from '@/types';

export enum DirectDownlineDataAccessCompGridRatesOptions {
  NO = 'No',
  YES = 'Yes',
}

export enum ExtendedDownlineDataAccessCompGridRatesOptions {
  NO = 'No',
  EXTENDED_DOWNLINES = 'extended_downlines',
  LEVELS_LOWER = 'levels_lower', // Hardcoded setting only for Transglobal account
  GRID_LEVELS_LOWER = 'grid_levels_lower',
}

export const defaultLandingPage = {
  id: '/reconciliation',
};

export const defaultRoleList = [
  {
    id: Roles.ACCOUNT_ADMIN,
  },
  {
    id: Roles.PRODUCER,
  },
  {
    id: Roles.DATA_SPECIALIST,
  },
];

// TODO: We need to use enums and boolean values instead of strings and add a custom component in the FE so we can use labels there.
export const defaultContactSettingsObject: ContactSettings = {
  uplineHierarchyAccessLevel: 'null',
  downlineHierarchyAccessLevel: 'null',
  directUplineDataAccess: {
    nameConfig: 'Full name',
    emailConfig: 'Yes',
    commissionsConfig: 'No',
  },
  extendedUplineDataAccess: {
    nameConfig: 'Full name',
    emailConfig: 'Yes',
    commissionsConfig: 'No',
  },
  directDownlineDataAccess: {
    nameConfig: 'Full name',
    emailConfig: 'Yes',
    commissionsConfig: 'No',
    policiesConfig: 'No',
    compGridRates: 'No',
  },
  extendedDownlineDataAccess: {
    nameConfig: 'Full name',
    emailConfig: 'Yes',
    commissionsConfig: 'No',
    policiesConfig: 'No',
    payoutLevels: [],
    compGridRates: 'No',
  },
};

export const commonDefaultSettings: Partial<ViewsAndFieldsPageSettings> = {
  show_page: true,
  fields: [],
  default_filters: [],
  read_only: false,
  page_options: [],
  custom_view_name: customViewDefault,
};

export const defaultSettingsObject: Record<string, ViewsAndFieldsPageSettings> =
  {
    customers: {
      custom_fields_id: 'customers',
      key: 'customers',
      page_label: 'Customers',
      menu_label: 'Customers',
      ...(commonDefaultSettings as ViewsAndFieldsPageSettings),
    },
    reconciliation: {
      custom_fields_id: 'reconciliations',
      key: 'reconciliation',
      page_label: 'Reconciliation',
      menu_label: 'Reconciliation',
      ...(commonDefaultSettings as ViewsAndFieldsPageSettings),
    },
    commissions: {
      custom_fields_id: 'statements',
      key: 'commissions',
      page_label: 'Commissions',
      menu_label: 'Commissions',
      ...(commonDefaultSettings as ViewsAndFieldsPageSettings),
    },
    policies: {
      custom_fields_id: 'reports',
      key: 'policies',
      page_label: 'Policies',
      menu_label: 'Policies',
      ...(commonDefaultSettings as ViewsAndFieldsPageSettings),
    },
    insights: {
      custom_fields_id: 'insights',
      key: 'insights',
      page_label: 'Insights',
      menu_label: 'Insights',
      ...(commonDefaultSettings as ViewsAndFieldsPageSettings),
    },
    add_documents: {
      custom_fields_id: 'addDocuments',
      key: 'add_documents',
      page_label: 'Upload data',
      menu_label: 'Upload data',
      ...(commonDefaultSettings as ViewsAndFieldsPageSettings),
    },
    agents: {
      custom_fields_id: 'agents',
      key: 'agents',
      page_label: 'Agents',
      menu_label: 'Agents',
      ...(commonDefaultSettings as ViewsAndFieldsPageSettings),
    },
    agents_groups: {
      custom_fields_id: 'agentsGroups',
      key: 'agents_groups',
      page_label: 'Agent groups',
      menu_label: 'Groups',
      ...(commonDefaultSettings as ViewsAndFieldsPageSettings),
    },
    agents_production: {
      custom_fields_id: 'agentsProduction',
      key: 'agents_production',
      page_label: 'Agent production and commissions',
      menu_label: 'Production',
      ...(commonDefaultSettings as ViewsAndFieldsPageSettings),
    },
    settings_data_processing: {
      custom_fields_id: 'settingsDataProcessing',
      key: 'settings_data_processing',
      page_label: 'Data processing',
      menu_label: 'Data processing',
      show_page: true,
      fields: [],
      default_filters: [],
      read_only: false,
      page_options: `[
      {
        enable_run_reconciliation_button: true,
      }
    ]`,
    },
    carriers_schedules: {
      custom_fields_id: 'carriersSchedules',
      key: 'carriers_schedules',
      page_label: 'Commission receivable schedules',
      menu_label: 'Carriers',
      ...(commonDefaultSettings as ViewsAndFieldsPageSettings),
    },
    compensation_profiles: {
      custom_fields_id: 'compensationProfiles',
      key: 'compensation_profiles',
      page_label: 'Compensation profiles',
      menu_label: 'Comp profiles',
      ...(commonDefaultSettings as ViewsAndFieldsPageSettings),
    },
    compensation_profile_sets: {
      custom_fields_id: 'compensationProfileSets',
      key: 'compensation_profile_sets',
      page_label: 'Compensation profile sets',
      menu_label: 'Comp profile sets',
      ...(commonDefaultSettings as ViewsAndFieldsPageSettings),
    },
    // Deprecated
    // agent_schedules: {
    //   custom_fields_id: 'agentSchedules',
    //   key: 'agent_schedules',
    //   page_label: 'Agent commission schedule profiles',
    //   menu_label: 'Agents',
    //   ...(commonDefaultSettings as ViewsAndFieldsPageSettings),
    // },
    incentives_schedules: {
      custom_fields_id: 'incentivesSchedules',
      key: 'incentives_schedules',
      page_label: 'Agent incentive tiers',
      menu_label: 'Incentives',
      ...(commonDefaultSettings as ViewsAndFieldsPageSettings),
    },
    comp_grids_schedules: {
      custom_fields_id: 'compGridsSchedules',
      key: 'comp_grids_schedules',
      page_label: 'Comp grids',
      menu_label: 'Comp grids',
      ...(commonDefaultSettings as ViewsAndFieldsPageSettings),
    },
    views: {
      custom_fields_id: 'views',
      key: 'views',
      page_label: 'Saved views',
      menu_label: 'Views',
      ...(commonDefaultSettings as ViewsAndFieldsPageSettings),
    },
    reports: {
      custom_fields_id: 'reports',
      key: 'reports',
      page_label: 'Saved reports',
      menu_label: 'Reports',
      ...(commonDefaultSettings as ViewsAndFieldsPageSettings),
    },
    summaries: {
      custom_fields_id: 'summaries',
      key: 'summaries',
      page_label: 'Saved report groups',
      menu_label: 'Summaries',
      ...(commonDefaultSettings as ViewsAndFieldsPageSettings),
    },
    custom_reports: {
      custom_fields_id: 'customReports',
      key: 'custom_reports',
      page_label: 'Custom reports',
      menu_label: 'Custom reports',
      ...(commonDefaultSettings as ViewsAndFieldsPageSettings),
    },
    companies: {
      custom_fields_id: 'companies',
      key: 'companies',
      page_label: 'Companies',
      menu_label: 'Companies',
      ...(commonDefaultSettings as ViewsAndFieldsPageSettings),
    },
    products: {
      custom_fields_id: 'products',
      key: 'products',
      page_label: 'Products',
      menu_label: 'Products',
      ...(commonDefaultSettings as ViewsAndFieldsPageSettings),
    },
    options: {
      custom_fields_id: 'options',
      key: 'options',
      page_label: 'Product options',
      menu_label: 'Options',
      ...(commonDefaultSettings as ViewsAndFieldsPageSettings),
    },
    documents: {
      custom_fields_id: 'documents',
      key: 'documents',
      page_label: 'Documents',
      menu_label: 'Documents',
      ...(commonDefaultSettings as ViewsAndFieldsPageSettings),
    },
  };
