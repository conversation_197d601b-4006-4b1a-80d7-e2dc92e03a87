import { describe, it, expect, vi, beforeEach } from 'vitest';

import { prismaClient as prisma } from '@/lib/prisma';
import { DataStates } from '@/types';
import { CommissionRetrieval } from '@/pages/api/data_processing/commissions/commissionRetrieval';

vi.mock('@/lib/prisma', () => ({
  prismaClient: {
    accounts: { findFirst: vi.fn() },
    companies: { findMany: vi.fn() },
    company_products: { findMany: vi.fn() },
    company_product_options: { findMany: vi.fn() },
    comp_grid_criteria: { findMany: vi.fn() },
    comp_grid_products: { findMany: vi.fn() },
    statement_data: { findMany: vi.fn() },
  },
}));

const mockAccountId = 'test-account';
const mockCompanyIds = ['company1', 'company2'];
const mockProductIds = ['prod1', 'prod2'];
const regressionTestMode = false;

let service: CommissionRetrieval;

beforeEach(() => {
  service = new CommissionRetrieval();
  vi.clearAllMocks();
});

describe('CommissionRetrieval', () => {
  describe('Utility Fetchers', () => {
    it('fetchAccountSettings should call prisma.accounts.findFirst', async () => {
      await service.fetchAccountSettings(mockAccountId, regressionTestMode);
      expect(prisma.accounts.findFirst).toHaveBeenCalledWith({
        where: { str_id: mockAccountId, state: 'active' },
        accountInject: true,
      });
    });

    it('fetchCompanies should format OR condition correctly', async () => {
      const nameArray = ['Carrier A', 'Carrier B'];
      await service.fetchCompanies(
        mockAccountId,
        nameArray,
        regressionTestMode
      );
      expect(prisma.companies.findMany).toHaveBeenCalledWith({
        where: {
          account_id: mockAccountId,
          state: DataStates.ACTIVE,
          OR: [
            { company_name: { equals: 'Carrier A', mode: 'insensitive' } },
            { company_name: { equals: 'Carrier B', mode: 'insensitive' } },
          ],
        },
        accountInject: true,
      });
    });

    it('fetchCompanyProducts should handle empty companyIds', async () => {
      await service.fetchCompanyProducts(mockAccountId, [], regressionTestMode);
      expect(prisma.company_products.findMany).toHaveBeenCalledWith({
        where: {
          account_id: mockAccountId,
          company_id: undefined,
          state: DataStates.ACTIVE,
        },
        accountInject: true,
      });
    });

    it('fetchProductOptions should include product_id filter when productIds provided', async () => {
      await service.fetchProductOptions(
        mockAccountId,
        mockProductIds,
        regressionTestMode
      );
      expect(prisma.company_product_options.findMany).toHaveBeenCalledWith({
        where: {
          account_id: mockAccountId,
          product_id: { in: mockProductIds },
          state: DataStates.ACTIVE,
        },
        accountInject: true,
      });
    });

    it('fetchCompGridCriteria includes correct nested include structure', async () => {
      await service.fetchCompGridCriteria(
        mockAccountId,
        mockCompanyIds,
        regressionTestMode
      );
      expect(prisma.comp_grid_criteria.findMany).toHaveBeenCalledWith({
        where: {
          account_id: mockAccountId,
          company_id: { in: mockCompanyIds },
          state: DataStates.ACTIVE,
        },
        accountInject: true,
        include: {
          comp_grid_product: { where: { state: DataStates.ACTIVE } },
          comp_grid: {
            where: { state: DataStates.ACTIVE },
            select: { id: true },
          },
        },
      });
    });

    it('fetchCompGridProducts filters by comp_grid_id when provided', async () => {
      const compGridIds = ['grid1', 'grid2'];
      await service.fetchCompGridProducts(
        mockAccountId,
        compGridIds,
        regressionTestMode
      );
      expect(prisma.comp_grid_products.findMany).toHaveBeenCalledWith({
        where: {
          account_id: mockAccountId,
          comp_grid_id: { in: compGridIds },
          state: DataStates.ACTIVE,
        },
        accountInject: true,
        include: {
          company_products: { where: { state: DataStates.ACTIVE } },
        },
      });
    });
  });

  describe('getAllData', () => {
    it('should return all data in getAllData()', async () => {
      const mockStatementData = [
        {
          id: 1,
          document_id: 'doc-1',
          carrier_name: 'Carrier A',
          writing_carrier_name: 'Carrier B',
          children_data: [],
        },
      ];

      (
        prisma.accounts.findFirst as unknown as ReturnType<typeof vi.fn>
      ).mockResolvedValue({ comp_grids_enabled: true });
      (
        prisma.statement_data.findMany as unknown as ReturnType<typeof vi.fn>
      ).mockResolvedValue(mockStatementData);
      (
        prisma.companies.findMany as unknown as ReturnType<typeof vi.fn>
      ).mockResolvedValue([{ id: 'comp1' }]);
      (
        prisma.company_products.findMany as unknown as ReturnType<typeof vi.fn>
      ).mockResolvedValue([{ id: 'prod1' }]);
      (
        prisma.company_product_options.findMany as unknown as ReturnType<
          typeof vi.fn
        >
      ).mockResolvedValue([{ id: 'opt1' }]);
      (
        prisma.comp_grid_criteria.findMany as unknown as ReturnType<
          typeof vi.fn
        >
      ).mockResolvedValue([{ comp_grid: { id: 'grid1' } }]);
      (
        prisma.comp_grid_products.findMany as unknown as ReturnType<
          typeof vi.fn
        >
      ).mockResolvedValue([{ id: 'gridprod1' }]);

      const result = await service.getAllData({
        account_id: 'acc1',
        regressionTestMode: false,
      });

      expect(result).toHaveProperty('lookupData');
      expect(result).toHaveProperty('statementData');
    });
  });

  describe('getTaskStatements', () => {
    it('should fetch paginated statements in getTaskStatements()', async () => {
      const mockStatements = new Array(3).fill({
        id: 1,
        document_id: 'doc1',
        carrier_name: 'Carrier A',
        writing_carrier_name: 'Carrier B',
        report: {},
        children_data: [],
        state: DataStates.ACTIVE,
      });

      (prisma.statement_data.findMany as unknown as ReturnType<typeof vi.fn>)
        .mockResolvedValueOnce(mockStatements)
        .mockResolvedValueOnce([]);

      const result = await service.getTaskStatements(
        {
          id: 1,
          ids: null,
          useGroupedCommissions: true,
          documentId: null,
          documentIds: null,
          startDate: null,
          endDate: null,
          policyId: null,
          payingEntity: null,
          isSync: false,
          onlyGetProfilesRates: false,
          statementIds: null,
          master_str_id: null,
          contactIds: null,
          worker: null,
          regressionTestMode: false,
          regressionAccount: null,
        },
        'acc1'
      );

      expect(result.length).toBeGreaterThan(0);
      expect(result[0]).toHaveProperty('carrier_name');
      expect(prisma.statement_data.findMany).toHaveBeenCalled();
    });
  });
});
