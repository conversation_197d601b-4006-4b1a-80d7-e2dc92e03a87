import { NextApiRequest, NextApiResponse } from 'next';
import { create<PERSON><PERSON><PERSON>, Get, Post, Req, Res } from 'next-api-decorators';
import {
  PerAgentGetTransactionsSchema,
  PerAgentPostTransactionSchema,
} from 'common/dto/accounting_transactions';
import type {
  PerAgentGetTransactionsDto,
  PerAgentPostTransactionDto,
} from 'common/dto/accounting_transactions';

import { container } from '@/ioc';
import { AccountingService, IAccountingService } from '@/services/accounting';
import { ZodBody, ZodQuery } from '@/lib/decorators';
import { ContactService, IContactService } from '@/services/contact';
import { BaseHandler } from '@/lib/baseHandler';
import { withAuth } from '@/lib/middlewares';
import { ExtNextApiRequest, ExtNextApiResponse } from '@/types';

class Handler extends BaseHandler {
  private accountingService: IAccountingService;
  private contactService: IContactService;

  constructor() {
    super();
    this.accountingService = container.get(AccountingService);
    this.contactService = container.get(ContactService);
  }

  @Get()
  async get(
    @Req() _req: ExtNextApiRequest & NextApiRequest,
    @Res() res: ExtNextApiResponse & NextApiResponse,
    @(ZodQuery(PerAgentGetTransactionsSchema)())
    query: PerAgentGetTransactionsDto
  ) {
    const { agent_str_id, start_date, end_date, transaction_str_id } = query;
    const queryPagination =
      query.page && query.limit
        ? { page: Number(query.page), limit: Number(query.limit) }
        : undefined;

    const response =
      await this.accountingService.getAccountingTransactionsByContact({
        agent_str_id,
        start_date,
        end_date,
        transaction_str_id,
        pagination: queryPagination,
      });

    res.json({
      transactions: response.transactions,
      total_transactions: response.total_transactions,
      pagination: response.pagination,
    });
  }

  @Post()
  async create(
    @Req() req: ExtNextApiRequest & NextApiRequest,
    @Res() res: ExtNextApiResponse & NextApiResponse,
    @(ZodBody(PerAgentPostTransactionSchema)())
    body: PerAgentPostTransactionDto
  ) {
    const { agent_str_id, transactions } = body;

    const agentId =
      await this.contactService.getContactIdsByStrIdList(agent_str_id);

    const result = await this.accountingService.manageAccountingDataByContact({
      contactId: agentId[0],
      accountId: req.account_id,
      uid: req.uid,
      ouid: req.ouid,
      updatedAccountingData: transactions.updates,
      deletedAccountingData: transactions.deletes,
    });

    res.json({ success: true, data: result });
  }
}

export default withAuth(createHandler(Handler));
