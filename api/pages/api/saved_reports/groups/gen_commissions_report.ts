import { saved_report_type } from '@prisma/client';
import { nanoid } from 'nanoid';
import CommonFormatter from 'common/Formatter';
import { createHandler, Post, Req, Res } from 'next-api-decorators';
import { NextApiRequest, NextApiResponse } from 'next';
import {
  CompReportDateFilter,
  ReportGroupsStatuses,
  SavedReportStatuses,
} from 'common/globalTypes';
import { DEFAULT_FILTER } from 'common/constants';

import { withAuth } from '@/lib/middlewares';
import prisma from '@/lib/prisma';
import { _getStatementData } from '@/pages/api/statement_data';
import {
  ExtNextApiRequest,
  ExtNextApiResponse,
  Roles,
  savedReportsGroupsTemplates,
} from '@/types';
import { BaseHandler } from '@/lib/baseHandler';
import { AccountingService } from '@/services/accounting';
import { container } from '@/ioc';
import { chunkArray, limitConcurrency } from '@/lib/helpers';

class GenerateCompReportHandler extends BaseHandler {
  constructor() {
    super();
  }

  @Post()
  async postGenerateCompReport(
    @Req() req: NextApiRequest & ExtNextApiRequest,
    @Res() res: ExtNextApiResponse & NextApiResponse
  ) {
    const result = await generateCommissionPayoutReportSummary(req);
    res.status(200).json({
      success: true,
      data: result,
    });
  }
}

export default withAuth(createHandler(GenerateCompReportHandler));

// TODO: Use CompReportsService when circular depenedency error involving _getStatementData is resolved
const generateCommissionPayoutReportSummary = async (req) => {
  const res = {
    reports: [],
    reportGroup: null,
  };

  const {
    contacts,
    compensation_types,
    contact_statuses,
    contact_payable_statuses,
    agent_commission_statuses,
    agent_commission_statuses2,
    include_linked_commissions,
    include_zero_split_commissions,
    dateFilter,
    reportPrefix,
  } = req.body;

  if (contacts.length === 0) {
    return res;
  }

  const accountingService = container.get<AccountingService>(AccountingService);

  // Check if '(Blank)' exists and include null in the query
  const contactStatusesQuery = contact_statuses.includes(
    DEFAULT_FILTER.BLANK_OPTION
  )
    ? {
        OR: [
          {
            status: {
              in: contact_statuses.filter(
                (status) => status !== DEFAULT_FILTER.BLANK_OPTION
              ),
            },
          },
          { status: null },
        ],
      }
    : { status: { in: contact_statuses } };

  const contactPayableStatusesQuery = contact_payable_statuses.includes(
    DEFAULT_FILTER.BLANK_OPTION
  )
    ? {
        OR: [
          {
            payable_status: {
              in: contact_payable_statuses.filter(
                (status) => status !== DEFAULT_FILTER.BLANK_OPTION
              ),
            },
          },
          { payable_status: null },
        ],
      }
    : { payable_status: { in: contact_payable_statuses } };

  const contactList = await prisma.contacts.findMany({
    where: {
      id: { in: contacts },
      account_id: req.account_id,
      ...contactStatusesQuery,
      ...contactPayableStatusesQuery,
    },
  });

  console.log(
    `Generating comp reports for ${CommonFormatter.date(new Date(req.body.start_date))} - ${CommonFormatter.date(new Date(req.body.end_date))} for the following agents (${contactList.length}): ${contactList.map((item) => CommonFormatter.contact(item, { account_id: req?.account_id })).join(', ')}`
  );

  const contactListPromises = async (contact) => {
    const contactId = contact.id;
    const contactStrId = contact.str_id;
    const contactUser = contact.user_str_id;
    const contactPayableStatus = contact.payable_status;
    const contactBankInfo = contact.bank_info;
    const _req = {
      ...req,
      query: {
        ...req.body,
        compensation_type: compensation_types,
        agent_commissions_status: agent_commission_statuses,
        agent_commissions_status2: agent_commission_statuses2,
        contacts: contact.str_id,
        processing_date_end:
          dateFilter === CompReportDateFilter.ProcessingDate
            ? req.body.end_date
            : '',
        processing_date_start:
          dateFilter === CompReportDateFilter.ProcessingDate
            ? req.body.start_date
            : '',
        payment_date_end:
          dateFilter === CompReportDateFilter.PaymentDate
            ? req.body.end_date
            : '',
        payment_date_start:
          dateFilter === CompReportDateFilter.PaymentDate
            ? req.body.start_date
            : '',
        agent_commissions: contact.str_id,
        is_commission_report: true,
        incl_linked: include_linked_commissions,
        incl_zero_split: include_zero_split_commissions,
      },
      role_id: Roles.PRODUCER,
    };
    console.log(
      `Generating comp report for ${CommonFormatter.contact(contact, { incl_email: true, account_id: req.account_id })}`
    );

    const data = await _getStatementData(_req);

    // Don't write empty reports
    if (+data[1] > 0) {
      const _fieldOptions = data[4];
      const fieldOptions = {
        contacts: undefined,
        payment_date_start: data[2]?.payment_date,
        payment_date_end: data[3]?.payment_date,
        processing_date_start: req.body.start_date,
        processing_date_end: req.body.end_date,
        ..._fieldOptions,
      };
      fieldOptions.contacts = Array.from(
        new Set(fieldOptions.contacts?.flat() ?? [])
      );

      const contactChunks = chunkArray(fieldOptions.contacts, 1000);

      const allContactInfos = await limitConcurrency(
        async (contacts) => {
          return await prisma.contacts.findMany({
            where: {
              str_id: {
                in: contacts,
              },
            },
            select: {
              str_id: true,
              first_name: true,
              last_name: true,
              type: true,
            },
          });
        },
        contactChunks,
        10
      );

      const contactInfoMap = new Map(
        allContactInfos.flat().map((item) => [item.str_id, item])
      );

      const contactsObject = fieldOptions.contacts.map((item) => {
        const contactInfo = contactInfoMap.get(item);
        if (!contactInfo) return { id: item, name: item };
        return {
          id: item,
          name: CommonFormatter.contact(contactInfo, {
            account_id: req.account_id,
          }),
          type: contactInfo.type,
        };
      });

      fieldOptions.contacts = contactsObject;

      // Manage contact balance
      const accountingDetailsIds = data[0]
        .map((item) =>
          item.accounting_transaction_details
            .filter((detail) => detail.contact_id === contactId)
            .map((detail) => detail.id)
        )
        .flat()
        .filter(Boolean);

      const accountingDetailsAmounts = data[0]
        .map((item) =>
          item.accounting_transaction_details
            .filter((detail) => detail.contact_id === contactId)
            .map((detail) => Number(detail.amount))
        )
        .flat()
        .filter((amount) => typeof amount === 'number' && !isNaN(amount));

      const { reportBalance, contactBalance, currentBalance, transactionId } =
        await accountingService.createTransactionWithDetails(
          req.account_id,
          contactId,
          accountingDetailsIds,
          accountingDetailsAmounts,
          req.uid,
          req.ouid
        );

      const snapshotData = {
        headers: JSON.parse(req.body.headers),
        data: {
          data: data[0],
          count: data[1],
          fieldOptions,
          totals: data[5],
          contactStrId: contactStrId,
          contactUser: contactUser,
          contactPayableStatus: contactPayableStatus,
          contactBankInfo: contactBankInfo,
          reportBalance: reportBalance,
          contactBalance: contactBalance,
          currentBalance: currentBalance,
          transactionId: transactionId,
        },
        reportPage: 'commissions',
      };

      const reportName =
        reportPrefix === ''
          ? `Commissions: ${new Date(req.body.start_date).toLocaleDateString()} - ${new Date(req.body.end_date).toLocaleDateString()} - ${CommonFormatter.contact(contact, { account_id: req.account_id })}`
          : `${reportPrefix} - ${CommonFormatter.contact(contact, { account_id: req.account_id })}`;
      console.log(`Creating saved report: ${reportName}`);
      const responseData = await prisma.saved_reports.create({
        data: {
          name: reportName,
          page: 'commissions',
          str_id: nanoid(),
          snapshot_data: snapshotData,
          contact_id: contact.id,
          uid: req.uid,
          account_id: req.account_id,
          type: saved_report_type.report,
          status: SavedReportStatuses.DRAFT,
          created_by: req.uid,
          created_proxied_by: req.ouid,
        },
      });

      // Link the created report to the correspponding transaction
      await accountingService.linkTransactionToReport(
        req.account_id,
        transactionId,
        responseData.id,
        req.uid,
        req.ouid
      );

      res.reports.push(responseData);
    }
  };

  await limitConcurrency(contactListPromises, contactList, 30);

  if (res.reports.length > 0) {
    console.log(
      `${res.reports.length} commission reports generated. Creating saved report group.`
    );
    const resReportGroup = await prisma.saved_report_groups.create({
      data: {
        name: `Commissions: ${new Date(req.body.start_date).toLocaleDateString()} - ${new Date(req.body.end_date).toLocaleDateString()}`,
        str_id: nanoid(),
        uid: req.uid,
        account_id: req.account_id,
        status: ReportGroupsStatuses.DRAFT,
        saved_reports: {
          connect: res.reports.map((item) => ({ id: item.id })),
        },
        template: savedReportsGroupsTemplates.COMMISSION_PAYOUT,
        created_by: req.uid,
        created_proxied_by: req.ouid,
      },
    });

    res.reportGroup = resReportGroup;
  }

  return res;
};
