import { create<PERSON><PERSON><PERSON>, <PERSON>, Req, Res } from 'next-api-decorators';
import { NextApiRequest, NextApiResponse } from 'next';

import { withAuth } from '@/lib/middlewares';
import { _getReconciliationsData } from '@/pages/api/reconciliation_data';
import { ExtNextApiRequest, ExtNextApiResponse, Roles } from '@/types';
import { getExportData } from './base';
import { BaseHandler } from '@/lib/baseHandler';
import { exportCsvResponse } from './base/export-csv-response';
import {
  checkReconciliationVersion,
  getReconciliationsDataV2,
} from '../reconciliation_data2/getReconciliationData';
import { RECONCILIATION_VERSION } from '@/constants/reconciliation-data';

class ExportReconciliationDataHandler extends BaseHandler {
  @Get()
  async exportHandler(
    @Req() req: NextApiRequest & ExtNextApiRequest,
    @Res() res: ExtNextApiResponse & NextApiResponse
  ) {
    const {
      query: { producer_view = false },
    } = req;

    if (producer_view) req.role_id = Roles.PRODUCER.toString();

    const version = await checkReconciliationVersion(req.account_id);
    let data = [];

    if (version === RECONCILIATION_VERSION.V2) {
      const result = await getReconciliationsDataV2(req, true);
      data = result.data;
    } else {
      const { data: result } = await _getReconciliationsData(req, true);
      data = result[0];
    }

    const { isExportRelationship } = req.query;

    const filterList = await getExportData({
      data,
      table: 'reconciliation_data',
      roleId: req.role_id,
      accountId: req.account_id,
      isExportRelationship: isExportRelationship === 'true',
      uid: req.uid,
      reconciliationVersion: version,
    });

    // Set the response headers to trigger the download
    res.setHeader(
      'Content-Disposition',
      'attachment; filename="Fintary-Reconciliation-Data-Export.csv"'
    );
    // Converting the data to CSV
    exportCsvResponse(filterList, res);
  }
}

export default withAuth(createHandler(ExportReconciliationDataHandler));
