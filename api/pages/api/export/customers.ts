import { GetCustomerDTO } from 'common/customer/customer.validator';
import { create<PERSON><PERSON>ler, Get, Req, Res } from 'next-api-decorators';
import { NextApiRequest, NextApiResponse } from 'next';
import { getCustomerFieldConfig } from 'common/field-config/customer';

import { withAuth } from '@/lib/middlewares';
import { ExtNextApiRequest, ExtNextApiResponse } from '@/types';
import { container } from '@/ioc';
import { CustomerService } from '@/services/customers/service';
import { BaseHandler } from '@/lib/baseHandler';
import { exportCsvResponse } from './base/export-csv-response';
import {
  formatExportData,
  mapHeaderFromFieldConfigs,
} from './base/export_data_processor';

class ExportCustomersHandler extends BaseHandler {
  @Get()
  async exportHandler(
    @Req() req: NextApiRequest & ExtNextApiRequest,
    @Res() res: ExtNextApiResponse & NextApiResponse
  ) {
    const { isExportRelationship } = req.query;
    const customerService = container.get(CustomerService);
    const params = {
      ...req.query,
      account_id: req.account_id,
    } as GetCustomerDTO;

    const result = await customerService.getCustomers({
      ...params,
    });

    const config = getCustomerFieldConfig();

    const headerConfig = mapHeaderFromFieldConfigs(
      config.fields,
      isExportRelationship
    );

    const formattedData = formatExportData(result.data, headerConfig);

    // Set the response headers to trigger the download
    res.setHeader(
      'Content-Disposition',
      `attachment; filename="Fintary-${config.label}-Export.csv"`
    );
    // Converting the data to CSV
    exportCsvResponse(formattedData, res);
  }
}

export default withAuth(createHandler(ExportCustomersHandler));
