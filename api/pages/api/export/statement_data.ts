import { create<PERSON><PERSON><PERSON>, <PERSON>, Req, Res } from 'next-api-decorators';
import { NextApiRequest, NextApiResponse } from 'next';
import * as dto from 'common/dto/statement_data';
import * as Sentry from '@sentry/nextjs';

import { withAuth } from '@/lib/middlewares';
import { _getStatementData } from '@/pages/api/statement_data';
import { ExtNextApiRequest, ExtNextApiResponse, Roles } from '@/types';
import { ZodQuery } from '@/lib/decorators';
import { formatPdfCurrency } from '@/services/export-report/format-currency';
import { getExportData } from '@/pages/api/export/base';
import {
  ExportReportType,
  generateReport,
} from '@/services/export-report/export-reports';
import { exportCsvResponse } from './base/export-csv-response';
import { BaseHandler } from '@/lib/baseHandler';

class ExportCommissionsHandler extends BaseHandler {
  @Get()
  async getExportStatementData(
    @Req() req: ExtNextApiRequest & NextApiRequest,
    @Res() res: ExtNextApiResponse & NextApiResponse,
    @(ZodQuery(dto.StatementDataQueryDtoSchema)())
    query: dto.StatementDataQueryDto
  ) {
    return exportStatementData(req, res, query);
  }
}

const exportStatementData = async (
  req: ExtNextApiRequest,
  res: ExtNextApiResponse,
  query: dto.StatementDataQueryDto
) => {
  try {
    let contactStrId: string | undefined;

    const exportQuery: Record<string, any> = {
      ...query,
    };

    if (query.producer_view) {
      exportQuery['is_commission_report'] = 'true';
      req.role_id = Roles.PRODUCER.toString();
      if (typeof query.contacts === 'string') {
        contactStrId = query.contacts;
      }
    }

    const [data] = await _getStatementData(req, query, true);

    // TODO: Consolidate export fields logic with FE
    // Export adjustments to align export data with FE
    const subPolicyDataIfEmpty = true;
    if (subPolicyDataIfEmpty && data.length > 0) {
      data.forEach((item) => {
        if (!item.transaction_type && item.report?.transaction_type) {
          item.transaction_type = `${item.report?.transaction_type}*`;
        }
        if (!item.internal_id && item.report?.internal_id) {
          item.internal_id = `${item.report?.internal_id}*`;
        }
        if (
          item.issue_age === null &&
          item.report?.issue_age !== null &&
          item.report?.issue_age !== undefined
        ) {
          item.issue_age = `${item.report?.issue_age}*`;
        }
        if (
          (item.group_name === null || item.group_name === '') &&
          item.report?.group_name
        ) {
          item.group_name = `${item.report?.group_name}*`;
        }
      });
    }

    let filterList = await getExportData({
      data,
      table: 'statement_data',
      roleId: req.role_id,
      accountId: req.account_id,
      isExportRelationship: query.is_export_relationship,
      uid: req.uid,
      contactStrId,
    });

    filterList = formatPdfCurrency(filterList);

    let commissionsReport: any[] = [...filterList];

    if (query.json_only) {
      res.status(200).json(commissionsReport);
      return;
    }

    if (query.report_processor) {
      commissionsReport = await generateReport(filterList, {
        reportProcessor: query.report_processor,
        producerView: query.producer_view,
        isExportRelationship: query.is_export_relationship,
        roleId: req.role_id,
        accountId: req.account_id,
        uid: req.uid,
        reportType: ExportReportType.COMMISSIONS,
      });
    }
    res.setHeader(
      'Content-Disposition',
      'attachment; filename="Fintary-Commissions-Data-Export.csv"'
    );
    exportCsvResponse(commissionsReport, res);
  } catch (error) {
    console.error('Error generating commissions report:', error);
    Sentry.captureException(error);
    res.status(500).json({
      error: `Error generating commissions report`,
    });
  }
};

export default withAuth(createHandler(ExportCommissionsHandler));
