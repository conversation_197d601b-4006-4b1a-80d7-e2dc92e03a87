import dayjs from 'dayjs';
import { RECONCILIATION_V2_ONLY_FIELDS } from 'common/constants/reconciliation';

import prisma from '@/lib/prisma';
import { ExportTableConfigs } from './export-fields-configs';
import { handleMappingValue, mergeConfigs } from './field-handler';
import { AccountSettings, ExportFieldInput, FieldConfig } from './types';
import Formatter from '@/lib/Formatter';
import { Roles } from '@/types';
import { container } from '@/ioc';
import { SettingsService } from '@/services/settings';
import { RECONCILIATION_VERSION } from '@/constants/reconciliation-data';

export interface ExportStrategy {
  getExportData(input: ExportFieldInput);
}

export class PoliciesDataExportStrategy implements ExportStrategy {
  async getExportData(input: ExportFieldInput) {
    const {
      data,
      roleId,
      accountId,
      templateType,
      isExportRelationship,
      uid,
      contactStrId,
    } = input;
    let exportFields: FieldConfig[] = [];
    const settingsService = container.get(SettingsService);

    const fields = await prisma.fields.findMany({
      where: {
        model: 'reports',
        state: 'active',
      },
      select: {
        label: true,
        key: true,
      },
    });

    const roleToUse: number = parseInt(roleId);

    const accountSettings: AccountSettings =
      await settingsService.getSettingsByContact({
        uid,
        accountId,
        roleId: roleToUse,
        contactStrId,
      });

    const fieldConfigMap = new Map();

    const filterRelationshipFields = ExportTableConfigs.report_data.filter(
      (field) => {
        fieldConfigMap.set(field.field, field);
        return field.isRelationalField;
      }
    );

    if (
      Array.isArray(accountSettings?.pages_settings?.policies?.fields) &&
      accountSettings.pages_settings?.policies?.fields.length > 0
    ) {
      const reportDataFields =
        accountSettings.pages_settings?.policies?.fields.map((key) => {
          const field = fields.find((field) => field.key === key);
          return field
            ? {
                field: key,
                label: field.label,
                isRelationalField: false,
                ...fieldConfigMap.get(key),
              }
            : null;
        });
      exportFields = reportDataFields;
    } else {
      exportFields = fields.map((field) => ({
        field: field.key,
        label: field.label,
        isRelationalField: false,
        ...fieldConfigMap.get(field.key),
      }));
    }

    const mergedConfigs = mergeConfigs(
      exportFields.filter(Boolean),
      filterRelationshipFields
    );

    return handleMappingValue(
      data,
      mergedConfigs,
      templateType,
      isExportRelationship
    );
  }
}

export class CommissionsDataExportStrategy implements ExportStrategy {
  async getExportData(input: ExportFieldInput): Promise<any[]> {
    const {
      data,
      roleId,
      accountId,
      templateType,
      isExportRelationship,
      uid,
      contactStrId,
    } = input;
    let exportFields: FieldConfig[] = [];
    const settingsService = container.get(SettingsService);

    const fields = await prisma.fields.findMany({
      where: {
        model: 'statements',
        state: 'active',
      },
      select: {
        label: true,
        key: true,
      },
    });

    const roleToUse: number = parseInt(roleId);

    const accountSettings: AccountSettings =
      await settingsService.getSettingsByContact({
        uid,
        accountId,
        roleId: roleToUse,
        contactStrId,
      });
    const fieldConfigMap = new Map();

    const filterRelationshipFields = ExportTableConfigs.statement_data.filter(
      (field) => {
        fieldConfigMap.set(field.field, field);
        return field.isRelationalField;
      }
    );

    if (
      Array.isArray(accountSettings?.pages_settings?.commissions?.fields) &&
      accountSettings.pages_settings?.commissions?.fields.length > 0
    ) {
      const statementDataFields =
        accountSettings.pages_settings?.commissions?.fields.map((key) => {
          const field = fields.find((field) => field.key === key);
          return field
            ? {
                field: key,
                label: field.label,
                isRelationalField: false,
                ...fieldConfigMap.get(key),
              }
            : null;
        });
      exportFields = statementDataFields;
    } else {
      exportFields = fields.map((field) => ({
        field: field.key,
        label: field.label,
        isRelationalField: false,
        ...fieldConfigMap.get(field.key),
      }));
    }

    const mergedConfigs = mergeConfigs(
      exportFields.filter(Boolean),
      filterRelationshipFields
    );

    const agentCommissionsSettings =
      await settingsService.getAgentCommissionsDownlineDataAccess(accountId);
    const agentCommissionsDownlineData =
      agentCommissionsSettings?.agentCommissionsDirectDownlineDataAccess ||
      agentCommissionsSettings?.agentCommissionsExtendedDownlineDataAccess
        ? true
        : false;

    return handleMappingValue(
      data,
      mergedConfigs,
      templateType,
      isExportRelationship,
      agentCommissionsDownlineData
    );
  }
}

export class ReconciliationDataExportStrategy implements ExportStrategy {
  async getExportData(input: ExportFieldInput): Promise<any[]> {
    const {
      data,
      roleId,
      accountId,
      templateType,
      isExportRelationship,
      uid,
      contactStrId,
      reconciliationVersion,
    } = input;
    let exportFields: FieldConfig[] = [];
    const settingsService = container.get(SettingsService);
    const roleToUse: number = parseInt(roleId);

    const accountSettings: AccountSettings =
      await settingsService.getSettingsByContact({
        uid,
        accountId,
        roleId: roleToUse,
        contactStrId,
      });

    if (
      Array.isArray(accountSettings?.pages_settings?.reconciliation?.fields) &&
      accountSettings.pages_settings?.reconciliation?.fields.length > 0
    ) {
      const filteredDataFields = ExportTableConfigs.reconciliation_data.filter(
        (config) =>
          (
            accountSettings.pages_settings?.reconciliation?.fields as string[]
          ).includes(config.field) || config.isRelationalField
      );

      exportFields = filteredDataFields;

      if (reconciliationVersion !== RECONCILIATION_VERSION.V2) {
        // Remove v2 only fields
        exportFields = exportFields.filter(
          (config) => !RECONCILIATION_V2_ONLY_FIELDS.includes(config.field)
        );
      }
    }

    if (+roleId === Roles.ACCOUNT_ADMIN) {
      exportFields.push({
        field: 'commission_amount_monthly',
        label: dayjs().format('MMM YYYY'),
        isRelationalField: false,
        handler: (value) => {
          try {
            let result = 0;
            Object.entries(value).forEach(([date, commission]) => {
              if (dayjs(date).isSame(dayjs(), 'month')) {
                result +=
                  (commission as { commission_amount_monthly: number })
                    ?.commission_amount_monthly ?? 0;
              }
            });
            if (result) {
              return Formatter.currency(result);
            }
            return '';
          } catch {
            return '';
          }
        },
      });
    }

    return handleMappingValue(
      data,
      exportFields,
      templateType,
      isExportRelationship
    );
  }
}

export class BaseExportStrategy implements ExportStrategy {
  async getExportData(input: ExportFieldInput): Promise<any[]> {
    const { data, table, templateType, isExportRelationship } = input;
    return handleMappingValue(
      data,
      ExportTableConfigs[table],
      templateType,
      isExportRelationship
    );
  }
}
