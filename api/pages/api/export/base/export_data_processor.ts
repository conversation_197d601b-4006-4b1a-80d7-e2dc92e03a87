import {
  FieldConfig,
  TextFormatterArgs,
} from 'common/field-config/shared/types/field';
import { Roles } from 'common/field-config/shared/types/roles';

import { container } from '@/ioc';
import { SettingsService } from '@/services/settings';
import { AccountSettings } from './types';

export const mapHeaderFromFieldConfigs = (
  fields: Record<string, FieldConfig>,
  isExportRelationship: string
) => {
  return Object.entries(fields)
    .filter(([_key, value]) => {
      if (isExportRelationship !== 'true') {
        return value.enabled && !value.isRelationalField;
      }
      return value.enabled;
    })
    .map(([key, value]) => {
      return {
        field: key,
        label: value.label,
        handler: value.textFormatter,
      };
    });
};

export const formatExportData = (
  data: unknown[],
  headerConfig: {
    field: string;
    label: string;
    handler: (
      value: unknown,
      row: unknown,
      args?: TextFormatterArgs
    ) => string | number;
  }[],
  args?: TextFormatterArgs
) => {
  return data.map((row) => {
    return Object.fromEntries(
      headerConfig.map((header) => {
        if (!header.handler) return [header.label, row[header.field] ?? ''];
        return [header.label, header.handler(row[header.field], row, args)];
      })
    );
  });
};

export const applyAccountSettings = async ({
  fields,
  roleId,
  accountId,
  uid,
  producerView,
  page,
}: {
  fields: Record<string, FieldConfig>;
  roleId: string;
  accountId: string;
  uid: string;
  producerView: boolean;
  page: string;
}) => {
  const settingsService = container.get<SettingsService>(SettingsService);
  let roleToUse: number = parseInt(roleId);
  if (producerView) {
    roleToUse = Roles.PRODUCER;
  }

  const accountSettings: AccountSettings =
    await settingsService.getSettingsByContact({
      uid,
      accountId,
      roleId: roleToUse,
    });

  const pageSettingFields = accountSettings?.pages_settings?.[page]?.fields;

  if (Array.isArray(pageSettingFields) && pageSettingFields.length > 0) {
    const newFields = (pageSettingFields ?? []).reduce((acc, cur) => {
      acc[cur] = {
        ...fields[cur],
      };

      Object.entries(fields).forEach(([key, value]) => {
        if (value.ref === cur) {
          acc[key] = {
            ...value,
          };
        }
      });

      return acc;
    }, {});
    return newFields;
  }
};
