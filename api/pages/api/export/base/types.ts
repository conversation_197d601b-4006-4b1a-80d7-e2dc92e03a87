export interface Fields {
  fields: string[];
}

export interface PageSettings {
  commissions?: Fields;
  policies?: Fields;
  reconciliation?: Fields;
}

export interface AccountSettings {
  pages_settings?: PageSettings;
}

export interface FieldConfig {
  field: string; // Field key (e.g., 'first_name')
  label: string; // Display label (e.g., 'First name')
  isRelationalField: boolean; // Whether the field is a relational field
  handler?: HandlerField;
  relationalField?: string;
}

export type HandlerField =
  | ((value: any, item: any) => string | number | Promise<string | number>)
  | undefined;

export interface ExportFieldInput {
  data: any[];
  table: string;
  roleId?: string;
  accountId?: string;
  templateType?: string;
  isExportRelationship?: boolean;
  uid?: string;
  contactStrId?: string;
  reconciliationVersion?: string;
}
