import CommonFormatter from 'common/Formatter';
import { capitalize } from 'lodash-es';

import Formatter from '@/lib/Formatter';
import DataTransformation from '@/lib/helpers/DataTransformation';
import { formatTextUnderscoreToTitle } from '@/lib/helpers/formatTextUnderscoreToTitle';
import { FieldConfig } from './types';
import { accountingTransactionDetailsHandler } from './field-handler';
import { formatGridLevels } from './formatter';
import {
  agentCommissionLogHandler,
  agentCompensationHandler,
  agentCompensationLogHandler,
  agentPayoutStatusHandler,
} from './handlers/statementData/statementDataHandlers';
import {
  balanceHand<PERSON>,
  contactsHandler,
  contactsSplitHandler,
} from './handlers/reconciliationData/reconciliationDataHendlers';
import {
  compProfileSetsHandler,
  compProfilesHandler,
  displayLength,
  downlinesHandler,
  extractIds,
  savedReportsHandler,
  uplines<PERSON><PERSON><PERSON>,
  userContactHandler,
} from './handlers/contacts/contactHandlers';

export const ExportTableConfigs: { [key: string]: FieldConfig[] } = {
  agent_production: [
    { field: 'agent_name', label: 'Agent', isRelationalField: false },
    { field: 'agent_id', label: 'Agent ID', isRelationalField: true }, // Relational field
    { field: 'policies_count', label: 'Policies', isRelationalField: false },
    {
      field: 'premiums',
      label: 'Premiums',
      isRelationalField: false,
      handler: Formatter.currency,
    },
    {
      field: 'commissions',
      label: 'Commissions',
      isRelationalField: false,
      handler: Formatter.currency,
    },
  ],
  contacts: [
    { field: 'first_name', label: 'First name', isRelationalField: false },
    { field: 'last_name', label: 'Last name', isRelationalField: false },
    { field: 'nickname', label: 'Nickname', isRelationalField: false },
    { field: 'email', label: 'Email', isRelationalField: false },
    { field: 'phone', label: 'Phone', isRelationalField: false },
    { field: 'agent_code', label: 'Agent code', isRelationalField: false },
    { field: 'bank_info', label: 'Bank info', isRelationalField: false },
    { field: 'contact_group', label: 'Agent group', isRelationalField: false },
    { field: 'company_name', label: 'Company', isRelationalField: false },
    { field: 'type', label: 'Type', isRelationalField: false },
    { field: 'status', label: 'Status', isRelationalField: false },
    {
      field: 'payable_status',
      label: 'Payable status',
      isRelationalField: false,
      handler: capitalize,
    },
    {
      field: 'contact_level',
      label: 'Carrier grid levels',
      isRelationalField: false,
      handler: formatGridLevels,
    },
    {
      field: 'contact_level',
      label: 'Carrier grid levels IDs',
      isRelationalField: true,
      handler: extractIds,
    },
    {
      field: 'agency_contact_levels',
      label: 'Agency grid levels',
      isRelationalField: false,
      handler: formatGridLevels,
    },
    {
      field: 'agency_contact_levels',
      label: 'Agency grid levels IDs',
      isRelationalField: true,
      handler: extractIds,
    },
    {
      field: 'parent_relationships',
      label: 'Uplines',
      isRelationalField: false,
      handler: uplinesHandler,
    },
    {
      field: 'parent_relationships',
      label: 'Uplines IDs',
      isRelationalField: true,
      handler: extractIds,
    },
    {
      field: 'child_relationships',
      label: 'Downlines',
      isRelationalField: false,
      handler: downlinesHandler,
    },
    {
      field: 'child_relationships',
      label: 'Downlines IDs',
      isRelationalField: true,
      handler: extractIds,
    },
    {
      field: 'contacts_agent_commission_schedule_profiles_sets',
      label: 'Comp profile sets',
      isRelationalField: false,
      handler: compProfileSetsHandler,
    },
    {
      field: 'contacts_agent_commission_schedule_profiles_sets',
      label: 'Comp profile sets IDs',
      isRelationalField: true,
      handler: extractIds,
    },
    {
      field: 'contacts_agent_commission_schedule_profiles',
      label: 'Custom comp profiles',
      isRelationalField: false,
      handler: compProfilesHandler,
    },
    {
      field: 'contacts_agent_commission_schedule_profiles',
      label: 'Custom comp profiles IDs',
      isRelationalField: true,
      handler: extractIds,
    },
    {
      field: 'contact_memos',
      label: 'Memos',
      isRelationalField: false,
      handler: displayLength,
    },
    {
      field: 'contact_referrals',
      label: 'Referrals',
      isRelationalField: false,
      handler: displayLength,
    },
    { field: 'notes', label: 'Notes', isRelationalField: false },
    {
      field: 'saved_reports',
      label: 'Saved reports',
      isRelationalField: false,
      handler: savedReportsHandler,
    },
    {
      field: 'saved_reports',
      label: 'Saved reports IDs',
      isRelationalField: true,
      handler: extractIds,
    },
    {
      field: 'user_contact',
      label: 'Fintary access',
      isRelationalField: false,
      handler: userContactHandler,
    },
    { field: 'str_id', label: 'Agent ID', isRelationalField: false },
    { field: 'sync_id', label: 'Sync ID', isRelationalField: false },
    {
      field: 'account_role_settings.custom_view_name',
      label: 'Custom view',
      isRelationalField: false,
    },
    {
      field: 'account_role_settings_id',
      label: 'Custom view ID',
      isRelationalField: true,
    },
    { field: 'customer_id', label: 'Customer ID', isRelationalField: true }, // Relational field
  ],
  company_products: [
    {
      field: 'company.company_name',
      label: 'Company',
      isRelationalField: false,
    },
    { field: 'company_id', label: 'Company ID', isRelationalField: true }, // Relational field
    { field: 'product_type', label: 'Product type', isRelationalField: false },
    { field: 'product_name', label: 'Product name', isRelationalField: false },
    { field: 'notes', label: 'Notes', isRelationalField: false },
  ],
  companies: [
    { field: 'company_name', label: 'Company name', isRelationalField: false },
    { field: 'email', label: 'Email', isRelationalField: false },
    { field: 'website', label: 'Website', isRelationalField: false },
    { field: 'phone', label: 'Phone', isRelationalField: false },
    { field: 'address', label: 'Address', isRelationalField: false },
    { field: 'notes', label: 'Notes', isRelationalField: false },
    { field: 'type', label: 'Type', isRelationalField: false },
  ],
  mappings: [
    { field: 'name', label: 'Name', isRelationalField: false },
    { field: 'type', label: 'Type', isRelationalField: false },
    { field: 'carrier', label: 'Carrier', isRelationalField: false },
    { field: 'carrier_id', label: 'Carrier ID', isRelationalField: true }, // Relational field
    {
      field: 'mapping',
      label: 'Mapping',
      isRelationalField: false,
      handler(value) {
        if (!value) return '';
        try {
          return JSON.stringify(value);
        } catch {
          return '';
        }
      },
    },
    { field: 'created_at', label: 'Created at', isRelationalField: false },
  ],
  processors: [
    { field: 'name', label: 'Name', isRelationalField: false },
    { field: 'type', label: 'Type', isRelationalField: false },
    { field: 'processor', label: 'Processor', isRelationalField: false },
    { field: 'created_at', label: 'Created at', isRelationalField: false },
    { field: 'updated_at', label: 'Updated at', isRelationalField: false },
    {
      field: 'companies_old.company_name',
      label: 'Company name',
      isRelationalField: false,
    },
    { field: 'company_id', label: 'Company ID', isRelationalField: true }, // Relational field
  ],
  report_data: [
    { field: 'agent_name', label: 'Agent name', isRelationalField: false },
    {
      field: 'writing_carrier_name',
      label: 'Master company',
      isRelationalField: false,
      relationalField: 'company_id',
    },
    {
      field: 'company_id',
      label: 'Company ID',
      isRelationalField: true,
    },
    {
      field: 'commissions_expected',
      label: 'Commission due',
      isRelationalField: false,
    },
    {
      field: 'customer_name',
      label: 'Customer name',
      isRelationalField: false,
    },
    { field: 'customer_id', label: 'Customer ID', isRelationalField: true }, // Relational field
    { field: 'agent_id', label: 'Agent ID', isRelationalField: true }, // Relational field
    {
      field: 'effective_date',
      label: 'Effective date',
      isRelationalField: false,
    },
    { field: 'policy_id', label: 'Policy number', isRelationalField: false },
    {
      field: 'policy_status',
      label: 'Status',
      isRelationalField: false,
    },
    {
      field: 'premium_amount',
      label: 'Annualized revenue',
      isRelationalField: false,
    },
    { field: 'product_type', label: 'Product type', isRelationalField: false },
    {
      field: 'product_name',
      label: 'Product name',
      isRelationalField: false,
      relationalField: 'company_product_id',
    },
    {
      field: 'company_product_id',
      label: 'Product ID',
      isRelationalField: true,
    }, // Relational field
    { field: 'issue_age', label: 'Issue age', isRelationalField: false },
    { field: 'internal_id', label: 'Internal ID', isRelationalField: false },
    { field: 'group_id', label: 'Group ID', isRelationalField: false },
    {
      field: 'transaction_type',
      label: 'Transaction type',
      isRelationalField: false,
    },
    {
      field: 'commissionable_premium_amount',
      label: 'Target premium',
      isRelationalField: false,
    },
    {
      field: 'excess_amount',
      label: 'Excess',
      isRelationalField: false,
    },
    { field: 'notes', label: 'Notes', isRelationalField: false },
    {
      field: 'accounting_transaction_details',
      label: 'Expected receivables',
      isRelationalField: false,
      handler: (value) => {
        return accountingTransactionDetailsHandler(value);
      },
    },
    {
      field: 'customer_paid_premium_amount',
      label: 'Customer paid premium amount',
      isRelationalField: false,
      handler: (value) => {
        return value;
      },
    },
  ],
  statement_data: [
    { field: 'policy_id', label: 'Policy number', isRelationalField: false },
    {
      field: 'customer_name',
      label: 'Customer name',
      isRelationalField: false,
    },
    { field: 'carrier_name', label: 'Paying entity', isRelationalField: false },
    {
      field: 'writing_carrier_name',
      label: 'Carrier/MGA',
      isRelationalField: false,
    },
    { field: 'premium_type', label: 'Premium type', isRelationalField: false },
    {
      field: 'effective_date',
      label: 'Effective date',
      isRelationalField: false,
    },
    { field: 'product_type', label: 'Product type', isRelationalField: false },
    { field: 'product_name', label: 'Product name', isRelationalField: false },
    { field: 'issue_age', label: 'Issue age', isRelationalField: false },
    {
      field: 'commission_amount',
      label: 'Commission amount',
      isRelationalField: false,
    },
    {
      field: 'advanced_commission_amount',
      label: 'Advanced amount',
      isRelationalField: false,
    },
    { field: 'payment_date', label: 'Payment date', isRelationalField: false },
    { field: 'agent_name', label: 'Agent name', isRelationalField: false },
    { field: 'contacts', label: 'Agents', isRelationalField: false },
    {
      field: 'agent_commissions',
      label: 'Agent commissions',
      isRelationalField: false,
    },
    {
      field: 'agent_commissions_log',
      label: 'Agent commissions log',
      isRelationalField: false,
      handler: agentCommissionLogHandler,
    },
    {
      field: 'comp_calc_log',
      label: 'Agent compensation log',
      isRelationalField: false,
      handler: agentCompensationLogHandler,
    },
    {
      field: 'comp_calc',
      label: 'Agent compensation',
      isRelationalField: false,
      handler: agentCompensationHandler,
    },
    {
      field: 'comp_calc_status',
      label: 'Agent payout status',
      isRelationalField: false,
      handler: agentPayoutStatusHandler,
    },
    {
      field: 'statement_number',
      label: 'Statement number',
      isRelationalField: false,
    },
    {
      field: 'transaction_type',
      label: 'Transaction type',
      isRelationalField: false,
    },
    {
      field: 'document_id',
      label: 'Document ID',
      isRelationalField: true,
      handler: (value) => {
        return value;
      },
    },
    {
      field: 'document_id',
      label: 'Document',
      isRelationalField: false,
      relationalField: 'document_id',
    },
    {
      field: 'contacts',
      label: 'Agent IDs',
      isRelationalField: true,
      handler: (value) => {
        if (Array.isArray(value)) return value.join(', ');
        return value;
      },
    },
    {
      field: 'contacts',
      label: 'Agents',
      isRelationalField: false,
      relationalField: 'contacts',
    },
    {
      field: 'premium_amount',
      label: 'Premium amount',
      isRelationalField: false,
    },
    {
      field: 'commission_rate',
      label: 'Commission rate',
      isRelationalField: false,
    },
    { field: 'fees', label: 'Fees', isRelationalField: false },
    { field: 'group_id', label: 'Group number', isRelationalField: false },
    { field: 'period_date', label: 'Period date', isRelationalField: false },
    {
      field: 'compensation_type',
      label: 'Compensation type',
      isRelationalField: false,
    },
    {
      field: 'commissionable_premium_amount',
      label: 'Commissionable premium amount',
      isRelationalField: false,
    },
    { field: 'notes', label: 'Notes', isRelationalField: false },
    {
      field: 'customer_paid_premium_amount',
      label: 'Customer paid premium amount',
      isRelationalField: false,
    },
    {
      field: 'agent_payout_rate',
      label: 'Agent payout rate',
      isRelationalField: false,
    },
    {
      field: 'accounting_transaction_details',
      label: 'Expected receivables',
      isRelationalField: false,
      handler: (value, item) => {
        const accountingTransactionDetails =
          item?.report?.accounting_transaction_details || value;
        return accountingTransactionDetailsHandler(
          accountingTransactionDetails
        );
      },
    },
  ],
  extractions: [
    { field: 'created_at', label: 'Created at', isRelationalField: false },
    { field: 'method', label: 'Method', isRelationalField: false },
    { field: 'str_id', label: 'Document ID', isRelationalField: false },
    { field: 'result', label: 'Result', isRelationalField: false },
    { field: 'output', label: 'Extracted data', isRelationalField: false },
  ],
  reconciliation_data: [
    { field: 'policy_id', label: 'Policy number', isRelationalField: false },
    {
      field: 'writing_carrier_name',
      label: 'Carrier/MGA',
      isRelationalField: false,
    },
    { field: 'carrier_name', label: 'Paying entity', isRelationalField: false },
    {
      field: 'customer_name',
      label: 'Customer name',
      isRelationalField: false,
    },
    { field: 'product_type', label: 'Product type', isRelationalField: false },
    {
      field: 'product_sub_type',
      label: 'Product sub type',
      isRelationalField: false,
    },
    { field: 'product_name', label: 'Product name', isRelationalField: false },
    {
      field: 'effective_date',
      label: 'Effective date',
      isRelationalField: false,
    },
    {
      field: 'processing_date',
      label: 'Processing date',
      isRelationalField: false,
    },
    {
      field: 'premium_amount',
      label: 'Premium amount',
      isRelationalField: false,
    },
    {
      field: 'commissionable_premium_amount',
      label: 'Target premium',
      isRelationalField: false,
    },
    {
      field: 'commissions_expected',
      label: 'Commission due',
      isRelationalField: false,
    },
    {
      field: 'cancellation_date',
      label: 'Cancellation date',
      isRelationalField: false,
    },
    {
      field: 'reinstatement_date',
      label: 'Reinstatement date',
      isRelationalField: false,
    },
    { field: 'agent_name', label: 'Agent name', isRelationalField: false },
    {
      field: 'contacts',
      label: 'Agents',
      isRelationalField: false,
      handler: contactsHandler,
    },
    {
      field: 'contacts',
      label: 'Agent IDs',
      isRelationalField: true,
      handler: (value) => {
        if (Array.isArray(value)) return value.join(', ');
        return value;
      },
    },
    { field: 'amount_paid', label: 'Amount paid', isRelationalField: false },
    {
      field: 'balance',
      label: 'Balance',
      isRelationalField: false,
      handler: balanceHandler,
    },
    { field: 'issue_age', label: 'Issue age', isRelationalField: false },
    { field: 'internal_id', label: 'Internal ID', isRelationalField: false },
    { field: 'group_id', label: 'Group ID', isRelationalField: false },
    {
      field: 'policy_status',
      label: 'Policy status',
      isRelationalField: false,
    },
    { field: 'notes', label: 'Notes', isRelationalField: false },
    {
      field: 'report_data',
      label: 'Agent split',
      isRelationalField: false,
      handler: contactsSplitHandler,
    },
    {
      field: 'customer_paid_premium_amount',
      label: 'Customer paid premium amount',
      isRelationalField: false,
    },
    { field: 'reconciled', label: 'Reconciled', isRelationalField: false },
    {
      field: 'commission_amount_monthly',
      label: 'Commission amount monthly',
      isRelationalField: false,
      handler: (value) => {
        try {
          const result = Object.keys(value).reduce((acc, date) => {
            acc[date] = value[date].commission_amount_monthly;
            return acc;
          }, {});
          return JSON.stringify(result);
        } catch {
          return '';
        }
      },
    },
  ],
  company_product_options: [
    {
      field: 'product.product_name',
      label: 'Product',
      isRelationalField: false,
    },
    {
      field: 'product_id',
      label: 'Product ID',
      isRelationalField: true,
    },
    {
      field: 'name',
      label: 'Option',
      isRelationalField: false,
    },
    {
      field: 'notes',
      label: 'Notes',
      isRelationalField: false,
    },
  ],
  contact_groups: [
    {
      field: 'id',
      label: 'ID',
      isRelationalField: false,
    },
    {
      field: 'name',
      label: 'Name',
      isRelationalField: false,
    },
    {
      field: 'notes',
      label: 'Notes',
      isRelationalField: false,
    },
    {
      field: 'contacts',
      label: 'Agents',
      isRelationalField: false,
      handler: (value) => {
        if (Array.isArray(value)) {
          const formattedContacts = value.map((contactData) => {
            return `${contactData.first_name} ${contactData.last_name}`;
          });
          return formattedContacts.join(', ');
        }
        return '';
      },
    },
    {
      field: 'contacts',
      label: 'Agent IDs',
      isRelationalField: true,
      handler: (value) => {
        if (Array.isArray(value)) {
          return value.map((contact) => contact.id).join(', ');
        }
        return '';
      },
    },
  ],
  calculation_methods: [
    {
      field: 'name',
      label: 'Name',
      isRelationalField: false,
    },
    {
      field: 'description',
      label: 'Description',
      isRelationalField: false,
    },
    {
      field: 'formula',
      label: 'Formula',
      isRelationalField: false,
    },
  ],
  comp_grids: [
    {
      field: 'company.company_name',
      label: 'Carrier',
      isRelationalField: false,
    },
    {
      field: 'company_id',
      label: 'Carrier ID',
      isRelationalField: true,
    },
    {
      field: 'name',
      label: 'Name',
      isRelationalField: false,
    },
    {
      field: 'rate_fields',
      label: 'Rate fields',
      isRelationalField: false,
      handler: (value) => {
        if (Array.isArray(value)) {
          return value
            .map((rate_field) => formatTextUnderscoreToTitle(rate_field))
            .join(', ');
        }
        return '';
      },
    },
    {
      field: 'notes',
      label: 'Notes',
      isRelationalField: false,
    },
    {
      field: 'created_at',
      label: 'Created',
      isRelationalField: false,
      handler: Formatter.dateStringToLocaleString,
    },
    {
      field: 'updated_at',
      label: 'Last updated',
      isRelationalField: false,
      handler: Formatter.dateStringToLocaleString,
    },
  ],
  comp_grids_products: [
    {
      field: 'comp_grid.name',
      label: 'Comp grid',
      isRelationalField: false,
    },
    {
      field: 'comp_grid_id',
      label: 'Comp grid ID',
      isRelationalField: true,
    },
    {
      field: 'name',
      label: 'Name',
      isRelationalField: false,
    },
    {
      field: 'type',
      label: 'Type',
      isRelationalField: false,
    },
    {
      field: 'notes',
      label: 'Notes',
      isRelationalField: false,
    },
    {
      field: 'company_products_name',
      label: 'Products',
      isRelationalField: false,
    },
    {
      field: 'company_product_ids',
      label: 'Product IDs',
      isRelationalField: false,
    },
    {
      field: 'created_at',
      label: 'Created',
      isRelationalField: false,
      handler: Formatter.dateStringToLocaleString,
    },
    {
      field: 'updated_at',
      label: 'Last updated',
      isRelationalField: false,
      handler: Formatter.dateStringToLocaleString,
    },
  ],
  comp_grid_levels: [
    {
      field: 'comp_grid.name',
      label: 'Comp grid',
      isRelationalField: false,
    },
    {
      field: 'comp_grid_id',
      label: 'Comp grid ID',
      isRelationalField: true,
    },
    {
      field: 'name',
      label: 'Name',
      isRelationalField: false,
    },
    {
      field: 'effective_date',
      label: 'Effective date',
      isRelationalField: false,
    },
    {
      field: 'type',
      label: 'Agency level',
      isRelationalField: false,
    },
    {
      field: 'notes',
      label: 'Notes',
      isRelationalField: false,
    },
    {
      field: 'created_at',
      label: 'Created',
      isRelationalField: false,
      handler: Formatter.dateStringToLocaleString,
    },
    {
      field: 'updated_at',
      label: 'Last updated',
      isRelationalField: false,
      handler: Formatter.dateStringToLocaleString,
    },
  ],
  comp_grid_criteria: [
    {
      field: 'company.company_name',
      label: 'Carrier',
      isRelationalField: false,
    },
    {
      field: 'company_id',
      label: 'Carrier ID',
      isRelationalField: true,
    },
    {
      field: 'comp_grid.name',
      label: 'Comp grid',
      isRelationalField: false,
    },
    {
      field: 'comp_grid_id',
      label: 'Comp grid ID',
      isRelationalField: true,
    },
    {
      field: 'product.name',
      label: 'Grid product',
      isRelationalField: false,
    },
    {
      field: 'grid_product_id',
      label: 'Grid product ID',
      isRelationalField: true,
    },
    {
      field: 'compensation_type',
      label: 'Compensation type',
      isRelationalField: false,
    },
    {
      field: 'transaction_type',
      label: 'Transaction type',
      isRelationalField: false,
    },
    {
      field: 'payment_mode',
      label: 'Payment mode',
      isRelationalField: false,
    },
    {
      field: 'issue_age_start',
      label: 'Issue age start',
      isRelationalField: false,
    },
    {
      field: 'issue_age_end',
      label: 'Issue age end',
      isRelationalField: false,
    },
    {
      field: 'premium_max',
      label: 'Premium max',
      isRelationalField: false,
    },
    {
      field: 'policy_year_start',
      label: 'Policy year start',
      isRelationalField: false,
    },
    {
      field: 'policy_year_end',
      label: 'Policy year end',
      isRelationalField: false,
    },
    {
      field: 'filter_date_field',
      label: 'Filter date field',
      isRelationalField: false,
    },
    {
      field: 'created_at',
      label: 'Created',
      isRelationalField: false,
      handler: Formatter.dateStringToLocaleString,
    },
    {
      field: 'updated_at',
      label: 'Last updated',
      isRelationalField: false,
      handler: Formatter.dateStringToLocaleString,
    },
  ],
  comp_grid_rates: [
    {
      field: 'comp_grid.name',
      label: 'Comp grid',
      isRelationalField: false,
    },
    {
      field: 'comp_grid_id',
      label: 'Comp grid ID',
      isRelationalField: true,
    },
    {
      field: 'date_ranges',
      label: 'Date ranges',
      isRelationalField: false,
      handler: (val) => {
        if (!val) return '';
        if (Array.isArray(val)) {
          return val
            .map((item) => {
              return item.name
                ? `${item.name} : ${CommonFormatter.dateRange(item.start_date, item.end_date)}`
                : `${CommonFormatter.dateRange(item.start_date, item.end_date)}`;
            })
            .join(', ');
        }
      },
    },
    {
      field: 'comp_grid_criteria',
      label: 'Comp grid criteria',
      isRelationalField: false,
      handler: Formatter.criteriaFormatter,
    },
    {
      field: 'comp_grid_criterion_id',
      label: 'Comp grid criterion ID',
      isRelationalField: true,
    },
    {
      field: 'comp_grid_level',
      label: 'Comp grid level',
      isRelationalField: false,
      handler: (o) => `Comp grid: ${o?.comp_grid?.name} • Level: ${o?.name}`,
    },
    {
      field: 'comp_grid_level_id',
      label: 'Comp grid level ID',
      isRelationalField: true,
    },
    {
      field: 'carrier_rate',
      label: 'Carrier rate',
      isRelationalField: false,
      handler: DataTransformation.formatPercentage,
    },
    {
      field: 'house_rate',
      label: 'House rate',
      isRelationalField: false,
      handler: DataTransformation.formatPercentage,
    },
    {
      field: 'rate',
      label: 'Rate',
      isRelationalField: false,
      handler: DataTransformation.formatPercentage,
    },
    {
      field: 'created_at',
      label: 'Created',
      isRelationalField: false,
      handler: Formatter.dateStringToLocaleString,
    },
    {
      field: 'updated_at',
      label: 'Last updated',
      isRelationalField: false,
      handler: Formatter.dateStringToLocaleString,
    },
  ],
  reconciler_flows: [
    {
      field: 'name',
      label: 'Name',
      isRelationalField: false,
    },
    {
      field: 'flowData',
      label: 'Flow',
      isRelationalField: false,
    },
    {
      field: 'flowDataIds',
      label: 'Flow IDs',
      isRelationalField: true,
    },
    {
      field: 'access',
      label: 'Access',
      isRelationalField: false,
    },
    {
      field: 'state',
      label: 'State',
      isRelationalField: false,
    },
    {
      field: 'account_default',
      label: 'Default reconciler',
      isRelationalField: false,
    },
    {
      field: 'updated_at',
      label: 'Last updated',
      isRelationalField: false,
      handler: Formatter.dateStringToLocaleString,
    },
  ],
  data_processing: [
    {
      field: 'created_at',
      label: 'Date',
      isRelationalField: false,
    },
    {
      field: 'duration',
      label: 'Duration',
      isRelationalField: false,
    },
    {
      field: 'notes',
      label: 'Notes',
      isRelationalField: false,
    },
    {
      field: 'params',
      label: 'Params',
      isRelationalField: false,
    },
    {
      field: 'stats',
      label: 'Stats',
      isRelationalField: false,
      handler: (value) => {
        if (!value) return '';
        try {
          return JSON.stringify(value);
        } catch {
          return '';
        }
      },
    },
    {
      field: 'status',
      label: 'Status',
      isRelationalField: false,
    },
    {
      field: 'type',
      label: 'Type',
      isRelationalField: false,
    },
    {
      field: 'user',
      label: 'User',
      isRelationalField: false,
      handler: (user, item) =>
        `${Formatter.contact(item?.user)}${item?.proxy_user ? ` (via ${Formatter.contact(item?.proxy_user)})` : ''}`,
    },
  ],
} as const;
