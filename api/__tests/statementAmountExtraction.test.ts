import { describe, it, expect } from 'vitest';
import extractAmountFromFilename from 'common/tools/statementAmountExtraction';

const testCases = [
  {
    input: 'statement_1234.56.pdf',
    expected: 1234.56,
  },
  {
    input: 'statement_1234_56.pdf',
    expected: null,
  },
  {
    input: 'statement_1234-56.pdf',
    expected: null,
  },
  {
    input: 'statement_12.34.pdf',
    expected: 12.34,
  },
  {
    input: 'statement_1.23.pdf',
    expected: null,
  },
  {
    input: 'statement_1,234.56.pdf',
    expected: 1234.56,
  },
  {
    input: './489.95 - 3_20_2025.pdf',
    expected: 489.95,
  },
  {
    input: './3,280.06 - 3_20_2025.pdf',
    expected: 3280.06,
  },
  {
    input: './571.33 - 3_20_2025.pdf',
    expected: 571.33,
  },
  {
    input: './2,013.31 - 3_20_2025.pdf',
    expected: 2013.31,
  },
  {
    input: './7,400.02 - 3_20_2025.pdf',
    expected: 7400.02,
  },
  {
    input: './4,169.39 - 3_20_2025.pdf',
    expected: 4169.39,
  },
  {
    input: './-1,588.69 3_25.pdf',
    expected: 1588.69,
  },
];

describe('extractAmountFromFilename', () => {
  testCases.forEach(({ input, expected }) => {
    it(`should extract ${expected} from ${input}`, () => {
      const result = extractAmountFromFilename(input);
      expect(result).toEqual(expected);
    });
  });
});
