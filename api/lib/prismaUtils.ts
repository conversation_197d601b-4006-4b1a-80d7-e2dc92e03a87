import { Prisma, PrismaClient } from '@prisma/client/extension';
import { Prisma as PrismaBase } from '@prisma/client';
import { DEFAULT_FILTER } from 'common/constants';

import { generateDateClauses } from './helpers/generateDateClauses';

export const whereWithBlank = (
  field: string,
  value: any,
  useHas: boolean = false
) => {
  if (!value) return undefined;

  if (value === DEFAULT_FILTER.BLANK_OPTION) {
    if (!useHas) {
      return [{ [field]: '' }, { [field]: null }];
    } else {
      return [
        {
          [field]: {
            has: null,
          },
        },
      ];
    }
  }
  let result = [];
  if (Array.isArray(value) && value.includes(DEFAULT_FILTER.BLANK_OPTION)) {
    value = value.filter((val) => val !== DEFAULT_FILTER.BLANK_OPTION);
    if (!useHas) {
      result = [{ [field]: '' }, { [field]: null }];
    } else {
      result = [
        {
          [field]: {
            has: null,
          },
        },
        { [field]: null },
      ];
    }
  }
  if (useHas) {
    if (Array.isArray(value)) {
      value.map((val) => {
        result = result.concat([
          {
            [field]: {
              has: val,
            },
          },
        ]);
      });
    } else {
      result = result.concat([
        {
          [field]: {
            has: value,
          },
        },
      ]);
    }
  } else {
    result = result.concat([
      {
        [field]: {
          in: Array.isArray(value) ? value : [value],
        },
      },
    ]);
  }

  return result;
};

export const generateReportWhere = ({
  account_id,
  userData,
  startDate,
  endDate,
  policy_status,
  product_type,
  compensation_type,
  agentList,
  includeBlankDate = true,
  unSelectedAgentList = null,
}) => {
  const whereBase = generateWhereBase(account_id, userData);
  const policyStatusWhere = generatePolicyStatusWhere(policy_status);
  const productTypeWhere = generateProductTypeWhere(product_type);
  const compensationTypeWhere = whereWithBlank(
    'compensation_type',
    compensation_type
  );
  const andClauses: any = [];

  if (includeBlankDate) {
    andClauses.push({
      OR: [
        {
          effective_date: timeRangeSearchCriteria(startDate, endDate),
        },
        { effective_date: null },
      ],
    });
  } else {
    andClauses.push({
      effective_date: timeRangeSearchCriteria(startDate, endDate),
    });
  }
  if (policyStatusWhere) {
    andClauses.push({ OR: policyStatusWhere });
  }
  if (productTypeWhere) {
    andClauses.push({ OR: productTypeWhere });
  }
  if (agentList) {
    andClauses.push({
      OR: agentList.map((agent) => ({ contacts: { has: agent.str_id } })),
    });
  } else if (unSelectedAgentList) {
    andClauses.push({
      NOT: {
        OR: unSelectedAgentList.map((agent) => ({
          contacts: { has: agent.str_id },
        })),
      },
    });
  }
  if (compensationTypeWhere) {
    andClauses.push({
      statement_data: { some: { OR: compensationTypeWhere } },
    });
  }
  return {
    ...whereBase,
    AND: andClauses,
  };
};

export const generateReportWhereWithoutDataFilter = ({
  account_id,
  userData,
  policy_status,
  product_type,
  compensation_type,
  agentList,
  unSelectedAgentList = null,
}) => {
  const whereBase = generateWhereBase(account_id, userData);
  const policyStatusWhere = generatePolicyStatusWhere(policy_status);
  const productTypeWhere = generateProductTypeWhere(product_type);
  const compensationTypeWhere = whereWithBlank(
    'compensation_type',
    compensation_type
  );
  const andClauses: any = [];

  if (policyStatusWhere) {
    andClauses.push({ OR: policyStatusWhere });
  }
  if (productTypeWhere) {
    andClauses.push({ OR: productTypeWhere });
  }
  if (agentList) {
    andClauses.push({
      OR: agentList.map((agent) => ({ contacts: { has: agent.str_id } })),
    });
  } else if (unSelectedAgentList) {
    andClauses.push({
      NOT: {
        OR: unSelectedAgentList.map((agent) => ({
          contacts: { has: agent.str_id },
        })),
      },
    });
  }
  if (compensationTypeWhere) {
    andClauses.push({
      statement_data: { some: { OR: compensationTypeWhere } },
    });
  }
  return {
    ...whereBase,
    AND: andClauses,
  };
};

export const generateStatementWhere = ({
  account_id,
  userData,
  startDate,
  endDate,
  product_type,
  compensation_type,
  tag,
  agentList,
  includeBlankDate,
  unSelectedAgentList = null,
}): PrismaBase.statement_dataWhereInput => {
  const whereBase = generateWhereBase(account_id, userData);
  const compensationTypeWhere = whereWithBlank(
    'compensation_type',
    compensation_type
  );
  const productTypeWhere = generateProductTypeWhere(product_type);
  const andClauses: PrismaBase.statement_dataWhereInput[] = [];

  andClauses.push(
    generateDateClauses('processing_date', startDate, endDate, includeBlankDate)
  );

  const tagWhere = whereWithBlank('tags', tag, true);

  if (tagWhere) {
    andClauses.push({ OR: tagWhere });
  }

  // Add OR clause for compensationTypeWhere if it is defined
  if (compensationTypeWhere) {
    andClauses.push({ OR: compensationTypeWhere });
  }

  // Add OR clause for productTypeWhere if it is defined
  if (productTypeWhere) {
    andClauses.push({ OR: productTypeWhere });
  }

  // Add OR clause for agentList if it is defined
  if (agentList) {
    andClauses.push({
      OR: agentList.map((agent) => ({
        report: { contacts: { has: agent.str_id } },
      })),
    });
  } else if (unSelectedAgentList) {
    andClauses.push({
      NOT: {
        OR: unSelectedAgentList.map((agent) => ({
          report: { contacts: { has: agent.str_id } },
        })),
      },
    });
  }

  // Build and return the final object
  return {
    ...whereBase,
    AND: andClauses,
  };
};

export const generateReportWhereCommissionProcessingDate = ({
  account_id,
  userData,
  startDate,
  endDate,
  product_type,
  compensation_type,
  agentList,
  includeBlankDate,
  unSelectedAgentList = null,
}) => {
  const whereBase = generateWhereBase(account_id, userData);
  const productTypeWhere = generateProductTypeWhere(product_type);
  const compensationTypeWhere = whereWithBlank(
    'compensation_type',
    compensation_type
  );

  const andClauses = [];

  // Add OR clause for productTypeWhere if it is defined
  if (productTypeWhere) {
    andClauses.push({ OR: productTypeWhere });
  }

  // Add OR clause for compensationTypeWhere if it is defined
  if (compensationTypeWhere) {
    andClauses.push({ OR: compensationTypeWhere });
  }

  if (agentList) {
    andClauses.push({
      OR: agentList.map((agent) => ({
        contacts: { has: agent.str_id },
      })),
    });
  } else if (unSelectedAgentList) {
    andClauses.push({
      NOT: {
        OR: unSelectedAgentList.map((agent) => ({
          contacts: { has: agent.str_id },
        })),
      },
    });
  }

  andClauses.push(
    generateDateClauses('processing_date', startDate, endDate, includeBlankDate)
  );

  // Build the final object
  return {
    ...whereBase,
    AND: [
      {
        statement_data: {
          some: {
            ...(andClauses.length > 0 && { AND: andClauses }),
          },
        },
      },
    ],
  };
};

export const generateContactWhere = ({
  account_id,
  agentList,
  unSelectedAgentList = null,
}: {
  account_id: string;
  agentList?: { str_id: string }[];
  unSelectedAgentList?: { str_id: string }[];
}): PrismaBase.contactsWhereInput => {
  const andClauses: PrismaBase.contactsWhereInput[] = [
    {
      account_id: account_id,
      state: 'active',
    },
  ];
  if (agentList?.length > 0) {
    andClauses.push({
      str_id: {
        in: agentList.map((agent) => agent.str_id),
      },
    });
  } else if (unSelectedAgentList) {
    andClauses.push({
      NOT: {
        str_id: {
          in: unSelectedAgentList.map((agent) => agent.str_id),
        },
      },
    });
  }
  return {
    AND: andClauses,
  };
};

export const generateAccountingTransactionsWhere = ({
  account_id,
  agentList,
  unSelectedAgentList = null,
}: {
  account_id: string;
  agentList?: { str_id: string }[];
  unSelectedAgentList?: { str_id: string }[];
}): PrismaBase.accounting_transactionsWhereInput => {
  const andClauses: PrismaBase.accounting_transactionsWhereInput[] = [
    {
      type: 'comp_report',
      account_id,
      state: 'active',
      status: {
        in: ['Approved', 'Paid'],
      },
    },
  ];
  if (agentList?.length > 0) {
    andClauses.push({
      contact: {
        str_id: {
          in: agentList.map((agent) => agent.str_id),
        },
      },
    });
  } else if (unSelectedAgentList) {
    andClauses.push({
      NOT: {
        contact: {
          str_id: {
            in: unSelectedAgentList.map((agent) => agent.str_id),
          },
        },
      },
    });
  }
  return {
    AND: andClauses,
  };
};

export const generateCustomersWhere = ({
  account_id,
  agentList,
  unSelectedAgentList = null,
}: {
  account_id: string;
  agentList?: { str_id: string }[];
  unSelectedAgentList?: { str_id: string }[];
}): PrismaBase.customersWhereInput => {
  const andClauses: PrismaBase.customersWhereInput[] = [
    {
      account_id,
      state: 'active',
    },
  ];
  if (agentList?.length > 0) {
    andClauses.push({
      OR: agentList.map((agent) => ({
        contacts: { some: { str_id: agent.str_id } },
      })),
    });
  } else if (unSelectedAgentList) {
    andClauses.push({
      NOT: {
        OR: unSelectedAgentList.map((agent) => ({
          contacts: { some: { str_id: agent.str_id } },
        })),
      },
    });
  }
  return {
    AND: andClauses,
  };
};

export const generateWhereBase = (account_id, userData) => {
  return userData?.length > 0
    ? [
        { account_id: account_id },
        { state: 'active' },
        {
          contacts: {
            has: userData.user_contact[0].str_id,
          },
        },
      ]
    : { account_id: account_id, state: 'active' };
};

export const generatePolicyStatusWhere = (policy_status) => {
  return whereWithBlank('policy_status', policy_status);
};

const generateProductTypeWhere = (product_type) => {
  return whereWithBlank('product_type', product_type);
};

export const createAgentListQuery = (contacts, contactIds) => {
  let result = undefined;
  if (contacts && contacts.length > 0) {
    result = contacts.map((contact) => ({ str_id: contact.str_id }));
  }
  if (!contactIds) return result;
  if (!Array.isArray(contactIds)) {
    contactIds = contactIds.split(',');
  }
  result = contactIds.map((contactId) => ({ str_id: contactId }));
  return result;
};

const timeRangeSearchCriteria = (startDate, endDate, _includeBlank = true) => {
  return {
    gte: startDate,
    lt: endDate,
  };
};
// Automaticlly passing the transaction client to the handler
export async function prismaTransactionHandler<T>(
  client: PrismaClient | Prisma.TransactionClient,
  handle: (prisma: PrismaClient | Prisma.TransactionClient) => Promise<T>,
  options: { maxWait?: number; timeout?: number } | { timeout: 6000000 } = {
    timeout: 30000,
  }
) {
  if (typeof client.$transaction !== 'undefined') {
    return await client.$transaction(handle, options);
  } else {
    return await handle(client);
  }
}

/**
 * Checks whether the input is a valid identifier (ID) for prisma.
 *
 * A valid ID is:
 * - A non-negative integer if it's a number
 * - A non-empty, non-whitespace-only string if it's a string
 *
 * @param input - The value to validate as an ID (can be of any type)
 * @returns `true` if the input is a valid ID, otherwise `false`
 */
const isValidId = (input: unknown): boolean => {
  if (typeof input === 'number') {
    return Number.isInteger(input) && input >= 0;
  }

  if (typeof input === 'string') {
    const trimmed = input.trim();

    if (trimmed === '') {
      return false;
    }

    return true;
  }

  return false;
};

export { isValidId };
