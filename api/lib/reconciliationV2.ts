import { numberOrDefault } from 'common/helpers';
import { createHash } from 'crypto';
import * as levenshtein from 'damerau-levenshtein';
import dayjs from 'dayjs';
import { distance } from 'fastest-levenshtein';
import { nanoid } from 'nanoid';
import 'reflect-metadata';
import { reconcilers, statement_data } from '@prisma/client';
import { isNill } from 'common/helpers/isNill';

import { container } from '@/ioc';
import { ReconciliationStatus } from '@/pages/api/reconciliation_data2/buildReconciliationQuery';
import { GroupDedupeService } from '@/services/data_processing/group-dedupe';
import { Tables } from '@/types';

// First add the necessary interfaces
interface BaseRecord {
  id: string | number;
  policy_id?: string;
  document_id?: string;
  contacts?: string[];
  contacts_split?: any;
  statement_data?: statement_data[];
}

export const findDupes = (
  data: BaseRecord[],
  config: {
    priority_field?: string;
    unique_empty?: string[];
    fields?: string[];
    diff_fields?: string[];
  },
  options?: {
    prioritySortFn?: (a: BaseRecord, b: BaseRecord) => number;
    table?: Tables;
  }
) => {
  const dupedData: { [key: string]: BaseRecord[] } = {};
  const recordsWithSameDocumentIds = new Map<string, Set<string>>();
  const priorityField = config?.priority_field ?? 'created_at';
  let sortFn =
    options?.prioritySortFn ??
    ((a: BaseRecord, b: BaseRecord) => b[priorityField] - a[priorityField]);

  if (!options?.prioritySortFn && priorityField === 'compensation_type') {
    sortFn = (a: BaseRecord, b: BaseRecord) => {
      const isACommission =
        typeof a[priorityField] === 'string' &&
        ['commissions', 'commission', 'override', 'overrides'].includes(
          a[priorityField].toLowerCase()
        );
      const isBCommission =
        typeof b[priorityField] === 'string' &&
        ['commissions', 'commission', 'override', 'overrides'].includes(
          b[priorityField].toLowerCase()
        );
      if (isACommission && !isBCommission) return -1;
      if (isBCommission && !isACommission) return 1;
      return 0;
    };
  }

  const groupDedupeService = container.get(GroupDedupeService);
  const uniqueEmptyKeys = config?.unique_empty ?? [];
  const fieldKeys =
    Array.isArray(config?.fields) && config.fields.length > 0
      ? config.fields
      : ['id'];

  data.forEach((datum) => {
    const recordKey = fieldKeys
      .map((field) =>
        isNill(datum?.[field]) && uniqueEmptyKeys.includes(field)
          ? `rnd-${nanoid()}`
          : typeof datum?.[field] === 'string'
            ? datum[field].trim()
            : datum[field]
      )
      .join('::');
    dupedData[recordKey] = [...(dupedData[recordKey] ?? []), datum];

    // TODO: Temporarily disable feature to not dedupe within same document
    //   We'll need this to be configurable by account, as some accounts don't want this.
    // if (datum.document_id) {
    //   const key = `${datum.document_id}::${recordKey}`;
    //   const item = recordsWithSameDocumentIds.get(key);
    //   if (item) {
    //     item.add(datum.id);
    //   } else {
    //     recordsWithSameDocumentIds.set(key, new Set([datum.id]));
    //   }
    // }
  });
  const idsWithSameDocs = Array.from(recordsWithSameDocumentIds.values())
    .filter((d) => d.size > 1)
    .reduce((a, ids) => [...a, ...ids.values()], []);
  let replaced: [string, BaseRecord[]][] = Object.entries(dupedData).filter(
    ([k, v]) => v.length > 1
  );
  // TODO: Not the most effective way of doing this...
  replaced = replaced.map(([k, v]) => {
    v.sort(sortFn);
    const diffFields = config.diff_fields ?? [];
    let newVals = v;
    diffFields.forEach((field) => {
      const seenVals: { [key: string]: boolean } = {};
      const newNewVals: BaseRecord[] = [];
      newVals.forEach((e) => {
        if (seenVals[e[field]] !== true) {
          newNewVals.push(e);
        }
        seenVals[e[field]] = true;
      });
      newVals = newNewVals;
    });
    return [k, newVals];
  });
  replaced = replaced.filter(([k, v]) => v.length > 1);
  if (options?.table === Tables.POLICY_DATA) {
    replaced = replaced.map((r) => [
      r[0],
      groupDedupeService.prioritizePolicy(r[1] as any),
    ]);
  }
  const toBeRemovedIds: (string | number)[] = [];
  const toBeRemovedData: BaseRecord[] = [];
  const relations = new Map<string | number, BaseRecord[]>();

  replaced.forEach(([k, v]) => {
    // Handle boolean and product_name
    v.forEach((e, i) => {
      if (i === 0) {
        relations.set(
          e.id,
          v
            .map((e) => ({
              id: e.id,
              contacts: e.contacts,
              contacts_split: e.contacts_split,
              statement_data: e.statement_data,
            }))
            .slice(1)
            .filter((e) => !idsWithSameDocs.includes(e.id))
        );
      } else if (idsWithSameDocs.includes(e.id)) {
        relations.set(e.id, []);
      } else {
        // Newest one is 1st
        toBeRemovedIds.push(e.id);
        toBeRemovedData.push(e);
      }
    });
  });

  // if diff field diff, can be keyed together, so boot into new
  // if diff field same, cannot be keyed together

  return { ids: toBeRemovedIds, data: toBeRemovedData, relations, replaced };
};

export const manualDedupe = <T extends { id: number }>(
  data: T[],
  masterId: number
) => {
  const groupedDataIds = data.map((r) => r.id).filter((id) => id !== masterId);
  const groupedData = data.filter((r) => r.id !== masterId);
  return {
    ids: groupedDataIds,
    data: groupedData,
    relations: new Map([[masterId, groupedData]]),
    replaced: data,
  };
};

const genKeyFn = (config) => {
  return (data) => {
    let key = '';
    key = config
      .map((fieldConfig) => {
        const curKey = applyTransformers(
          data[fieldConfig.field],
          fieldConfig.transformers,
          fieldConfig.params
        );
        return curKey;
      })
      .join('::');
    return key;
  };
};

export const getKeyMethods = (reconciler: reconcilers) => {
  let getStatementKey = (statement) => statement.policy_id;
  if (reconciler.method_type === 'key-config') {
    getStatementKey = genKeyFn(reconciler.key_config_statement);
  } else if (reconciler.method_type === 'key-custom') {
    getStatementKey = eval(reconciler.key_config_statement as string);
  }
  let getReportKey = (report) => report.policy_id;
  if (reconciler.method_type === 'key-config') {
    getReportKey = genKeyFn(reconciler.key_config_report);
  } else if (reconciler.method_type === 'key-custom') {
    getReportKey = eval(reconciler.key_config_report as string);
  }
  return { getStatementKey, getReportKey };
};

const applyTransformers = (
  input: string,
  transformers: string[] | string,
  params?: Record<string, any>
) => {
  let result = input;
  const _transformers = Array.isArray(transformers)
    ? transformers
    : [transformers];
  const standardizeTransformers = _transformers.filter((t) =>
    t.startsWith('standardize')
  );
  const otherTransformers = _transformers.filter(
    (t) => !t.startsWith('standardize')
  );
  standardizeTransformers.forEach((transformer: string) => {
    if (isNill(result)) return result;
    switch (transformer) {
      case 'standardizeProduct':
        try {
          const mappings = JSON.parse(
            params['standardizeProduct'].mapping
          ) as Record<string, string[]>;
          for (const [key, value] of Object.entries(mappings)) {
            if (value.includes(result)) {
              result = key;
              break;
            }
          }
        } catch (e) {
          console.error('Error parsing standardizeProduct mapping', e);
        }
        break;
      case 'standardizeProductType':
        try {
          const mappings = JSON.parse(
            params['standardizeProductType'].mapping
          ) as Record<string, string[]>;
          for (const [key, value] of Object.entries(mappings)) {
            if (value.includes(result)) {
              result = key;
              break;
            }
          }
        } catch (e) {
          console.error('Error parsing standardizeProduct mapping', e);
        }
        break;
    }
  });
  otherTransformers.forEach((transformer: string) => {
    if (isNill(result)) return result;
    switch (transformer) {
      case 'toLowerCase':
        result = result.toLowerCase();
        break;
      case 'toUpperCase':
        result = result.toUpperCase();
        break;
      case 'removeDashes':
        result = result.replace(/-/g, '');
        break;
      case 'removeSpaces':
        result = result.replace(/\s/g, '');
        break;
      case 'trimZeros':
        result = result.replace(/^0+/g, '');
        result = result.replace(/0+$/g, '');
        break;
      case 'removePeriods':
        result = result.replace(/\./g, '');
        break;
      case 'removeCommas':
        result = result.replace(/,/g, '');
        break;
      case 'removeApostrophes':
        result = result.replace(/'/g, '');
        break;
      case 'normalizeSpaces':
        result = result.replace(/\s+/g, ' ');
        break;
      case 'normalizeCompany':
        result = normalizeCompany(result);
        break;
      case 'normalizeSymbols':
        result = normalizeSymbols(result);
        break;
      case 'removeLetters':
        result =
          result.replace(/[a-zA-Z]/g, '').length >= 6
            ? result.replace(/[a-zA-Z]/g, '')
            : result;
        break;
      case 'removedashnn':
        result = result.replace(/-\S\S?$/g, '');
        result = result.replace(/_\S\S?$/g, '');
        break;
      case 'removespacenn':
        result = result.replace(/ \d\d?$/g, '');
        break;
      case 'removeFromEnd':
        result = result.substring(
          0,
          result.length - (params ? params['removeFromEnd']?.n || 1 : 1)
        );
        break;
      case 'slice':
        result = result.slice(
          params?.slice?.indexStart || 0,
          params?.slice?.indexEnd || undefined
        );
        break;
    }
  });
  return result;
};

const distanceLevenshtein = (s1: string, s2: string) => {
  const _s1 = s1 ?? '';
  const _s2 = s2 ?? '';
  const maxLength = Math.max(_s1.length, _s2.length);
  const dist = distance(_s1, _s2);
  const score = (maxLength - dist) / maxLength;
  return score;
};
const distanceDamerauLevenshtein = (s1: string, s2: string) => {
  const res = levenshtein(s1, s2);
  return res.similarity;
};

const normalizeCompany = (s?: string) => {
  return s
    ?.replace(
      /(llc|inc|inc\.|co|corp|ltd|limited|insurance|agency|co, llc)$/i,
      ''
    )
    ?.replace(/(,\s*$)/, '')
    ?.trim();
};

const normalizeSymbols = (s?: string) => {
  return s?.replace(/ & /, ' and ');
};

export const funcLib = {
  distanceLevenshtein,
  distanceDamerauLevenshtein,
  normalizeCompany,
  normalizeSymbols,
};

export const getScoreMethod = (reconciler) => {
  let scoreMethod = (report, statement, _libs) =>
    distanceLevenshtein(report.policy_id, statement.policy_id);
  if (reconciler.method_type === 'similarity-config') {
    scoreMethod = (report, statement, _libs) => {
      const results = [];
      reconciler.similarity_config.forEach((fieldConfig) => {
        let reportFieldVal = applyTransformers(
          report[fieldConfig.reportField],
          fieldConfig.reportTransformers
        );
        let statementFieldVal = applyTransformers(
          statement[fieldConfig.statementField],
          fieldConfig.statementTransformers
        );
        if (
          fieldConfig?.additionalSettings?.includes('simiarlityOnShorterLength')
        ) {
          const shorterLength = Math.min(
            reportFieldVal?.length ?? 0,
            statementFieldVal?.length ?? 0
          );
          if (shorterLength >= 16) {
            reportFieldVal = reportFieldVal?.substring(0, shorterLength);
            statementFieldVal = statementFieldVal?.substring(0, shorterLength);
          }
        }

        let score = distanceLevenshtein(reportFieldVal, statementFieldVal);
        if (
          fieldConfig?.additionalSettings?.includes('startsWithMatch') &&
          reportFieldVal?.length > 2 &&
          statementFieldVal?.length > 2 &&
          (reportFieldVal.startsWith(statementFieldVal) ||
            statementFieldVal.startsWith(reportFieldVal))
        ) {
          score = 1;
        }
        const result = score >= (fieldConfig.threshold ?? 0) ? score : 0;
        if (
          !fieldConfig?.additionalSettings?.includes('ignoreEmpty') ||
          (reportFieldVal && statementFieldVal)
        ) {
          results.push(result);
        }
      });
      const avg = results.reduce((acc, cur) => cur + acc, 0) / results.length;
      return avg;
    };
  } else if (reconciler.method_type === 'similarity-custom') {
    scoreMethod = eval(reconciler.similarity_config);
  }
  return scoreMethod;
};

export const getFieldConfigs = (
  mode = 'insurance'
): {
  type?: string;
  visible?: boolean;
  fieldId?: string;
  source?: string;
  enabled?: boolean;
  label?: string;
  compute?: (v: any, settings?: any) => any;
  getKey?: (s: any) => string;
  keyAs?: string;
  id2?: string;
  method?: (acc: any, cur: any) => any;
  additionalSettings?: string[];
}[] => {
  const fields = [
    {
      type: 'field',
      visible: false,
      fieldId: 'id',
      source: 'reports',
      enabled: true,
    },
    {
      type: 'field',
      visible: false,
      fieldId: 'override',
      source: 'reports',
      enabled: true,
    },
    {
      type: 'field',
      fieldId: 'policy_id',
      source: 'reports',
      enabled: true,
    },
    {
      type: 'field',
      fieldId: 'writing_carrier_name',
      source: 'statements',
      enabled: mode === 'insurance',
    },
    {
      type: 'field',
      fieldId: 'carrier_name',
      source: 'statements',
      enabled: true,
    },
    {
      type: 'field',
      fieldId: 'customer_name',
      source: 'statements',
      enabled: true,
    },
    {
      type: 'field',
      fieldId: 'product_type',
      source: 'reports',
      enabled: mode === 'insurance',
    },
    {
      type: 'field',
      fieldId: 'product_sub_type',
      source: 'reports',
      enabled: mode === 'insurance',
    },
    { type: 'field', fieldId: 'agent_name', source: 'statements' },
    {
      type: 'field',
      fieldId: 'effective_date',
      source: 'reports',
      enabled: true,
    },
    {
      type: 'field',
      fieldId: 'premium_amount',
      source: 'statements',
      enabled: true,
    },
    {
      type: 'field',
      fieldId: 'premium_amount',
      source: 'reports',
      label: 'Report Premium',
      infoIcon: true,
    },
    {
      type: 'field',
      fieldId: 'commissions_expected',
      source: 'reports',
      enabled: true,
    },
    {
      type: 'field',
      fieldId: 'notes',
      source: 'reports',
      enabled: true,
    },
    {
      type: 'field',
      fieldId: 'commissionable_premium_amount',
      source: 'reports',
      enabled: true,
    },
    {
      type: 'field',
      fieldId: 'product_name',
      source: 'reports',
      enabled: true,
    },
    {
      type: 'field',
      fieldId: 'issue_age',
      source: 'reports',
      enabled: true,
    },
    {
      type: 'field',
      fieldId: 'group_id',
      source: 'reports',
      enabled: true,
    },
    {
      type: 'field',
      fieldId: 'internal_id',
      source: 'reports',
      enabled: true,
    },
    {
      type: 'field',
      fieldId: 'transaction_type',
      source: 'statements',
      enabled: true,
    },
    {
      type: 'field',
      fieldId: 'processing_date',
      source: 'statements',
      enabled: true,
    },
    {
      type: 'aggregate',
      getKey: () => 'amount_paid',
      fieldId: 'commission_amount',
      keyAs: 'amount_paid',
      id2: 'amount_paid',
      enabled: true,
      getter: (v) => v?.amount_paid?.amount_paid,
      source: 'statements',
    },
    {
      type: 'computed',
      fieldId: 'balance',
      compute: (v) => {
        return (
          (v.commissions_expected ?? 0) -
          (v?.amount_paid?.amount_paid?.amount_paid ?? 0)
        );
      },
      enabled: true,
    },
    {
      type: 'field',
      fieldId: 'policy_status',
      source: 'reports',
      enabled: true,
    },
    {
      type: 'field',
      fieldId: 'cancellation_date',
      source: 'reports',
      enabled: true,
    },
    {
      type: 'field',
      fieldId: 'reinstatement_date',
      source: 'reports',
      enabled: true,
    },
    {
      type: 'field',
      fieldId: 'agent_name',
      source: 'reports',
      enabled: true,
    },
    {
      type: 'field',
      fieldId: 'agent_id',
      source: 'reports',
      enabled: true,
    },
    {
      type: 'field',
      fieldId: 'parent_id',
      source: 'reports',
      enabled: true,
    },
    {
      type: 'field',
      fieldId: 'customer_paid_premium_amount',
      source: 'reports',
      enabled: true,
    },
    {
      type: 'computed',
      fieldId: 'reconciled',
      compute: (v) => {
        if (v.report_id && v.statement_ids?.length > 0) {
          if (sumCommissionAmountMonthly(v.commission_amount_monthly) > 0) {
            return ReconciliationStatus.COMMISSIONS_RECEIVED;
          }

          return ReconciliationStatus.RECONCILED;
        }

        return v.report_id
          ? ReconciliationStatus.MISSING_COMMISSIONS
          : ReconciliationStatus.MISSING_POLICY;
      },
      enabled: true,
    },
    { type: 'field', visible: false, label: 'Notes', fieldId: 'notes' },
    {
      type: 'aggregate',
      visible: false,
      getKey: (s) =>
        dayjs(s.payment_date).startOf('month').format('MM/DD/YYYY'),
      fieldId: 'commission_amount',
      keyAs: 'commission_amount_monthly',
      id2: 'amount_paid_monthly',
      enabled: true,
      source: 'statements',
    },
    {
      type: 'aggregate',
      visible: false,
      fieldId: 'payment_date',
      keyAs: 'payment_date_first',
      getKey: () => 'payment_date_first',
      enabled: true,
      method: (acc: Date | null, cur: Date) =>
        cur > (acc || new Date('1900-01-01')) ? acc : cur,
      source: 'statements',
    },
    {
      type: 'aggregate',
      visible: false,
      fieldId: 'payment_date',
      keyAs: 'payment_date_last',
      getKey: () => 'payment_date_last',
      enabled: true,
      method: (acc: Date | null, cur: Date) =>
        cur > (acc || new Date('2100-01-01')) ? cur : acc,
      source: 'statements',
    },
    {
      type: 'computed',
      fieldId: 'amount_paid_commissionable_premium_amount_pct',
      compute: (v) => {
        const res =
          +(v?.amount_paid?.amount_paid?.amount_paid ?? 0) /
          numberOrDefault(+v.commissionable_premium_amount, 0);
        return Number.isNaN(res) ? null : res;
      },
      enabled: true,
    },
    {
      type: 'computed',
      fieldId: 'amount_paid_premium_amount_pct',
      compute: (v) => {
        const res =
          +(v?.amount_paid?.amount_paid?.amount_paid ?? 0) /
          numberOrDefault(+v.premium_amount, 0);
        return Number.isNaN(res) ? null : res;
      },
      enabled: true,
    },
  ];

  return fields.filter((e) => e.enabled);
};

// Calculate the hash of the reconciler
export const digestReconciler = function (data = {}) {
  const fields = [
    'config',
    'key_config_report',
    'key_config_statement',
    'key_condition',
    'method_type',
    'method_threshold_match',
    'method_threshold_maybe',
    'similarity_config',
  ].sort();
  const content = fields.map((key) => JSON.stringify(data[key])).join('');
  return createHash('md5').update(content).digest('hex');
};

type CommissionData = Record<
  string,
  {
    commission_amount_monthly: number;
  }
>;

export function sumCommissionAmountMonthly(data: CommissionData): number {
  if (!data) return 0;

  return Object.values(data).reduce(
    (total, item) => total + (item.commission_amount_monthly || 0),
    0
  );
}
