import { isNill } from 'common/helpers';

import { FilterValue, FilterableValue, RecordValue } from './matcher';

const normalizeAsArray = (value: FilterValue): FilterableValue[] => {
  if (typeof value === 'string') {
    return value.split(',').map((item) => item.trim());
  } else if (Array.isArray(value)) {
    return value;
  }
  return [value];
};

export const containsOperator = (
  recordValue: RecordValue,
  filterValue: FilterValue
): boolean => {
  const valuesArray = normalizeAsArray(filterValue);
  return valuesArray.some((item) =>
    (recordValue as string)?.includes(String(item))
  );
};

export const notContainsOperator = (
  recordValue: RecordValue,
  filterValue: FilterValue
): boolean => {
  return !containsOperator(recordValue, filterValue);
};

export const isNotEmptyOperator = (recordValue: RecordValue): boolean => {
  if (Array.isArray(recordValue)) {
    return recordValue.length > 0;
  }

  return !isNill(recordValue);
};
