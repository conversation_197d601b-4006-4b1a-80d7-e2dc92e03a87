import { describe, it, expect } from 'vitest';

import {
  containsOperator,
  notContainsOperator,
  isNotEmptyOperator,
} from './matcher.helpers';

describe('containsOperator', () => {
  it('Given string record value and string filter value, should return true when record contains filter', () => {
    expect(containsOperator('hello world', 'world')).toBe(true);
  });

  it('Given string record value and string filter value, should return false when record does not contain filter', () => {
    expect(containsOperator('hello world', 'xyz')).toBe(false);
  });

  it('Given string record value and array filter value, should return true when record contains any filter item', () => {
    expect(containsOperator('hello world', ['world', 'xyz'])).toBe(true);
  });

  it('Given string record value and array filter value, should return false when record contains none of the filter items', () => {
    expect(containsOperator('hello world', ['xyz', 'abc'])).toBe(false);
  });

  it('Given string record value and comma-separated filter value, should return true when record contains any item', () => {
    expect(containsOperator('hello world', 'world, xyz')).toBe(true);
  });

  it('Given string record value and comma-separated filter value, should return false when record contains none of the items', () => {
    expect(containsOperator('hello world', 'xyz, abc')).toBe(false);
  });

  it('Given string record value and number filter value, should convert number to string and check containment', () => {
    expect(containsOperator('test123', 123)).toBe(true);
  });

  it('Given string record value and boolean filter value, should convert boolean to string and check containment', () => {
    expect(containsOperator('result: true', true)).toBe(true);
  });

  it('Given null record value, should return false', () => {
    expect(containsOperator(null, 'test')).toBe(false);
  });

  it('Given undefined record value, should return false', () => {
    expect(containsOperator(undefined, 'test')).toBe(false);
  });

  it('Given empty string record value and empty string filter value, should return true', () => {
    expect(containsOperator('', '')).toBe(true);
  });
});

describe('notContainsOperator', () => {
  it('Given string record value that contains filter value, should return false', () => {
    expect(notContainsOperator('hello world', 'world')).toBe(false);
  });

  it('Given string record value that does not contain filter value, should return true', () => {
    expect(notContainsOperator('hello world', 'xyz')).toBe(true);
  });

  it('Given string record value and array filter value where record contains any item, should return false', () => {
    expect(notContainsOperator('hello world', ['world', 'xyz'])).toBe(false);
  });

  it('Given string record value and array filter value where record contains none of the items, should return true', () => {
    expect(notContainsOperator('hello world', ['xyz', 'abc'])).toBe(true);
  });

  it('Given null record value, should return true', () => {
    expect(notContainsOperator(null, 'test')).toBe(true);
  });

  it('Given undefined record value, should return true', () => {
    expect(notContainsOperator(undefined, 'test')).toBe(true);
  });
});

describe('isNotEmptyOperator', () => {
  it('Given array with items, should return true', () => {
    expect(isNotEmptyOperator(['item1', 'item2'])).toBe(true);
  });

  it('Given empty array, should return false', () => {
    expect(isNotEmptyOperator([])).toBe(false);
  });

  it('Given non-empty string, should return true', () => {
    expect(isNotEmptyOperator('hello')).toBe(true);
  });

  it('Given empty string, should return false', () => {
    expect(isNotEmptyOperator('')).toBe(false);
  });

  it('Given string with only spaces, should return true', () => {
    expect(isNotEmptyOperator('   ')).toBe(true);
  });

  it('Given null value, should return false', () => {
    expect(isNotEmptyOperator(null)).toBe(false);
  });

  it('Given undefined value, should return false', () => {
    expect(isNotEmptyOperator(undefined)).toBe(false);
  });

  it('Given string "null", should return false', () => {
    expect(isNotEmptyOperator('null')).toBe(false);
  });

  it('Given string "undefined", should return false', () => {
    expect(isNotEmptyOperator('undefined')).toBe(false);
  });

  it('Given number zero, should return true', () => {
    expect(isNotEmptyOperator(0)).toBe(true);
  });

  it('Given positive number, should return true', () => {
    expect(isNotEmptyOperator(42)).toBe(true);
  });

  it('Given negative number, should return true', () => {
    expect(isNotEmptyOperator(-5)).toBe(true);
  });

  it('Given boolean true, should return true', () => {
    expect(isNotEmptyOperator(true)).toBe(true);
  });

  it('Given boolean false, should return true', () => {
    expect(isNotEmptyOperator(false)).toBe(true);
  });

  it('Given string "NaN", should return false', () => {
    expect(isNotEmptyOperator('NaN')).toBe(false);
  });

  it('Given empty quotes as string, should return false', () => {
    expect(isNotEmptyOperator('""')).toBe(false);
  });

  it('Given empty single quotes as string, should return false', () => {
    expect(isNotEmptyOperator("''")).toBe(false);
  });
});
