# api

## Releases on 2025-07-14

### Version 6.11.0
<details>

### Minor Changes
 - Added support for retro payment allocation

### Patch Changes
 - Update Statement service filter to consider unselect agents
- Updated dependencies [48371a9]
- Updated dependencies [cd9fde0]
  - common@0.24.7
</details>

### Version 6.10.18
<details>

### Patch Changes
 - Improved transaction management with pagination support and refactored backend logic
 - Added support for AI extraction(Gemini, ChatGPT, Claude, etc.) and enabled the ability to choose between multiple extraction methods in document processing.
 - - Introduced a new field 'processing_date' in the reconciliation configuration.
  - Enhanced filtering logic to handle fields specific to reconciliation version 2.
  - Added constants for reconciliation versions and fields to improve maintainability.
 - Added tooltips on document status fields to provide more detailed information about document processing results and errors.
- Updated dependencies [d6ee6a7]
- Updated dependencies [5547d60]
- Updated dependencies [fefa9a4]
  - common@0.24.6
</details>

### Version 6.10.17
<details>

### Patch Changes
 - Add custom report feature under the report to enable user upload custom report
 - Skip requirement for comp grid criteria if calc method doesn't require comp grid rates.
 - Customers page:
  - Move config from web to common, and add textFormatter for use in export.
  - Updated the `ExportCustomersHandler` to export based on the new config.
  - Added unit tests for textFormatter.
- Updated dependencies [f53d05c]
- Updated dependencies [1c9d5ce]
  - common@0.24.5
</details>

## Releases on 2025-07-11

### Version 6.10.16
<details>

### Patch Changes
 - Producers will now only see commissions in paid or approved state.
 - Data actions tool: Add support to 'is not empty' operator for string fields (e.g. Group name field).
 - Only sync policies with state = inforce, and sync commPrem & commAnnPrem
 - Added locks to Views and Fields which are not editable.
 - Allied payment allocation improvements including UI & allocation adjustment
</details>

### Version 6.10.15
<details>

### Patch Changes
 - Remove page & limit to export full data
</details>

### Version 6.10.14
<details>

### Patch Changes
 - Removed accounting transactions enabled flag when creating comp reports
 - Remove users with deleted access from Admin > Accounts
</details>

## Releases on 2025-07-10

### Version 6.10.13
<details>

### Patch Changes
 - Sync transaction type , group name and sales vps for DMI account
</details>

## Releases on 2025-07-09

### Version 6.10.12
<details>

### Patch Changes
 - - Update TWC agent payout rate calculation to average result.
  - Fix invalid hook call in JsonStringToggle formatter
  - Add loading skeletons to DynamicSelect
  - Set max width in value on FieldConfig component to reduce overflow (still can happen)
  - Don't retry permission denied requests
 - Allow users to reset payment allocations
 - Disable member count syncing feature for risk tag
 - Fix creating / saving commissions failing due to transaction_type formatter returning object.
</details>

### Version 6.10.11
<details>

### Patch Changes
 - Fix companies using potential_match column that doesn't exist in our schema caused by: 1. Ignoring the potential_match field 2. Reverting deprecated code
 - Export alignment on Policy page
  - Move config from web to common, and add textFormatter for use in export
  - Move DataTransformation from web to common to be used by textFormatter
  - Refactor the report_data export logic with the following steps: getData → get fields based on config and accountSetting → fetch dynamicSelect data → formatData → export CSV
 - Display the carrier grid level name and the house payout grid level name for single carrier mode comp profiles
 - Fix the commission calculation failure with different calc basis
 - When manually adjusting agent_commissions in commissions page, also adjust accounting transaction details
 - - Introduced a new model `uploaded_saved_reports` to manage file metadata.
  - Updated existing `saved_reports` model to include a foreign key reference to `uploaded_saved_reports`.
  - Added new endpoints for uploading, downloading, listing and filtering saved report files.
  - Created DTOs for file upload and retrieval operations.
 - Include relational fields for params in data update criteria and actions. Avoid using asynchronous logic for executing custom rules.
 - Add a new reconciled status: Commission received
 - Fixes for comp reports not being approved
 - Fix some issue on processor part: 1.missing data in company and some fields. 2. Crash when open crate a new processors.
 - Refactor reconciliation_data v2 api
  - Remove paging when querying the database
  - Implement sorting and pagination in JS
  - Add tests for sorting and pagination
  - Implemented batch retrieval instead of retrieving all records in a single query
 - Fix grouping failure caused by null premium_amount & split_percent
 - Fix the prisma select issue
 - - Introduced `policyDataIfEmptyFields` constant to manage fields sourced from policy data.
  - Updated `queryFieldValues` to utilize `findMany` for fetching statement data, enhancing flexibility in selecting fields.
  - Modified `StatementFilterService` to incorporate policy data fields in filter logic.
  - Refactored table formatting in `Statements.tsx` to improve tooltip handling for policy data discrepancies.
 - Fix for accounting transaction details not saving from agents page
- Updated dependencies [db2a894]
- Updated dependencies [624c907]
- Updated dependencies [df70eef]
- Updated dependencies [d519450]
- Updated dependencies [07887a5]
- Updated dependencies [c09430e]
  - common@0.24.4
</details>

## Releases on 2025-07-07

### Version 6.10.9
<details>

### Patch Changes
 - - Updated the `is_saved_report` parameter in `shouldGetSelectedFields` to accept a string instead of a boolean.
  - Adjusted related function calls across various API endpoints to pass the `is_saved_report` as a string.
  - Removed unnecessary comments and TODOs from the codebase for clarity.
  - Introduced `ChildRelationshipWithContact` type for better type safety.
 - - Move config from web to common, and add textFormatter for use in export.
  - Implemented `getDocumentFieldConfig` to retrieve field configurations based on account mode and timezone.
  - Added `formatExportData` and `mapHeaderFromFieldConfigs` to process export data and headers.
  - Updated the `ExportDocumentsHandler` to include timezone as a parameter.
  - Introduced new constants for field types and labels to improve maintainability.
- Updated dependencies [9e548c2]
- Updated dependencies [0a84843]
  - common@0.24.3
</details>

### Version 6.10.8
<details>

### Patch Changes
 - Only calculate renewal profiles if the statement’s compensation type is Renewal Commission.
 - Improves performance by minimizing database load for all required data in comp calc.
</details>

### Version 6.10.10
<details>

### Patch Changes
 - Fix handling of commission basis for is_virutal commission records by not adding child commission_amounts.
</details>

## Releases on 2025-07-06

### Version 6.10.7
<details>

### Patch Changes
 - Fix for auto populate house rate and rate when creating new lines in comp grid viewer for grids with only carrier rate.
 - Fixed bug for data actions tool where a deleted action still takes effect on data preview
</details>

### Version 6.10.6
<details>

### Patch Changes
 - Fix issue where created_by is missing when uploading a document.
 - Fix companies unable to update document profile mappings due to unique constraint violation in "companies_document_profiles".
 - Fix file upload API failing due to missing service configuration
 - Update metrics view to fix the issue where the table doesn’t separate "auto" and "manual" data, and to resolve the legend overlap in the ‘Company Documents Count’ chart.
</details>

## Releases on 2025-07-04

### Version 6.10.5
<details>

### Patch Changes
 - Fixed agent transaction save failure when all transactions had been deleted
</details>

### Version 6.10.4
<details>

### Patch Changes
 - Reduce classification confidence threshold to 0.8 to allow more files result.
 - In admin accounts, sort users by active first. Add user state formatter.
 - Fixed integration tests
 - `Total premium` now available in saved report group views and exports.
- Updated dependencies [6a7dee2]
  - common@0.24.2
</details>

### Version 6.10.3
<details>

### Patch Changes
 - Not calc receivables for sale reps.
</details>

### Version 6.10.2
<details>

### Patch Changes
 - Support specifying a multiplier for each comp profile in match criteria when the calculation method is either "Pay commission to grid level" or "Share override to grid level".
 - Delete accounting transactions data from agents payload in agents view
</details>

## Releases on 2025-07-03

### Version 6.10.1
<details>

### Patch Changes
 - Updated report summary labels
 - Allow user to select manual grouping calculation method
 - Fixed "Hide commissions with payout" filter on Commissions page that was not returning expected results
- Updated dependencies [daa48af]
  - common@0.24.1
</details>

## Releases on 2025-07-02

### Version 6.10.0
<details>

### Minor Changes
 - Update Companies & Global Companies Page: 1. Link Suggestions: Added a feature to suggest potential account companies that can be linked to each global company. It shows how many unlinked companies are available and allows linking them directly. 2. Merge Function: Introduced a merge feature that lets users merge selected fields from one global company into another, helping maintain cleaner and more consistent data.

### Patch Changes
 - Fixed an error that occurred when updating data for the 'state' field
 - Correctly populate fields value into generated grouped statement, including new_commission_rate, contacts, split_percentage, agent_commission_payout_rate and agent_payout_rate
 - Made export agent columns CSV and added Formatter.label
 - Optimise the Companies / Global Companies page:
  1. Fixed the issue preventing global companies from being updated.
  2. Optimized the global companies search function by removing irrelevant search options.
  3. Improved the UI for processors and profiles in the Companies / Global Companies page by adding item counts and collapse functionality.
 - Replace comp reports data source from snapshotdata to accounting transactions in new endpoint and new FE component in '.../comp-reports/report_str_id' route.
 - Support running groupings with rules event with no default grouping settings
 - Fix for comp calc not getting results for Hitchings account
 - Replace comp reports data source from snapshotdata to accounting transactions part IV
 - Fix the bulk edit issue that when we update the any field the date field(like deposit date) will be auto updated
 - Fix the bug of paying current agent if hierarchy processing is none
  Fix the bug of associate current agents with the appropriate compensation profile when the "Apply to Downlines" option is selected for an upline agent in the comp profile settings.
  Fetch all comp profiles for comp profile matching service
- Updated dependencies [dc66daf]
- Updated dependencies [2c59195]
- Updated dependencies [2c59195]
- Updated dependencies [034e734]
  - common@0.24.0
</details>

## Releases on 2025-07-01

### Version 6.9.5
<details>

### Patch Changes
 - Enhance the data update tool to support executing queries independently of custom code logic.
 - Fixed grouping failures due to foreign key constraint violations
</details>

