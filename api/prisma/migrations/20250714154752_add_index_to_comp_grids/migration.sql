-- CreateIndex
CREATE INDEX IF NOT EXISTS "idx_comp_grid_levels_id_state" ON "comp_grid_levels"("id", "state");

-- CreateIndex
CREATE INDEX IF NOT EXISTS "comp_grid_rates_state_comp_grid_level_id_idx" ON "comp_grid_rates"("state", "comp_grid_level_id");

-- CreateIndex
CREATE INDEX IF NOT EXISTS "idx_comp_grid_rates_query_opt" ON "comp_grid_rates"("account_id", "state", "comp_grid_criterion_id", "comp_grid_level_id", "id");

-- CreateIndex
CREATE INDEX IF NOT EXISTS "idx_comp_grid_rates_state_level" ON "comp_grid_rates"("state", "comp_grid_level_id");

-- CreateIndex
CREATE INDEX IF NOT EXISTS "idx_date_ranges_id_state_dates" ON "date_ranges"("id", "state", "start_date", "end_date");

CREATE INDEX IF NOT EXISTS idx_CompGridRatesDateRanges_A_B ON "_CompGridRatesDateRanges" ("A", "B");