generator client {
  provider        = "prisma-client-js"
  previewFeatures = ["postgresqlExtensions", "metrics"]
  binaryTargets   = ["native", "linux-musl-arm64-openssl-3.0.x"]
}

datasource db {
  provider   = "postgresql"
  url        = env("DATABASE_URL")
  extensions = [vector]
}

model account_configs {
  id                 Int      @id @default(autoincrement())
  str_id             String?  @unique @default(dbgenerated("\"substring\"(md5((random())::text), 0, 21)"))
  account_id         String?  @db.VarChar(36)
  uid                String?
  state              String   @default("active")
  created_at         DateTime @default(now()) @db.Timestamp(6)
  created_by         String?  @db.VarChar(36)
  created_proxied_by String?  @db.VarChar(36)
  updated_at         DateTime @default(now()) @db.Timestamp(6)
  updated_by         String?  @db.VarChar(36)
  updated_proxied_by String?  @db.VarChar(36)

  name  String? @db.Var<PERSON>har(255)
  notes String?
  type  String  @db.Var<PERSON>har(255)
  value Json

  account accounts? @relation(fields: [account_id], references: [str_id])

  @@unique([account_id, name, state])
  @@index([account_id, type, state])
}

// TODO: Clean up the fields that are not used in an individual PR so we can reverted if needed
model account_role_settings {
  id                 Int       @id @default(autoincrement())
  account_id         String    @db.VarChar(36)
  state              String    @default("active")
  created_at         DateTime? @default(now()) @db.Timestamp(6)
  created_by         String?   @db.VarChar(36)
  created_proxied_by String?   @db.VarChar(36)
  updated_at         DateTime? @default(now()) @db.Timestamp(6)
  updated_by         String?   @db.VarChar(36)
  updated_proxied_by String?   @db.VarChar(36)

  agent_settings            Json?
  commissions_filters       Json?
  commissions_page_label    String? @default("Commissions") // Deprecated field, we need to remove it
  commissions_view          Json? // Deprecated field, we need to remove it
  companies_view            Json?
  custom_view_name          String  @default("default")
  default_page              String?
  insights_widgets          Json?
  pages_settings            Json?
  policies_page_label       String? @default("Policies") // Deprecated field, we need to remove it
  policies_view             Json? // Deprecated field, we need to remove it
  reconciliation_page_label String? @default("Reconciliation") // Deprecated field, we need to remove it
  reconciliation_view       Json? // Deprecated field, we need to remove it
  role_id                   Int

  account  accounts   @relation(fields: [account_id], references: [str_id])
  contacts contacts[]
  role     roles      @relation(fields: [role_id], references: [id])

  @@unique([account_id, role_id, custom_view_name])
}

model account_user_roles {
  state              String   @default("active")
  created_at         DateTime @default(now()) @db.Timestamp(6)
  created_by         String?  @db.VarChar(36)
  created_proxied_by String?  @db.VarChar(36)
  updated_at         DateTime @default(now()) @db.Timestamp(6)
  updated_by         String?  @db.VarChar(36)
  updated_proxied_by String?  @db.VarChar(36)

  account_id String @db.VarChar(36)
  user_id    String @db.VarChar(36)
  role_id    Int

  account accounts @relation(fields: [account_id], references: [str_id])
  role    roles    @relation(fields: [role_id], references: [id])
  user    users    @relation(fields: [user_id], references: [str_id])

  @@id([account_id, user_id])
  @@unique([account_id, user_id])
}

model accounting_transactions {
  id                 Int      @id @default(autoincrement())
  str_id             String   @unique @default(dbgenerated("\"substring\"(md5((random())::text), 0, 21)")) @db.VarChar(36)
  account_id         String?  @db.VarChar(36)
  state              String   @default("active")
  created_at         DateTime @default(now()) @db.Timestamp(6)
  created_by         String?  @db.VarChar(36)
  created_proxied_by String?  @db.VarChar(36)
  updated_at         DateTime @default(now()) @db.Timestamp(6)
  updated_by         String?  @db.VarChar(36)
  updated_proxied_by String?  @db.VarChar(36)

  amount          Decimal?
  contact_id      Int?
  report_id       Int?
  saved_report_id Int?
  date            DateTime?
  logs            Json?
  notes           String?
  status          String?   @default("draft")
  type            String?

  accounting_transaction_details accounting_transaction_details[] @relation("TransactionDetails")
  contact                        contacts?                        @relation(fields: [contact_id], references: [id])
  report_data                    report_data?                     @relation(fields: [report_id], references: [id])
  saved_report                   saved_reports?                   @relation(fields: [saved_report_id], references: [id])
}

model accounting_transaction_details {
  id                 Int      @id @default(autoincrement())
  str_id             String   @unique @default(dbgenerated("\"substring\"(md5((random())::text), 0, 21)")) @db.VarChar(36)
  account_id         String?  @db.VarChar(36)
  state              String   @default("active")
  created_at         DateTime @default(now()) @db.Timestamp(6)
  created_by         String?  @db.VarChar(36)
  created_proxied_by String?  @db.VarChar(36)
  updated_at         DateTime @default(now()) @db.Timestamp(6)
  updated_by         String?  @db.VarChar(36)
  updated_proxied_by String?  @db.VarChar(36)

  amount          Decimal?
  contact_id      Int?
  date            DateTime?
  logs            Json?
  notes           String?
  report_id       Int?
  saved_report_id Int?
  statement_id    Int?
  status          String?   @default("draft")
  transaction_id  Int?
  type            String?
  rate            Decimal?  @db.Decimal
  party           String?   @db.VarChar(36)
  company_id      Int?
  tags            String[]  @default([])

  customer_id Int?

  accounting_transactions accounting_transactions[] @relation("TransactionDetails")
  statement_data          statement_data?           @relation(fields: [statement_id], references: [id])
  customer                customers?                @relation(fields: [customer_id], references: [id])
  saved_report            saved_reports?            @relation(fields: [saved_report_id], references: [id])
  report                  report_data?              @relation("ReportToTransactionDetails", fields: [report_id], references: [id])
  contact                 contacts?                 @relation(fields: [contact_id], references: [id])
  company                 companies?                @relation(fields: [company_id], references: [id])

  @@index([account_id, state])
  @@index([account_id, state, report_id])
  @@index([account_id, state, statement_id])
}

model accounts {
  id                 Int      @id @default(autoincrement())
  str_id             String   @unique @default(dbgenerated("\"substring\"(md5((random())::text), 0, 21)")) @db.VarChar(36)
  uid                String?
  state              String   @default("pending")
  created_at         DateTime @default(now()) @db.Timestamp(6)
  created_by         String?  @db.VarChar(36)
  created_proxied_by String?  @db.VarChar(36)
  updated_at         DateTime @default(now()) @db.Timestamp(6)
  updated_by         String?  @db.VarChar(36)
  updated_proxied_by String?  @db.VarChar(36)

  accounting_transactions_enabled     Boolean  @default(false)
  auto_email_commission_report        Boolean  @default(false)
  auto_share_commission_report        Boolean  @default(false)
  commissions_expected_source         String?
  comp_grids_enabled                  Boolean  @default(false)
  csms                                Json?
  dashboard_filter_by_agent           String?
  dashboard_widgets                   Json?
  data_processors                     Json?
  default_reconciler                  String?
  description                         String?
  is_allowed_upload_from_email        Boolean?
  logo_url                            String?
  mode                                String?
  name                                String?
  notes                               String?
  only_paid_commissions_for_producers Boolean  @default(false)
  plaid_access_token                  String?
  plan_name                           String?
  policies_show_grouped               Boolean? @default(false)
  reconciliation_threshold            Decimal? @db.Decimal
  reconciliation_units                String?
  report_de_dupe                      Json?
  report_grouping                     Json?
  report_grouping2                    Json?
  short_name                          String?
  statement_de_dupe                   Json?
  statement_grouping                  Json?
  status                              String?
  stripe_id                           String?
  subscription                        Json?
  type                                String?
  white_label_mode                    Boolean  @default(false)

  account_configs        account_configs[]
  account_role_settings  account_role_settings[]
  account_user_roles     account_user_roles[]
  data_processing        data_processing[]
  documents              documents[]
  processors             processors[]
  saved_report_groups    saved_report_groups[]
  saved_reports          saved_reports[]
  user                   users?                   @relation(fields: [created_by], references: [uid])
  uploaded_saved_reports uploaded_saved_reports[]
}

model admin {
  id    Int     @id @default(autoincrement())
  uid   String? @unique
  notes String?
}

model contacts_agent_commission_schedule_profiles_sets {
  str_id             String   @id @default(dbgenerated("\"substring\"(md5((random())::text), 0, 21)"))
  account_id         String?  @db.VarChar(36)
  uid                String
  state              String   @default("active")
  created_at         DateTime @default(now()) @db.Timestamp(6)
  created_by         String?  @db.VarChar(36)
  created_proxied_by String?  @db.VarChar(36)
  updated_at         DateTime @default(now()) @db.Timestamp(6)
  updated_by         String?  @db.VarChar(36)
  updated_proxied_by String?  @db.VarChar(36)

  contact_id                               Int
  agent_commission_schedule_profile_set_id Int
  start_date                               DateTime? @db.Timestamp(6)
  end_date                                 DateTime? @db.Timestamp(6)
  hierarchy_processing                     String?
  multiplier                               Decimal?  @db.Decimal
  method                                   String?
  rate                                     Decimal?  @db.Decimal
  payee_id                                 Int?
  config                                   Json?
  sync_id                                  String?
  sync_worker                              String?

  contact                                 contacts?                                @relation(fields: [contact_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
  agent_commission_schedule_profiles_sets agent_commission_schedule_profiles_sets? @relation(fields: [agent_commission_schedule_profile_set_id], references: [id], onDelete: NoAction, onUpdate: NoAction)

  @@unique([contact_id, agent_commission_schedule_profile_set_id, str_id])
  @@unique([account_id, sync_id, state])
}

model agent_commission_schedule_profiles {
  id                 Int      @id @default(autoincrement())
  str_id             String   @unique @default(dbgenerated("\"substring\"(md5((random())::text), 0, 21)"))
  account_id         String?  @db.VarChar(36)
  uid                String
  state              String   @default("active")
  created_at         DateTime @default(now()) @db.Timestamp(6)
  created_by         String?  @db.VarChar(36)
  created_proxied_by String?  @db.VarChar(36)
  updated_at         DateTime @default(now()) @db.Timestamp(6)
  updated_by         String?  @db.VarChar(36)
  updated_proxied_by String?  @db.VarChar(36)

  calculation_basis        String?
  calculation_scope        String?
  comp_grid_id             Int?
  company_id               Int?
  hierarchy_processing     Json?
  name                     String?
  notes                    String?
  payee_comp_grid_level_id Int?
  payee_grid_level_name    String?
  payee_types              Json?
  payer_comp_grid_level_id Int?
  payer_grid_level_name    String?
  payout_timing            String?
  product_type             String[] @default([])
  schedules                Json?    @default("[]")
  sync_id                  String?
  sync_worker              String?
  single_carrier_mode      Boolean? @default(true)

  commission_profiles_sets                    agent_commission_schedule_profiles_sets[]
  comp_grid                                   comp_grids?                                   @relation(fields: [comp_grid_id], references: [id])
  company                                     companies?                                    @relation(fields: [company_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
  contacts_agent_commission_schedule_profiles contacts_agent_commission_schedule_profiles[]
  payee_comp_grid_level                       comp_grid_levels?                             @relation("payee_comp_grid_level", fields: [payee_comp_grid_level_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
  payer_comp_grid_level                       comp_grid_levels?                             @relation("payer_comp_grid_level", fields: [payer_comp_grid_level_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
  report_data                                 report_data[]

  @@unique([account_id, sync_id, state])
}

model agent_commission_schedule_profiles_sets {
  id                 Int      @id @default(autoincrement())
  str_id             String   @unique @default(dbgenerated("\"substring\"(md5((random())::text), 0, 21)"))
  account_id         String?  @db.VarChar(36)
  uid                String
  state              String   @default("active")
  created_at         DateTime @default(now()) @db.Timestamp(6)
  created_by         String?  @db.VarChar(36)
  created_proxied_by String?  @db.VarChar(36)
  updated_at         DateTime @default(now()) @db.Timestamp(6)
  updated_by         String?  @db.VarChar(36)
  updated_proxied_by String?  @db.VarChar(36)

  name        String?
  notes       String?
  sync_id     String?
  sync_worker String?

  commission_profiles                              agent_commission_schedule_profiles[]
  contacts_agent_commission_schedule_profiles_sets contacts_agent_commission_schedule_profiles_sets[]

  @@unique([account_id, sync_id, state])
}

model agent_incentive_tiers {
  id                 Int      @id @default(autoincrement())
  str_id             String   @unique @default(dbgenerated("\"substring\"(md5((random())::text), 0, 21)"))
  account_id         String?  @db.VarChar(36)
  uid                String
  state              String   @default("active")
  created_at         DateTime @default(now()) @db.Timestamp(6)
  created_by         String?  @db.VarChar(36)
  created_proxied_by String?  @db.VarChar(36)
  updated_at         DateTime @default(now()) @db.Timestamp(6)
  updated_by         String?  @db.VarChar(36)
  updated_proxied_by String?  @db.VarChar(36)

  calculation_basis String?
  end_date          DateTime? @db.Timestamp(6)
  group             String?
  lookback_quantity Decimal?  @db.Decimal
  lookback_units    String?
  name              String?
  notes             String?
  start_date        DateTime? @db.Timestamp(6)
  threshold_max     Decimal?  @db.Decimal
  threshold_min     Decimal?  @db.Decimal

  companies companies[]
}

model api_key {
  id                  Int       @id @default(autoincrement())
  str_id              String?   @unique @default(dbgenerated("\"substring\"(md5((random())::text), 0, 21)"))
  account_id          String?   @db.VarChar(36)
  state               String    @default("active")
  uid                 String?
  created_at          DateTime  @default(now()) @db.Timestamp(6)
  created_by          String?   @db.VarChar(36)
  created_proxied_by  String?   @db.VarChar(36)
  updated_at          DateTime  @default(now()) @db.Timestamp(6)
  updated_by          String?   @db.VarChar(36)
  updated_proxied_by  String?   @db.VarChar(36)
  disabled_at         DateTime?
  disabled_by         String?   @db.VarChar(36)
  disabled_proxied_by String?   @db.VarChar(36)

  api_key       String   @unique @db.Char(36)
  ip_allow_list String[] @default([])
  name          String
  notes         String?
  permissions   String   @default("all")
  status        String   @default("active")
}

// store client defined processing methods
model calculation_methods {
  id                 Int      @id @default(autoincrement())
  str_id             String?  @unique @default(dbgenerated("\"substring\"(md5((random())::text), 0, 21)"))
  account_id         String?  @db.VarChar(36) //empty account_id means it is a system method
  state              String   @default("active")
  created_at         DateTime @default(now()) @db.Timestamp(6)
  created_by         String?  @db.VarChar(36)
  created_proxied_by String?  @db.VarChar(36)
  updated_at         DateTime @default(now()) @db.Timestamp(6)
  updated_by         String?  @db.VarChar(36)
  updated_proxied_by String?  @db.VarChar(36)

  dependencies Json?
  description  String?
  formula      String?
  inputs       Json?
  method       String?
  name         String
  notes        String?
  type         String?
  version      String  @db.VarChar(13) // use timestamp as version, can be used for rollback or upgrade

  calculation_method_revisions calculation_method_revisions[]
}

model calculation_method_revisions {
  id                 Int      @id @default(autoincrement())
  str_id             String?  @unique @default(dbgenerated("\"substring\"(md5((random())::text), 0, 21)"))
  state              String   @default("active")
  created_at         DateTime @default(now()) @db.Timestamp(6)
  created_by         String?  @db.VarChar(36)
  created_proxied_by String?  @db.VarChar(36)

  version    String  @db.VarChar(13)
  account_id String? @db.VarChar(36)
  method_id  Int
  snapshot   Json // store the whole record snapshot

  calculation_method calculation_methods @relation(fields: [method_id], references: [id])
}

model comments {
  id                 Int      @default(autoincrement())
  str_id             String   @unique @default(dbgenerated("\"substring\"(md5((random())::text), 0, 21)"))
  account_str_id     String?  @db.VarChar
  account_id         String?  @db.VarChar(36)
  user_str_id        String
  state              String   @default("active")
  access             String?  @default("account")
  created_at         DateTime @default(now()) @db.Timestamp(6)
  created_by         String?  @db.VarChar(36)
  created_proxied_by String?  @db.VarChar(36)
  updated_at         DateTime @default(now()) @db.Timestamp(6)
  updated_by         String?  @db.VarChar(36)
  updated_proxied_by String?  @db.VarChar(36)

  message          String
  processor_str_id String?
  type             String?
}

model commission_schedules {
  id                 Int      @id @default(autoincrement())
  str_id             String   @unique @default(dbgenerated("\"substring\"(md5((random())::text), 0, 21)"))
  account_id         String?  @db.VarChar(36)
  uid                String
  state              String   @default("active")
  created_at         DateTime @default(now()) @db.Timestamp(6)
  created_by         String?  @db.VarChar(36)
  created_proxied_by String?  @db.VarChar(36)
  updated_at         DateTime @default(now()) @db.Timestamp(6)
  updated_by         String?  @db.VarChar(36)
  updated_proxied_by String?  @db.VarChar(36)

  agent_grid_level         String?
  carrier_company_id       Int?
  commission_schedule      Json?
  delay                    Int?
  end_date                 DateTime? @db.Timestamp(6)
  issue_age_end            Int?
  issue_age_start          Int?
  name                     String
  notes                    String?
  paying_entity_company_id Int?
  premium_max              Decimal?  @db.Decimal
  premium_min              Decimal?  @db.Decimal
  product_name             String?
  product_type             String?
  start_date               DateTime? @db.Timestamp(6)

  carrier_companies       companies? @relation("carrier_companies", fields: [carrier_company_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
  paying_entity_companies companies? @relation("paying_entity_companies", fields: [paying_entity_company_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
}

model comp_grid_criteria {
  id                 Int      @id @default(autoincrement())
  str_id             String?  @unique @default(dbgenerated("\"substring\"(md5((random())::text), 0, 21)"))
  account_id         String?  @db.VarChar(36)
  uid                String?
  state              String   @default("active")
  access             String?  @default("account")
  created_at         DateTime @default(now()) @db.Timestamp(6)
  created_by         String?  @db.VarChar(36)
  created_proxied_by String?  @db.VarChar(36)
  updated_at         DateTime @default(now()) @db.Timestamp(6)
  updated_by         String?  @db.VarChar(36)
  updated_proxied_by String?  @db.VarChar(36)

  comp_grid_id                  Int
  company_id                    Int?
  compensation_type             String?
  compensation_type_alternative String?
  filter_date_field             String?
  geo_states                    String[] @default([])
  grid_product_id               Int?
  grid_product_option_id        Int?
  issue_age_end                 Int?
  issue_age_start               Int?
  notes                         String?
  payment_mode                  String?
  policy_year_end               Int?
  policy_year_start             Int?
  premium_max                   Decimal? @db.Decimal
  premium_min                   Decimal? @db.Decimal
  sync_id                       String?
  sync_worker                   String?
  transaction_type              String?

  company           companies?          @relation(fields: [company_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
  comp_grid         comp_grids?         @relation(fields: [comp_grid_id], references: [id])
  comp_grid_rates   comp_grid_rates[]
  comp_grid_product comp_grid_products? @relation(fields: [grid_product_id], references: [id])

  date_ranges date_ranges[] @relation("CompGridCriteriaDateRanges")

  @@unique([account_id, sync_id, state])
}

model comp_grid_levels {
  id                 Int      @id @default(autoincrement())
  str_id             String?  @unique @default(dbgenerated("\"substring\"(md5((random())::text), 0, 21)"))
  account_id         String?  @db.VarChar(36)
  uid                String?
  state              String   @default("active")
  access             String?  @default("account")
  type               String?  @db.VarChar(36)
  created_at         DateTime @default(now()) @db.Timestamp(6)
  created_by         String?  @db.VarChar(36)
  created_proxied_by String?  @db.VarChar(36)
  updated_at         DateTime @default(now()) @db.Timestamp(6)
  updated_by         String?  @db.VarChar(36)
  updated_proxied_by String?  @db.VarChar(36)

  comp_grid_id   Int?
  order_index    Int?
  effective_date DateTime? @db.Timestamp(6)
  name           String?
  notes          String?
  sync_id        String?
  sync_worker    String?

  comp_grid          comp_grids?  @relation(fields: [comp_grid_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
  related_comp_grids comp_grids[] @relation("ManyToManyCompGridLevelToCompGrid")

  comp_grid_rates                          comp_grid_rates[]
  payee_agent_commission_schedule_profiles agent_commission_schedule_profiles[] @relation("payee_comp_grid_level")
  payer_agent_commission_schedule_profiles agent_commission_schedule_profiles[] @relation("payer_comp_grid_level")

  @@unique([account_id, sync_id, state, order_index])
  @@index([id, state], name: "idx_comp_grid_levels_id_state")
}

model comp_grid_products {
  id                 Int      @id @default(autoincrement())
  str_id             String?  @unique() @default(dbgenerated("\"substring\"(md5((random())::text), 0, 21)"))
  account_id         String?  @db.VarChar(36)
  uid                String?
  state              String   @default("active")
  access             String?  @default("account")
  created_at         DateTime @default(now()) @db.Timestamp(6)
  created_by         String?  @db.VarChar(36)
  created_proxied_by String?  @db.VarChar(36)
  updated_at         DateTime @default(now()) @db.Timestamp(6)
  updated_by         String?  @db.VarChar(36)
  updated_proxied_by String?  @db.VarChar(36)

  comp_grid_id Int
  name         String?
  type         String?
  notes        String?
  sync_id      String?
  sync_worker  String?

  comp_grid          comp_grids           @relation(fields: [comp_grid_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
  company_products   company_products[]
  comp_grid_criteria comp_grid_criteria[]

  @@unique([account_id, sync_id, state])
}

model comp_grid_rates {
  id                 Int      @id @default(autoincrement())
  str_id             String?  @unique @default(dbgenerated("\"substring\"(md5((random())::text), 0, 21)"))
  account_id         String?  @db.VarChar(36)
  uid                String?
  state              String   @default("active")
  access             String?  @default("account")
  created_at         DateTime @default(now()) @db.Timestamp(6)
  created_by         String?  @db.VarChar(36)
  created_proxied_by String?  @db.VarChar(36)
  updated_at         DateTime @default(now()) @db.Timestamp(6)
  updated_by         String?  @db.VarChar(36)
  updated_proxied_by String?  @db.VarChar(36)

  bonus_rate             Decimal? @db.Decimal
  broker                 String?
  carrier_rate           Decimal? @db.Decimal
  comp_grid_criterion_id Int
  comp_grid_level_id     Int
  contract_code          String?
  house_rate             Decimal? @db.Decimal
  pass_through_rate      Decimal? @db.Decimal
  rate                   Decimal? @db.Decimal
  sync_id                String?
  sync_worker            String?

  comp_grid_criterion comp_grid_criteria @relation(fields: [comp_grid_criterion_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
  comp_grid_level     comp_grid_levels   @relation(fields: [comp_grid_level_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
  date_ranges         date_ranges[]      @relation("CompGridRatesDateRanges")

  @@unique([account_id, sync_id, state])
  @@index([account_id, state, comp_grid_criterion_id])
  @@index([account_id, state, comp_grid_level_id])
  @@index([state, comp_grid_level_id])
  @@index([account_id, state, comp_grid_criterion_id, comp_grid_level_id, id], name: "idx_comp_grid_rates_query_opt")
  @@index([state, comp_grid_level_id], name: "idx_comp_grid_rates_state_level")
}

model comp_grids {
  id                 Int      @id @default(autoincrement())
  str_id             String?  @unique @default(dbgenerated("\"substring\"(md5((random())::text), 0, 21)"))
  uid                String?
  account_id         String?  @db.VarChar(36)
  state              String   @default("active")
  access             String?  @default("account")
  created_at         DateTime @default(now()) @db.Timestamp(6)
  created_by         String?  @db.VarChar(36)
  created_proxied_by String?  @db.VarChar(36)
  updated_at         DateTime @default(now()) @db.Timestamp(6)
  updated_by         String?  @db.VarChar(36)
  updated_proxied_by String?  @db.VarChar(36)

  company_id  Int
  name        String?
  notes       String?
  rate_fields Json?
  sync_id     String?
  sync_worker String?

  comp_grid_levels         comp_grid_levels[]
  related_comp_grid_levels comp_grid_levels[] @relation("ManyToManyCompGridLevelToCompGrid")

  agent_commission_schedule_profiles agent_commission_schedule_profiles[]
  comp_grid_criteria                 comp_grid_criteria[]
  comp_grid_products                 comp_grid_products[]
  company                            companies                            @relation(fields: [company_id], references: [id], onDelete: NoAction, onUpdate: NoAction)

  @@unique([account_id, sync_id, state])
}

model companies {
  id                 Int      @id @default(autoincrement())
  str_id             String?  @unique(map: "companies_str_id_unique") @default(dbgenerated("\"substring\"(md5((random())::text), 0, 21)"))
  account_id         String?  @db.VarChar(36)
  uid                String?
  state              String   @default("active")
  access             String?  @default("account")
  created_at         DateTime @default(now()) @db.Timestamp(6)
  created_by         String?  @db.VarChar(36)
  created_proxied_by String?  @db.VarChar(36)
  updated_at         DateTime @default(now()) @db.Timestamp(6)
  updated_by         String?  @db.VarChar(36)
  updated_proxied_by String?  @db.VarChar(36)

  address      String?
  alias_list   String[] @default([])
  canonical_id Int?
  company_id   String?
  company_name String?
  config       Json?
  email        String?
  group_id     String?
  log          Json?
  notes        String?
  phone        String?
  sync_id      String?
  sync_worker  String?
  type         Json?
  website      String?

  accounting_transaction_details     accounting_transaction_details[]
  agent_commission_schedule_profiles agent_commission_schedule_profiles[]
  agent_incentive_tiers              agent_incentive_tiers[]
  canonical_company                  companies?                           @relation("canonical_company", fields: [canonical_id], references: [id], onDelete: SetNull, onUpdate: Cascade)
  carrier_commission_schedules       commission_schedules[]               @relation("carrier_companies")
  child_companies                    companies[]                          @relation("canonical_company")
  comp_grid_criteria                 comp_grid_criteria[]
  comp_grids                         comp_grids[]
  companies_processors               companies_processors[]
  company_mappings                   company_mappings[]
  company_products                   company_products[]
  documents                          documents[]
  imports                            data_imports[]
  mappings                           mappings[]
  paying_entity_commission_schedules commission_schedules[]               @relation("paying_entity_companies")
  processors                         processors[]                         @relation("companies_processors")
  processors_old                     processors[]
  prompts                            prompts[]
  companies_document_profiles        companies_document_profiles[]

  @@unique([account_id, sync_id, state])
}

model company_mappings {
  id                 Int      @id @default(autoincrement())
  str_id             String?  @unique(map: "company_mappings_str_id_unique") @default(dbgenerated("\"substring\"(md5((random())::text), 0, 21)"))
  account_id         String?  @db.VarChar(36)
  state              String   @default("active")
  created_at         DateTime @default(now()) @db.Timestamp(6)
  created_by         String?  @db.VarChar(36)
  created_proxied_by String?  @db.VarChar(36)
  updated_at         DateTime @default(now()) @db.Timestamp(6)
  updated_by         String?  @db.VarChar(36)
  updated_proxied_by String?  @db.VarChar(36)

  company_id  Int
  record_id   Int
  record_type String

  company companies @relation(fields: [company_id], references: [id], onDelete: Cascade)

  @@unique([company_id, record_id, record_type], map: "company_mappings_unique")
  @@index([company_id], map: "index_company_id")
  @@index([record_id, record_type], map: "index_record_type")
  @@index([account_id], map: "index_account_id")
}

model company_products {
  id                 Int      @id @default(autoincrement())
  str_id             String?  @unique() @default(dbgenerated("\"substring\"(md5((random())::text), 0, 21)"))
  account_id         String?  @db.VarChar(36)
  uid                String?
  state              String   @default("active")
  created_at         DateTime @default(now()) @db.Timestamp(6)
  created_by         String?  @db.VarChar(36)
  created_proxied_by String?  @db.VarChar(36)
  updated_at         DateTime @default(now()) @db.Timestamp(6)
  updated_by         String?  @db.VarChar(36)
  updated_proxied_by String?  @db.VarChar(36)

  company_id       Int
  config           Json?
  notes            String?
  product_name     String?
  product_sub_type String?
  product_type     String?
  sync_id          String?
  sync_worker      String?

  company                 companies                 @relation(fields: [company_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "fk_company_products_company_id")
  company_product_options company_product_options[]
  comp_grid_products      comp_grid_products[]
  report_data             report_data[]

  @@unique([account_id, sync_id, state])
}

model company_product_options {
  id                 Int      @id @default(autoincrement())
  str_id             String?  @unique() @default(dbgenerated("\"substring\"(md5((random())::text), 0, 21)"))
  account_id         String?  @db.VarChar(36)
  uid                String?
  state              String   @default("active")
  created_at         DateTime @default(now()) @db.Timestamp(6)
  created_by         String?  @db.VarChar(36)
  created_proxied_by String?  @db.VarChar(36)
  updated_at         DateTime @default(now()) @db.Timestamp(6)
  updated_by         String?  @db.VarChar(36)
  updated_proxied_by String?  @db.VarChar(36)

  product_id Int
  name       String?
  notes      String?

  product company_products @relation(fields: [product_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "fk_company_product_options_product_id")
}

model compensation_reports_config {
  id                 Int      @id @default(autoincrement())
  str_id             String?  @unique() @default(dbgenerated("\"substring\"(md5((random())::text), 0, 21)"))
  account_id         String?  @unique() @db.VarChar(36)
  state              String   @default("active")
  created_at         DateTime @default(now()) @db.Timestamp(6)
  created_by         String?  @db.VarChar(36)
  created_proxied_by String?  @db.VarChar(36)
  updated_at         DateTime @default(now()) @db.Timestamp(6)
  updated_by         String?  @db.VarChar(36)
  updated_proxied_by String?  @db.VarChar(36)

  custom_terms_text   String?
  enable_custom_terms Boolean? @default(false)
}

model contact_groups {
  id                 Int      @id @default(autoincrement())
  str_id             String?  @unique @default(dbgenerated("\"substring\"(md5((random())::text), 0, 21)"))
  account_id         String?  @db.VarChar(36)
  uid                String?
  state              String   @default("active")
  created_at         DateTime @default(now()) @db.Timestamp(6)
  created_by         String?  @db.VarChar(36)
  created_proxied_by String?  @db.VarChar(36)
  updated_at         DateTime @default(now()) @db.Timestamp(6)
  updated_by         String?  @db.VarChar(36)
  updated_proxied_by String?  @db.VarChar(36)

  name  String?
  notes String?

  contacts  contacts[]
  hierarchy contact_hierarchy[]
}

model contact_hierarchy {
  id                 Int      @id @default(autoincrement())
  account_id         String?  @db.VarChar(36)
  state              String   @default("active")
  created_at         DateTime @default(now()) @db.Timestamp(6)
  created_by         String?  @db.VarChar(36)
  created_proxied_by String?  @db.VarChar(36)
  updated_at         DateTime @default(now()) @db.Timestamp(6)
  updated_by         String?  @db.VarChar(36)
  updated_proxied_by String?  @db.VarChar(36)

  config              Json?
  contact_group_id    Int?
  contact_id          Int
  end_date            DateTime?
  hierarchy_master_id Int?
  parent_id           Int?
  split_percentage    Decimal?
  start_date          DateTime?
  sync_id             String?
  sync_worker         String?

  contact      contacts        @relation(fields: [contact_id], references: [id])
  contactGroup contact_groups? @relation(fields: [contact_group_id], references: [id])
  parent       contacts?       @relation("ParentToChild", fields: [parent_id], references: [id])

  @@unique([account_id, sync_id, state])
  @@index([contact_id])
}

model contact_levels {
  id                 Int      @id @default(autoincrement())
  account_id         String?  @db.VarChar(36)
  state              String   @default("active")
  created_at         DateTime @default(now()) @db.Timestamp(6)
  created_by         String?  @db.VarChar(36)
  created_proxied_by String?  @db.VarChar(36)
  updated_at         DateTime @default(now()) @db.Timestamp(6)
  updated_by         String?  @db.VarChar(36)
  updated_proxied_by String?  @db.VarChar(36)

  agency_contact_id Int?
  company_id        Int?
  config            Json?
  contact_id        Int?
  end_date          DateTime?
  level             String?
  level_label       String?   @db.VarChar(100)
  loa               Boolean?
  product_type      String?   @db.VarChar(36)
  start_date        DateTime?
  sync_id           String?
  sync_worker       String?

  agency_contact  contacts? @relation("agency_contact_levels", fields: [agency_contact_id], references: [id])
  carrier_contact contacts? @relation("carrier_levels", fields: [contact_id], references: [id])

  @@unique([account_id, sync_id, state])
}

model contact_memos {
  id                 Int      @id @default(autoincrement())
  account_id         String?  @db.VarChar(36)
  state              String   @default("active")
  created_at         DateTime @default(now()) @db.Timestamp(6)
  created_by         String?  @db.VarChar(36)
  created_proxied_by String?  @db.VarChar(36)
  updated_at         DateTime @default(now()) @db.Timestamp(6)
  updated_by         String?  @db.VarChar(36)
  updated_proxied_by String?  @db.VarChar(36)

  approver_contact_id Int?
  approver_name       String?
  contact_id          Int
  description         String?
  end_date            DateTime?
  recorded_by_user_id String?
  remarks             String?
  start_date          DateTime?

  approver contacts? @relation("approver", fields: [approver_contact_id], references: [id])
  contact  contacts  @relation(fields: [contact_id], references: [id])
}

model contact_referrals {
  id                 Int      @id @default(autoincrement())
  account_id         String?  @db.VarChar(36)
  state              String   @default("active")
  created_at         DateTime @default(now()) @db.Timestamp(6)
  created_by         String?  @db.VarChar(36)
  created_proxied_by String?  @db.VarChar(36)
  updated_at         DateTime @default(now()) @db.Timestamp(6)
  updated_by         String?  @db.VarChar(36)
  updated_proxied_by String?  @db.VarChar(36)

  config              Json?
  contact_id          Int
  end_date            DateTime?
  notes               String?
  payer_contact_id    Int?
  referrer_contact_id Int?
  remarks             String?
  start_date          DateTime?
  type                String?

  contact  contacts  @relation(fields: [contact_id], references: [id])
  referrer contacts? @relation("referrer", fields: [referrer_contact_id], references: [id])
  payer    contacts? @relation("payer", fields: [payer_contact_id], references: [id])
}

model contacts {
  id                 Int      @id @default(autoincrement())
  str_id             String?  @unique(map: "contacts_str_id_unique") @default(dbgenerated("\"substring\"(md5((random())::text), 0, 21)"))
  account_id         String?  @db.VarChar(36)
  uid                String?
  state              String   @default("active")
  created_at         DateTime @default(now()) @db.Timestamp(6)
  created_by         String?  @db.VarChar(36)
  created_proxied_by String?  @db.VarChar(36)
  updated_at         DateTime @default(now()) @db.Timestamp(6)
  updated_by         String?  @db.VarChar(36)
  updated_proxied_by String?  @db.VarChar(36)

  account_role_settings_id Int?
  agent_code               String?
  balance                  Decimal?  @db.Decimal
  bank_info                String?
  birthday                 DateTime? @db.Timestamp(6)
  city                     String?
  company_name             String?
  config                   Json?
  contact_group_id         Int?
  country                  String?
  customer_id              Int?
  email                    String?
  first_name               String?
  gender                   String?
  geo_state                String?
  last_name                String?
  level                    String?
  log                      Json?
  middle_name              String?
  name                     String?
  nickname                 String?
  notes                    String?
  payable_status           String?
  phone                    String?
  phone_type               String?
  phone2                   String?
  phone2_type              String?
  reports                  String[]  @default([])
  status                   String?
  sync_id                  String?
  sync_worker              String?
  title                    String?
  type                     Json?
  user_str_id              String?   @db.VarChar(36)
  zip                      String?

  account_role_settings                            account_role_settings?                             @relation(fields: [account_role_settings_id], references: [id])
  accounting_transaction_details                   accounting_transaction_details[]
  accounting_transactions                          accounting_transactions[]
  agency_contact_levels                            contact_levels[]                                   @relation("agency_contact_levels")
  child_relationships                              contact_hierarchy[]                                @relation("ParentToChild")
  contact_approver_memos                           contact_memos[]                                    @relation("approver")
  contact_group                                    contact_groups?                                    @relation(fields: [contact_group_id], references: [id])
  contact_level                                    contact_levels[]                                   @relation("carrier_levels")
  contact_memos                                    contact_memos[]
  contact_referrals                                contact_referrals[]
  contact_referrals_payer                          contact_referrals[]                                @relation("payer")
  contact_referrers                                contact_referrals[]                                @relation("referrer")
  contacts_agent_commission_schedule_profiles      contacts_agent_commission_schedule_profiles[]
  contacts_agent_commission_schedule_profiles_sets contacts_agent_commission_schedule_profiles_sets[]
  customer                                         customers?                                         @relation(fields: [customer_id], references: [id])
  parent_relationships                             contact_hierarchy[]
  report_contacts                                  report_contacts[]
  saved_reports                                    saved_reports[]
  user_contact                                     users?                                             @relation("user_contact", fields: [user_str_id], references: [str_id], onDelete: NoAction, onUpdate: NoAction, map: "fk_contact_user_id")

  @@unique([account_id, user_str_id])
  @@unique([account_id, sync_id, state])
  @@index([agent_code])
  @@index([email])
  @@index([first_name])
  @@index([last_name])
}

model contacts_agent_commission_schedule_profiles {
  str_id             String   @id @default(dbgenerated("\"substring\"(md5((random())::text), 0, 21)"))
  account_id         String?  @db.VarChar(36)
  uid                String
  state              String   @default("active")
  created_at         DateTime @default(now()) @db.Timestamp(6)
  created_by         String?  @db.VarChar(36)
  created_proxied_by String?  @db.VarChar(36)
  updated_at         DateTime @default(now()) @db.Timestamp(6)
  updated_by         String?  @db.VarChar(36)
  updated_proxied_by String?  @db.VarChar(36)

  agent_commission_schedule_profile_id Int
  config                               Json?
  contact_id                           Int
  downline_group_contact_id            Int?
  end_date                             DateTime? @db.Timestamp(6)
  hierarchy_processing                 String?
  method                               String?
  multiplier                           Decimal?  @db.Decimal
  payee_id                             Int?
  rate                                 Decimal?  @db.Decimal
  start_date                           DateTime? @db.Timestamp(6)

  agent_commission_schedule_profile agent_commission_schedule_profiles? @relation(fields: [agent_commission_schedule_profile_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
  contact                           contacts?                           @relation(fields: [contact_id], references: [id], onDelete: NoAction, onUpdate: NoAction)

  // @@id([contact_id, agent_commission_schedule_profile_id])
  @@unique([contact_id, agent_commission_schedule_profile_id, str_id])
}

model customers {
  id                 Int      @id @default(autoincrement())
  str_id             String?  @unique @default(dbgenerated("\"substring\"(md5((random())::text), 0, 21)"))
  account_id         String?  @db.VarChar(36)
  state              String   @default("active")
  created_at         DateTime @default(now()) @db.Timestamp(6)
  created_by         String?  @db.VarChar(36)
  created_proxied_by String?  @db.VarChar(36)
  updated_at         DateTime @default(now()) @db.Timestamp(6)
  updated_by         String?  @db.VarChar(36)
  updated_proxied_by String?  @db.VarChar(36)

  address      Json?
  company_name String?
  config       Json?
  dob          DateTime?
  email        String?
  end_date     DateTime?
  first_name   String?
  gender       customer_gender?
  group_id     String?
  last_name    String?
  middle_name  String?
  nickname     String?
  phone        String?
  start_date   DateTime?
  status       String?
  sync_id      String?
  sync_worker  String?
  type         customer_type?
  website      String?

  accounting_transaction_details accounting_transaction_details[]
  contacts                       contacts[]
  report_data                    report_data[]

  @@unique([account_id, sync_id, state])
}

model dashboards {
  id                 Int      @id @default(autoincrement())
  str_id             String?  @unique @default(dbgenerated("\"substring\"(md5((random())::text), 0, 21)"))
  account_id         String?  @db.VarChar(36)
  state              String   @default("active")
  uid                String?
  created_at         DateTime @default(now()) @db.Timestamp(6)
  created_by         String?  @db.VarChar(36)
  created_proxied_by String?  @db.VarChar(36)
  updated_at         DateTime @default(now()) @db.Timestamp(6)
  updated_by         String?  @db.VarChar(36)
  updated_proxied_by String?  @db.VarChar(36)

  name         String?
  widgets      Json?
  access       String?
  access_roles String[] @default([])
  label        String?
}

model data_update_actions {
  id                 Int      @id @default(autoincrement())
  str_id             String?  @unique @default(dbgenerated("\"substring\"(md5((random())::text), 0, 21)"))
  account_id         String?  @db.VarChar(36)
  state              String   @default("active")
  access             String?  @default("account")
  created_at         DateTime @default(now()) @db.Timestamp(6)
  created_by         String?  @db.VarChar(36)
  created_proxied_by String?  @db.VarChar(36)
  updated_at         DateTime @default(now()) @db.Timestamp(6)
  updated_by         String?  @db.VarChar(36)
  updated_proxied_by String?  @db.VarChar(36)

  data_entity                String?
  data_update_actions        Json?
  data_update_actions_params String?
  name                       String?
  notes                      String?

  data_update_config data_update_config[] @relation("data_update_actions")
}

model data_update_config {
  id                 Int      @id @default(autoincrement())
  str_id             String?  @unique @default(dbgenerated("\"substring\"(md5((random())::text), 0, 21)"))
  account_id         String?  @db.VarChar(36)
  state              String   @default("active")
  access             String?  @default("account")
  created_at         DateTime @default(now()) @db.Timestamp(6)
  created_by         String?  @db.VarChar(36)
  created_proxied_by String?  @db.VarChar(36)
  updated_at         DateTime @default(now()) @db.Timestamp(6)
  updated_by         String?  @db.VarChar(36)
  updated_proxied_by String?  @db.VarChar(36)

  data_entity      String?
  flag_config_mode Boolean? @default(false)
  group            String?
  name             String?
  notes            String?

  data_update_criteria data_update_criteria[] @relation("data_update_criteria")
  data_update_actions  data_update_actions[]  @relation("data_update_actions")
  validation_results   validation_results[]   @relation("DataUpdateConfigToValidationResults")
}

model data_update_criteria {
  id                 Int      @id @default(autoincrement())
  str_id             String?  @unique @default(dbgenerated("\"substring\"(md5((random())::text), 0, 21)"))
  account_id         String?  @db.VarChar(36)
  state              String   @default("active")
  access             String?  @default("account")
  created_at         DateTime @default(now()) @db.Timestamp(6)
  created_by         String?  @db.VarChar(36)
  created_proxied_by String?  @db.VarChar(36)
  updated_at         DateTime @default(now()) @db.Timestamp(6)
  updated_by         String?  @db.VarChar(36)
  updated_proxied_by String?  @db.VarChar(36)

  custom_data_update_criteria        String?
  custom_data_update_criteria_params String?
  custom_data_update_criteria_mode   Boolean? @default(false)
  data_entity                        String?
  data_update_criteria               Json?
  data_update_criteria_params        Json?
  name                               String?
  notes                              String?

  data_update_config data_update_config[] @relation("data_update_criteria")
}

model data_imports {
  id                 Int      @id @default(autoincrement())
  str_id             String?  @unique @default(dbgenerated("\"substring\"(md5((random())::text), 0, 21)"))
  account_id         String?  @db.VarChar(36)
  state              String   @default("active")
  created_at         DateTime @default(now()) @db.Timestamp(6)
  created_by         String?  @db.VarChar(36)
  created_proxied_by String?  @db.VarChar(36)
  updated_at         DateTime @default(now()) @db.Timestamp(6)
  updated_by         String?  @db.VarChar(36)
  updated_proxied_by String?  @db.VarChar(36)

  company_str_id         String?
  count                  Int?
  document_str_id        String?
  feedback               String?
  import_method          String?            @db.VarChar(32)
  metadata               Json
  notes                  String?
  output                 Json?
  process_action_records Json?
  process_count          Int?
  process_duration       Decimal?           @db.Decimal
  processor_str_id       String?
  mapping_str_id         String?
  statement_total_amount Decimal?
  stats                  Json?
  status                 String?
  summed_total_amount    Decimal?
  type                   data_imports_type?

  company   companies?  @relation(fields: [company_str_id], references: [str_id], onDelete: SetNull, onUpdate: Cascade)
  document  documents?  @relation(fields: [document_str_id], references: [str_id], onDelete: SetNull, onUpdate: Cascade)
  processor processors? @relation(fields: [processor_str_id], references: [str_id], onDelete: SetNull, onUpdate: Cascade)
  mapping   mappings?   @relation(fields: [mapping_str_id], references: [str_id], onDelete: SetNull, onUpdate: Cascade)
}

model data_processing {
  id                 Int      @id @default(autoincrement())
  str_id             String?  @unique @default(dbgenerated("\"substring\"(md5((random())::text), 0, 21)"))
  account_id         String?  @db.VarChar(36)
  state              String   @default("active")
  created_at         DateTime @default(now()) @db.Timestamp(6)
  created_by         String?  @db.VarChar(36)
  created_proxied_by String?  @db.VarChar(36)
  updated_at         DateTime @default(now()) @db.Timestamp(6)
  updated_by         String?  @db.VarChar(36)
  updated_proxied_by String?  @db.VarChar(36)

  duration      Decimal?             @db.Decimal
  failed        Int?
  master_str_id String?              @db.VarChar(36)
  notes         String?
  output        Json?
  params        String?
  stats         Json?
  status        String
  success       Int?
  total         Int?
  type          data_processing_type
  worker        String?

  account    accounts?         @relation(fields: [account_id], references: [str_id])
  jobs       data_processing[] @relation("master")
  master     data_processing?  @relation("master", fields: [master_str_id], references: [str_id], onDelete: NoAction, onUpdate: NoAction)
  proxy_user users?            @relation("data_processing_proxy_user", fields: [created_proxied_by], references: [uid])
  user       users?            @relation("data_processing_user", fields: [created_by], references: [uid])
}

model date_ranges {
  id                 Int      @id @default(autoincrement())
  account_id         String?  @db.VarChar(36)
  state              String   @default("active")
  created_at         DateTime @default(now()) @db.Timestamp(6)
  created_by         String?  @db.VarChar(36)
  created_proxied_by String?  @db.VarChar(36)
  updated_at         DateTime @default(now()) @db.Timestamp(6)
  updated_by         String?  @db.VarChar(36)
  updated_proxied_by String?  @db.VarChar(36)

  config      Json?
  end_date    DateTime?
  name        String?
  notes       String?
  start_date  DateTime?
  sync_id     String?
  sync_worker String?
  type        DateRangesTypes?

  comp_grid_criteria comp_grid_criteria[] @relation("CompGridCriteriaDateRanges")
  comp_grid_rates    comp_grid_rates[]    @relation("CompGridRatesDateRanges")

  @@unique([account_id, sync_id, state])
  @@index([id, state, start_date, end_date], name: "idx_date_ranges_id_state_dates")
}

model document_profiles {
  id                 Int      @id @default(autoincrement())
  str_id             String?  @unique @default(dbgenerated("\"substring\"(md5((random())::text), 0, 21)"))
  uid                String?
  account_id         String?  @db.VarChar(36)
  state              String   @default("active")
  created_at         DateTime @default(now()) @db.Timestamp(6)
  created_by         String?  @db.VarChar(36)
  created_proxied_by String?  @db.VarChar(36)
  updated_at         DateTime @default(now()) @db.Timestamp(6)
  updated_by         String?  @db.VarChar(36)
  updated_proxied_by String?  @db.VarChar(36)

  name          String?
  notes         String?
  owner         String?
  status        String?
  priority      Int?
  file_link     String[]
  paying_entity String?
  carrier_name  String?

  documents                   documents[]
  processors                  processors[]
  mappings                    mappings[]
  prompts                     prompts[]
  companies_document_profiles companies_document_profiles[]
}

model companies_document_profiles {
  id                 Int      @id @default(autoincrement())
  str_id             String?  @unique @default(dbgenerated("\"substring\"(md5((random())::text), 0, 21)"))
  account_id         String?  @db.VarChar(36)
  state              String   @default("active")
  created_at         DateTime @default(now()) @db.Timestamp(6)
  created_by         String?  @db.VarChar(36)
  created_proxied_by String?  @db.VarChar(36)
  updated_at         DateTime @default(now()) @db.Timestamp(6)
  updated_by         String?  @db.VarChar(36)
  updated_proxied_by String?  @db.VarChar(36)

  document_profile_str_id String
  company_str_id          String
  auto_mapping_id         String?

  document_profile document_profiles @relation(fields: [document_profile_str_id], references: [str_id], onDelete: Cascade)
  company          companies         @relation(fields: [company_str_id], references: [str_id], onDelete: Cascade)
  auto_mapping     mappings?         @relation("CompanyDocumentProfileAutoMapping", fields: [auto_mapping_id], references: [str_id], onDelete: SetNull)

  @@unique([document_profile_str_id, company_str_id])
  @@index([document_profile_str_id])
  @@index([company_str_id])
  @@index([account_id])
  @@index([auto_mapping_id])
}

model documents {
  id                 Int       @id(map: "uploads_pkey") @default(autoincrement())
  str_id             String?   @unique @default(dbgenerated("\"substring\"(md5((random())::text), 0, 21)"))
  account_id         String?   @db.VarChar(36)
  uid                String?
  state              String    @default("active")
  created_at         DateTime  @default(now()) @db.Timestamp(6)
  created_by         String?   @db.VarChar(36)
  created_proxied_by String?   @db.VarChar(36)
  updated_at         DateTime  @default(now()) @db.Timestamp(6)
  imported_at        DateTime?
  updated_by         String?   @db.VarChar(36)
  updated_proxied_by String?   @db.VarChar(36)

  bank_total_amount   Decimal?
  check_date          DateTime?
  company_str_id      String?
  deposit_date        DateTime?
  file_hash           String?
  file_path           String?
  file_type           file_type?
  filename            String?
  import_id           String?
  mapping             String?
  method              String?
  notes               String?
  optimized_file_hash String?
  optimized_file_path String?
  optimized_filename  String?
  override_file_hash  String?
  override_file_path  String?
  override_filename   String?
  override_mapping    String?
  paycycle_date       DateTime?
  payment_method      String?
  predicted_result    Json?
  process_method      String?
  processor           String?
  profile_str_id      String?
  prompt              String?
  statement_amount    Decimal?
  statement_month     DateTime?
  status              String     @default("new")
  sync_id             String?
  sync_worker         String?
  tag                 String?
  type                String?
  upload_source       String?
  validations         Json?
  processing_task_id  String?    @db.VarChar(36)
 
  account         accounts?          @relation(fields: [account_id], references: [str_id])
  companies       companies?         @relation(fields: [company_str_id], references: [str_id], onDelete: NoAction, onUpdate: NoAction, map: "uploads_company_str_id_fkey")
  extractions     extractions[]
  imports         data_imports[]
  profiles        document_profiles? @relation(fields: [profile_str_id], references: [str_id], onDelete: NoAction, onUpdate: NoAction, map: "uploads_profile_str_id_fkey")
  report_data     report_data[]
  statement_data  statement_data[]
  created_by_user users?             @relation("documents_created_byTousers", fields: [created_by], references: [uid], onDelete: NoAction, onUpdate: NoAction, map: "fk_uploads_created_by")
  updated_by_user users?             @relation("documents_updated_byTousers", fields: [updated_by], references: [uid], onDelete: NoAction, onUpdate: NoAction, map: "fk_uploads_updated_by")

  @@unique([account_id, sync_id, state])
}

model extractions {
  id                 Int      @id @default(autoincrement())
  str_id             String   @default(dbgenerated("\"substring\"(md5((random())::text), 0, 21)"))
  account_id         String?  @db.VarChar(36)
  uid                String
  state              String   @default("active")
  created_at         DateTime @default(now()) @db.Timestamp(6)
  created_by         String?  @db.VarChar(36)
  created_proxied_by String?  @db.VarChar(36)
  updated_at         DateTime @default(now()) @db.Timestamp(6)
  updated_by         String?  @db.VarChar(36)
  updated_proxied_by String?  @db.VarChar(36)

  document_id    Int
  method         String
  method_options Json?
  output         Json?
  output_format  String?
  result         String?
  result_id      String?

  documents  documents          @relation(fields: [document_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
  processors processors[]
  status     extractions_status @default(new)

  @@index([document_id], map: "document_id")
}

model fields {
  id                 Int      @id @default(autoincrement())
  str_id             String?  @unique() @default(dbgenerated("\"substring\"(md5((random())::text), 0, 21)"))
  state              String   @default("active")
  created_at         DateTime @default(now()) @db.Timestamp(6)
  created_by         String?  @db.VarChar(36)
  created_proxied_by String?  @db.VarChar(36)
  updated_at         DateTime @default(now()) @db.Timestamp(6)
  updated_by         String?  @db.VarChar(36)
  updated_proxied_by String?  @db.VarChar(36)

  copyable     Boolean  @default(false)
  description  String?
  enabled_for  Json?
  formatter    String?
  key          String?
  label        Json?
  matches      String[] @default([])
  model        String?
  normalizer   String?
  notes        String?
  options      String[] @default([])
  required_for Json?
  type         String?
}

// This table contains check constraints and requires additional setup for migrations. Visit https://pris.ly/d/check-constraints for more info.
model history {
  id                 Int      @id @default(autoincrement())
  account_id         String?  @db.VarChar(36)
  uid                String?  @db.VarChar(255)
  created_at         DateTime @default(now()) @db.Timestamp(6)
  created_by         String?  @db.VarChar(36)
  created_proxied_by String?  @db.VarChar(36)
  updated_at         DateTime @default(now()) @db.Timestamp(6)
  updated_by         String?  @db.VarChar(36)
  updated_proxied_by String?  @db.VarChar(36)

  json_data         String?
  report_data_id    Int?
  statement_data_id Int?

  report_data    report_data?    @relation(fields: [report_data_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
  state          history_state?
  statement_data statement_data? @relation(fields: [statement_data_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
  status         history_status?
  table_type     history_type?

  @@index([report_data_id])
  @@index([statement_data_id])
}

model grouping_rules {
  id                 Int      @id @default(autoincrement())
  str_id             String   @unique @default(dbgenerated("\"substring\"(md5((random())::text), 0, 21)"))
  account_id         String?  @db.VarChar(36)
  state              String   @default("active")
  created_at         DateTime @default(now()) @db.Timestamp(6)
  created_by         String?  @db.VarChar(36)
  created_proxied_by String?  @db.VarChar(36)
  updated_at         DateTime @default(now()) @db.Timestamp(6)
  updated_by         String?  @db.VarChar(36)
  updated_proxied_by String?  @db.VarChar(36)

  config        Json?
  entity        String?
  filter        Json?
  key_condition Json?
  name          String
  notes         String?
  method        String?

  @@index([account_id])
}

model mappings {
  id                 Int      @id @default(autoincrement())
  str_id             String?  @unique() @default(dbgenerated("\"substring\"(md5((random())::text), 0, 21)"))
  account_id         String?  @db.VarChar(36)
  uid                String?
  state              String   @default("active")
  access             String?  @default("account")
  created_at         DateTime @default(now()) @db.Timestamp(6)
  created_by         String?  @db.VarChar(36)
  created_proxied_by String?  @db.VarChar(36)
  updated_at         DateTime @default(now()) @db.Timestamp(6)
  updated_by         String?  @db.VarChar(36)
  updated_proxied_by String?  @db.VarChar(36)

  carrier_id    String?
  mapping       Json?
  modify_status String  @default("new")
  name          String?
  type          String?

  company                     companies?                    @relation(fields: [carrier_id], references: [str_id], onDelete: NoAction, onUpdate: NoAction, map: "fk_mappings_company_id")
  document_profile            document_profiles[]
  companies_document_profiles companies_document_profiles[] @relation("CompanyDocumentProfileAutoMapping")
  imports                     data_imports[]
}

model plans {
  id                 Int      @id(map: "users_pkey") @default(autoincrement())
  uid                String?  @unique(map: "uid_unique")
  state              String   @default("active")
  created_at         DateTime @default(now()) @db.Timestamp(6)
  created_by         String?  @db.VarChar(36)
  created_proxied_by String?  @db.VarChar(36)
  updated_at         DateTime @default(now()) @db.Timestamp(6)
  updated_by         String?  @db.VarChar(36)
  updated_proxied_by String?  @db.VarChar(36)

  plan_name    String?
  stripe_id    String?
  subscription Json?
}

model processors {
  id                 Int      @id @default(autoincrement())
  str_id             String?  @unique() @default(dbgenerated("\"substring\"(md5((random())::text), 0, 21)"))
  account_id         String?  @db.VarChar(36)
  uid                String?
  state              String   @default("active")
  created_at         DateTime @default(now()) @db.Timestamp(6)
  created_by         String?  @db.VarChar(36)
  created_proxied_by String?  @db.VarChar(36)
  updated_at         DateTime @default(now()) @db.Timestamp(6)
  updated_by         String?  @db.VarChar(36)
  updated_proxied_by String?  @db.VarChar(36)
  owner              String?  @db.VarChar(36)

  access           String?                  @default("account")
  company_id       String?
  document_str_id  String?                  @db.VarChar
  extract_ids      Int[]                    @default([])
  extract_str_ids  String[]                 @default([])
  extractionsid    Int?
  import_status    String?                  @default("none") @db.VarChar(32)
  inner_name       String?
  method           document_extract_method?
  name             String?
  notes            String?
  processor        String?
  processor_status String?                  @default("new")
  reviewed_at      DateTime?                @db.Timestamp(6)
  reviewed_by      String?                  @db.VarChar
  reviewer_id      Int?
  reviewer_str_id  String?                  @db.VarChar
  status           String                   @default("draft")
  suggest_for      String?
  type             String?

  account                             accounts?              @relation(fields: [account_id], references: [str_id])
  companies                           companies[]            @relation("companies_processors")
  companies_old                       companies?             @relation(fields: [company_id], references: [str_id], onDelete: NoAction, onUpdate: NoAction, map: "fk_processors_company_id")
  companies_processors                companies_processors[]
  extractions                         extractions?           @relation(fields: [extractionsid], references: [id], onDelete: NoAction, onUpdate: NoAction)
  file_type                           file_type?
  imports                             data_imports[]
  document_profiles                   document_profiles[]
  users_processors_created_byTousers  users?                 @relation("processors_created_byTousers", fields: [created_by], references: [uid], onDelete: NoAction, onUpdate: NoAction, map: "fk_processors_created_by")
  users_processors_reviewed_byTousers users?                 @relation("processors_reviewed_byTousers", fields: [reviewer_str_id], references: [uid], onDelete: NoAction, onUpdate: NoAction, map: "fk_processor_reviewed_by")
  users_processors_updated_byTousers  users?                 @relation("processors_updated_byTousers", fields: [updated_by], references: [uid], onDelete: NoAction, onUpdate: NoAction, map: "fk_processor_updated_by")

  @@index([extractionsid])
}

model companies_processors {
  id                 Int      @id @default(autoincrement())
  str_id             String?  @unique @default(dbgenerated("\"substring\"(md5((random())::text), 0, 21)"))
  account_id         String?  @db.VarChar(36)
  state              String   @default("active")
  created_at         DateTime @default(now()) @db.Timestamp(6)
  created_by         String?  @db.VarChar(36)
  created_proxied_by String?  @db.VarChar(36)
  updated_at         DateTime @default(now()) @db.Timestamp(6)
  updated_by         String?  @db.VarChar(36)
  updated_proxied_by String?  @db.VarChar(36)

  company_str_id   String
  import_status    String? @default("none") @db.VarChar(32)
  processor_str_id String

  company   companies  @relation(fields: [company_str_id], references: [str_id], onDelete: Cascade, onUpdate: Cascade)
  processor processors @relation(fields: [processor_str_id], references: [str_id], onDelete: Cascade, onUpdate: Cascade)

  @@unique([company_str_id, processor_str_id, account_id])
}

model prompts {
  id                 Int      @id @default(autoincrement())
  str_id             String?  @unique() @default(dbgenerated("\"substring\"(md5((random())::text), 0, 21)"))
  account_id         String?  @db.VarChar(36)
  uid                String?
  state              String   @default("active")
  created_at         DateTime @default(now()) @db.Timestamp(6)
  created_by         String?  @db.VarChar(36)
  created_proxied_by String?  @db.VarChar(36)
  updated_at         DateTime @default(now()) @db.Timestamp(6)
  updated_by         String?  @db.VarChar(36)
  updated_proxied_by String?  @db.VarChar(36)
  owner              String?  @db.VarChar(36)

  access          String?  @default("account")
  company_str_id  String?
  document_str_id String?  @db.VarChar
  extract_str_id  String?  @db.VarChar
  name            String?
  prompt          String?
  status          String   @default("draft")
  models          String[] @default([])

  document_profiles document_profiles[]
  companies         companies?          @relation(fields: [company_str_id], references: [str_id], onDelete: NoAction, onUpdate: NoAction, map: "fk_processors_company_str_id")
}

model reconciler_flows {
  id                 Int      @id(map: "reconciliation_flow_pkey") @default(autoincrement())
  str_id             String   @unique @default(dbgenerated("\"substring\"(md5((random())::text), 0, 21)"))
  account_id         String?  @db.VarChar(36)
  uid                String
  state              String   @default("active")
  access             String?  @default("account")
  created_at         DateTime @default(now()) @db.Timestamp(6)
  created_by         String?  @db.VarChar(36)
  created_proxied_by String?  @db.VarChar(36)
  updated_at         DateTime @default(now()) @db.Timestamp(6)
  updated_by         String?  @db.VarChar(36)
  updated_proxied_by String?  @db.VarChar(36)

  flow  Json?
  name  String
  notes String?
}

model reconciler_output {
  id                 Int      @id @default(autoincrement())
  str_id             String?  @unique @default(dbgenerated("\"substring\"(md5((random())::text), 0, 21)"))
  account_id         String?  @db.VarChar(36)
  uid                String?
  state              String   @default("active")
  created_at         DateTime @default(now()) @db.Timestamp(6)
  created_by         String?  @db.VarChar(36)
  created_proxied_by String?  @db.VarChar(36)
  updated_at         DateTime @default(now()) @db.Timestamp(6)
  updated_by         String?  @db.VarChar(36)
  updated_proxied_by String?  @db.VarChar(36)

  generated_key   String? @db.VarChar(255)
  reconciler_hash String? @db.VarChar(32)
  reconciler_id   Int     @db.Integer
  report_id       Int?    @db.Integer
  statement_id    Int?    @db.Integer

  @@index([report_id])
  @@index([statement_id])
  @@index([reconciler_id, reconciler_hash])
  @@index([account_id, uid, state])
}

model reconcilers {
  id                 Int      @id @default(autoincrement())
  str_id             String   @unique @default(dbgenerated("\"substring\"(md5((random())::text), 0, 21)"))
  account_id         String?  @db.VarChar(36)
  uid                String
  state              String   @default("active")
  access             String?  @default("account")
  created_at         DateTime @default(now()) @db.Timestamp(6)
  created_by         String?  @db.VarChar(36)
  created_proxied_by String?  @db.VarChar(36)
  updated_at         DateTime @default(now()) @db.Timestamp(6)
  updated_by         String?  @db.VarChar(36)
  updated_proxied_by String?  @db.VarChar(36)

  config                  Json?
  filter_report_params    Json?
  filter_statement_params Json?
  hash                    String?  @db.VarChar(32)
  key_condition           Json?
  key_condition_config    Json?
  key_config_report       Json?
  key_config_statement    Json?
  method_threshold_match  Decimal? @db.Decimal
  method_threshold_maybe  Decimal? @db.Decimal
  method_type             String?
  name                    String
  notes                   String?
  order                   Json?
  similarity_config       Json?

  @@index([hash])
}

model reconciliation_data {
  id                 Int      @id(map: "reconciliations_pkey") @default(autoincrement())
  str_id             String?  @unique @default(dbgenerated("\"substring\"(md5((random())::text), 0, 21)"))
  account_id         String?  @db.VarChar(36)
  uid                String?
  state              String   @default("active")
  created_at         DateTime @default(now()) @db.Timestamp(6)
  created_by         String?  @db.VarChar(36)
  created_proxied_by String?  @db.VarChar(36)
  updated_at         DateTime @default(now()) @db.Timestamp(6)
  updated_by         String?  @db.VarChar(36)
  updated_proxied_by String?  @db.VarChar(36)

  agent_id                      String?
  agent_name                    String?
  aggregate_premiums            Json?
  amount_paid                   Json?
  balance                       Decimal?  @db.Decimal
  cancellation_date             DateTime? @db.Timestamp(6)
  carrier_name                  String?
  commission_amount_monthly     Json?
  commission_balance_monthly    Json?
  commission_expected_monthly   Json?
  commissionable_premium_amount Decimal?  @db.Decimal
  commissions_expected          Decimal?  @db.Decimal
  contacts                      String[]  @default([])
  customer_name                 String?
  customer_paid_premium_amount  Decimal?  @db.Decimal
  effective_date                DateTime? @db.Timestamp(6)
  group_id                      String?
  internal_id                   String?
  issue_age                     Int?
  log                           Json?
  normalized_id                 String?
  notes                         String?
  override                      String?
  parent_id                     Int?
  payment_date_first            DateTime? @db.Timestamp(6)
  payment_date_last             DateTime? @db.Timestamp(6)
  policy_id                     String?
  policy_status                 String?
  premium_amount                Decimal?  @db.Decimal
  product_name                  String?
  product_sub_type              String?
  product_type                  String?
  reconciled                    String?
  reconciliation_id             Int?
  reconciliation_methods        Json?
  reconciliation_status         String?
  reconciliation_str_id         String?
  reinstatement_date            DateTime? @db.Timestamp(6)
  report_data_id                Int?
  report_id                     String?
  report_str_id                 String?
  statement_id                  String?
  statement_ids                 Json?
  statement_str_ids             Json?
  tag                           String?
  transaction_type              String?
  writing_carrier_name          String?

  children_reconciliation_data reconciliation_data[] @relation("parent")
  parent_data                  reconciliation_data?  @relation("parent", fields: [parent_id], references: [id])
  report_data                  report_data?          @relation(fields: [report_data_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
  statements                   statement_data[]

  @@index([account_id])
  @@index([normalized_id, report_id])
  @@index([effective_date])
  @@index([parent_id])
  @@index([payment_date_first])
  @@index([payment_date_last])
  @@index([reconciled])
  @@index([report_data_id])
  @@index([state])
}

model reconciliations {
  id                 Int      @id(map: "reconciliations_stats_pkey") @default(autoincrement())
  str_id             String   @unique @default(dbgenerated("\"substring\"(md5((random())::text), 0, 21)"))
  account_id         String?  @db.VarChar(36)
  uid                String
  state              String   @default("active")
  created_at         DateTime @default(now()) @db.Timestamp(6)
  created_by         String?  @db.VarChar(36)
  created_proxied_by String?  @db.VarChar(36)
  updated_at         DateTime @default(now()) @db.Timestamp(6)
  updated_by         String?  @db.VarChar(36)
  updated_proxied_by String?  @db.VarChar(36)

  notes                  String?
  reconciler_flow_id     Int?
  reconciler_flow_str_id String? @db.VarChar(36)
  result                 Json
  settings               Json

  @@index([account_id])
  @@index([state])
}

model report_contacts {
  id                 Int    @id @default(autoincrement())
  str_id_report_data String @map("str_id_report_data")
  str_id_contact     String @map("str_id_contact")

  contacts    contacts    @relation(fields: [str_id_contact], references: [str_id])
  report_data report_data @relation(fields: [str_id_report_data], references: [str_id])

  @@index([str_id_report_data], map: "str_id_report_data")
  @@index([str_id_contact], map: "str_id_contact")
}

model report_data {
  id                 Int      @id(map: "reportData_pkey") @default(autoincrement())
  str_id             String?  @unique(map: "report_data_str_id_unique") @default(dbgenerated("\"substring\"(md5((random())::text), 0, 21)"))
  account_id         String?  @db.VarChar(36)
  uid                String?
  state              String   @default("active")
  processing_status  String?
  created_at         DateTime @default(now()) @db.Timestamp(6)
  created_by         String?  @db.VarChar(36)
  created_proxied_by String?  @db.VarChar(36)
  updated_at         DateTime @default(now()) @db.Timestamp(6)
  updated_by         String?  @db.VarChar(36)
  updated_proxied_by String?  @db.VarChar(36)

  account_type                  String?
  agent_comp_profiles_rates     Json?
  agent_id                      String?
  agent_name                    String?
  agent_payout_rate_override    Json?
  aggregation_id                String?
  aggregation_primary           Boolean?
  cancellation_date             DateTime?              @db.Timestamp(6)
  commission_profile_id         String?
  commissionable_premium_amount Decimal?               @db.Decimal
  commissions_expected          Decimal?               @db.Decimal
  company_id                    Int?
  company_product_id            Int?
  config                        Json?
  contacts                      String[]               @default([])
  contacts_commission_split     Json?
  contacts_split                Json?
  customer_first_name           String?
  customer_id                   Int?
  customer_last_name            String?
  customer_name                 String?
  customer_paid_premium_amount  Decimal?               @db.Decimal
  dba                           String?
  document_id                   String?
  effective_date                DateTime?              @db.Timestamp(6)
  embedding                     Unsupported("vector")?
  embedding_info                Json?
  first_payment_date            DateTime?              @db.Timestamp(6)
  first_processed_date          DateTime?              @db.Timestamp(6)
  flags                         Json?
  flags_log                     Json?
  geo_state                     String?
  group_id                      String?
  group_name                    String?
  import_id                     String?
  internal_id                   String?
  issue_age                     Int?
  is_virtual                    Boolean                @default(false)
  notes                         String?
  parent_id                     Int?
  payment_mode                  String?
  policy_date                   DateTime?              @db.Timestamp(6)
  policy_id                     String?
  policy_status                 String?
  policy_term_months            Int?
  premium_amount                Decimal?               @db.Decimal
  excess_amount                 Decimal?               @db.Decimal
  product_name                  String?
  product_option_name           String?
  product_sub_type              String?
  product_type                  String?
  receivable_schedule           Json?
  reconciliation_method         String?
  reconciliation_stats          Json?
  reconciliation_status         String?
  reinstatement_date            DateTime?              @db.Timestamp(6)
  signed_date                   DateTime?              @db.Timestamp(6)
  split_percentage              Decimal?               @db.Decimal
  sync_id                       String?
  sync_worker                   String?
  tags                          String[]               @default([])
  transaction_type              String?
  type                          String?
  virtual_type                  virtual_type?
  writing_carrier_name          String?

  accounting_transaction_details accounting_transaction_details[]    @relation("ReportToTransactionDetails")
  accounting_transactions        accounting_transactions[]
  children_report_data           report_data[]                       @relation("parent")
  commission_profile             agent_commission_schedule_profiles? @relation(fields: [commission_profile_id], references: [str_id])
  company_product                company_products?                   @relation(fields: [company_product_id], references: [id])
  customer                       customers?                          @relation(fields: [customer_id], references: [id])
  document                       documents?                          @relation(fields: [document_id], references: [str_id])
  history                        history[]
  parent_data                    report_data?                        @relation("parent", fields: [parent_id], references: [id])
  reconciliation_data            reconciliation_data[]
  report_contacts                report_contacts[]
  statement_data                 statement_data[]

  @@unique([account_id, sync_id, state])
  @@index([account_id, state])
  @@index([account_id, state, effective_date])
  @@index([account_id, state, product_type])
  @@index([account_id, state, policy_id])
  @@index([account_id, state, policy_status])
  @@index([account_id, state, writing_carrier_name])
  @@index([embedding])
  @@index([parent_id])
  @@index([account_id, is_virtual])
  @@index([str_id])
}

model roles {
  id                 Int      @id
  state              String   @default("active")
  created_at         DateTime @default(now()) @db.Timestamp(6)
  created_by         String?  @db.VarChar(36)
  created_proxied_by String?  @db.VarChar(36)
  updated_at         DateTime @default(now()) @db.Timestamp(6)
  updated_by         String?  @db.VarChar(36)
  updated_proxied_by String?  @db.VarChar(36)

  description String?
  name        String

  account_role_settings account_role_settings[]
  account_user_roles    account_user_roles[]
}

model saved_report_groups {
  id                 Int      @id @default(autoincrement())
  str_id             String?  @unique @default(dbgenerated("\"substring\"(md5((random())::text), 0, 21)"))
  account_id         String   @db.VarChar(36)
  uid                String?
  state              String   @default("active")
  access             String?  @default("user")
  created_at         DateTime @default(now()) @db.Timestamp(6)
  created_by         String?  @db.VarChar(36)
  created_proxied_by String?  @db.VarChar(36)
  updated_at         DateTime @default(now()) @db.Timestamp(6)
  updated_by         String?  @db.VarChar(36)
  updated_proxied_by String?  @db.VarChar(36)

  name             String?
  notes            String?
  page             String?
  reviewed_by      String?            @db.VarChar
  status           String?            @default("")
  template         String?
  type             saved_report_type?
  users_white_list String[]           @default([])

  account       accounts        @relation(fields: [account_id], references: [str_id])
  saved_reports saved_reports[]
}

model saved_reports {
  id                 Int      @id @default(autoincrement())
  str_id             String?  @unique @default(dbgenerated("\"substring\"(md5((random())::text), 0, 21)"))
  account_id         String?  @db.VarChar(36)
  uid                String?
  state              String   @default("active")
  access             String?  @default("user")
  created_at         DateTime @default(now()) @db.Timestamp(6)
  created_by         String?  @db.VarChar(36)
  created_proxied_by String?  @db.VarChar(36)
  updated_at         DateTime @default(now()) @db.Timestamp(6)
  updated_by         String?  @db.VarChar(36)
  updated_proxied_by String?  @db.VarChar(36)

  contact_id            Int?
  name                  Json?
  notes                 String?
  page                  String?
  params                String?
  reviewed_at           DateTime?          @db.Timestamp(6)
  reviewed_by           String?            @db.VarChar
  saved_report_group_id Int?
  snapshot_data         Json?
  snapshot_data_old     Json?
  status                String             @default("draft")
  type                  saved_report_type?
  users_white_list      String[]           @default([])
  file_link             Int?               @unique

  account                          accounts?                        @relation(fields: [account_id], references: [str_id])
  accounting_transaction_details   accounting_transaction_details[]
  accounting_transactions          accounting_transactions[]
  contact                          contacts?                        @relation(fields: [contact_id], references: [id])
  saved_report_group               saved_report_groups?             @relation(fields: [saved_report_group_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
  saved_reports_history            saved_reports_history[]          @relation("saved_reports_history_saved_reports")
  users_reports_reviewed_byTousers users?                           @relation("reports_reviewed_byTousers", fields: [reviewed_by], references: [uid], onDelete: NoAction, onUpdate: NoAction, map: "fk_report_reviewed_by")
  uploaded_saved_reports           uploaded_saved_reports?          @relation(fields: [file_link], references: [id])
}

model uploaded_saved_reports {
  id                 Int      @id(map: "uploaded_saved_reports_pkey") @default(autoincrement())
  str_id             String?  @unique @default(dbgenerated("\"substring\"(md5((random())::text), 0, 21)"))
  account_id         String?  @db.VarChar(36)
  uid                String?
  state              String   @default("active")
  created_at         DateTime @default(now()) @db.Timestamp(6)
  created_by         String?  @db.VarChar(36)
  created_proxied_by String?  @db.VarChar(36)
  updated_at         DateTime @default(now()) @db.Timestamp(6)
  updated_by         String?  @db.VarChar(36)
  updated_proxied_by String?  @db.VarChar(36)

  file_path String?
  file_name String?
  file_type String?
  file_hash String?

  account       accounts?      @relation(fields: [account_id], references: [str_id])
  saved_reports saved_reports?
}

model saved_reports_history {
  id                 Int      @id @default(autoincrement())
  str_id             String?  @unique @default(dbgenerated("\"substring\"(md5((random())::text), 0, 21)"))
  account_id         String?  @db.VarChar(36)
  access             String?
  created_at         DateTime @default(now()) @db.Timestamp(6)
  created_by         String?  @db.VarChar(36)
  created_proxied_by String?  @db.VarChar(36)
  updated_at         DateTime @default(now()) @db.Timestamp(6)
  updated_by         String?  @db.VarChar(36)
  updated_proxied_by String?  @db.VarChar(36)

  name                  Json?
  notes                 String?
  page                  String?
  reviewed_by           String?
  saved_report_group_id Int?
  saved_reports_str_id  String
  snapshot_data         Json?
  status                String
  type                  saved_report_type?
  users_white_list      String[]

  rep_created_byTousers               users?        @relation("saved_rep_created_byTousers", fields: [created_by], references: [uid], onDelete: NoAction, onUpdate: NoAction, map: "fk_saved_rep_created_by")
  rep_reviewed_byTousers              users?        @relation("saved_rep_reviewed_byTousers", fields: [reviewed_by], references: [uid], onDelete: NoAction, onUpdate: NoAction, map: "fk_saved_rep_reviewed_by")
  saved_reports_history_saved_reports saved_reports @relation("saved_reports_history_saved_reports", fields: [saved_reports_str_id], references: [str_id], onDelete: NoAction, onUpdate: NoAction)
}

model statement_data {
  id                 Int      @id(map: "statementData_pkey") @default(autoincrement())
  str_id             String?  @unique @default(dbgenerated("\"substring\"(md5((random())::text), 0, 21)"))
  account_id         String?  @db.VarChar(36)
  uid                String?
  state              String   @default("active")
  processing_status  String?
  created_at         DateTime @default(now()) @db.Timestamp(6)
  created_by         String?  @db.VarChar(36)
  created_proxied_by String?  @db.VarChar(36)
  updated_at         DateTime @default(now()) @db.Timestamp(6)
  updated_by         String?  @db.VarChar(36)
  updated_proxied_by String?  @db.VarChar(36)

  account_type                     String?
  advanced_commission_amount       Decimal?      @db.Decimal
  agent_commission_payout_rate     Json?
  agent_commission_payout_rate_old Json?
  agent_commissions                Json?
  agent_commissions_log            Json?
  agent_commissions_status         Json?
  agent_commissions_status2        Json?
  agent_commissions_v2             Json?
  agent_id                         String?
  agent_name                       String?
  agent_payout_rate                Json?
  agent_payout_rate_old            Json?
  agent_payout_rate_override       Json?
  aggregation_id                   String?
  allocated_amount                 Decimal?      @db.Decimal
  bill_mode                        String?
  carrier_name                     String?
  carrier_rate                     String?
  commission_amount                Decimal?      @db.Decimal
  commission_basis                 String?
  commission_paid_amount           Decimal?      @db.Decimal
  commission_rate                  String?
  commission_rate_percent          Decimal?      @db.Decimal
  commissionable_premium_amount    Decimal?      @db.Decimal
  compensation_type                String?
  contacts                         String[]      @default([])
  customer_name                    String?
  customer_paid_premium_amount     Decimal?      @db.Decimal
  document_id                      String?
  effective_date                   DateTime?     @db.Timestamp(6)
  fees                             Decimal?      @db.Decimal
  flags                            Json?
  flags_log                        Json?
  geo_state                        String?
  group_id                         String?
  group_name                       String?
  import_id                        String?
  internal_id                      String?
  invoice_date                     DateTime?     @db.Timestamp(6)
  issue_age                        Int?
  is_virtual                       Boolean       @default(false)
  master_id                        Int?
  member_count                     Int?
  new_carrier_rate                 Decimal?      @db.Decimal
  new_commission_rate              Decimal?      @db.Decimal
  notes                            String?
  parent_id                        Int?
  payment_date                     DateTime?     @db.Timestamp(6)
  payment_mode                     String?
  payment_status                   String?
  period_date                      DateTime?     @db.Timestamp(6)
  policy_id                        String?
  premium_amount                   Decimal?      @db.Decimal
  premium_type                     String        @default("policy")
  processing_date                  DateTime?     @default(now()) @db.Timestamp(6)
  product_name                     String?
  product_option_name              String?
  product_sub_type                 String?
  product_type                     String?
  reconciled_at                    DateTime?     @db.Timestamp(6)
  reconciliation_method            String?
  reconciliation_stats             Json?
  reconciliation_status            String?
  remain_amount                    Decimal?      @db.Decimal
  report_data_id                   Int?
  split_percentage                 Decimal?      @db.Decimal
  standardized_customer_name       String?
  statement_number                 String?
  status                           String?
  sync_id                          String?
  tags                             String[]      @default([])
  transaction_type                 String?
  type                             String?
  virtual_type                     virtual_type?
  writing_carrier_name             String?

  accounting_transaction_details accounting_transaction_details[]
  children_data                  statement_data[]                 @relation("parent")
  document                       documents?                       @relation(fields: [document_id], references: [str_id])
  history                        history[]
  master                         statement_data?                  @relation("master", fields: [master_id], references: [id])
  details                        statement_data[]                 @relation("master")
  parent_data                    statement_data?                  @relation("parent", fields: [parent_id], references: [id])
  reconciliations                reconciliation_data[]
  report                         report_data?                     @relation(fields: [report_data_id], references: [id], onDelete: NoAction, onUpdate: NoAction)

  @@index([account_id, is_virtual])
  @@index([account_id, state, agent_commissions_status])
  @@index([account_id, state, agent_name])
  @@index([account_id, state, carrier_name])
  @@index([account_id, state, compensation_type])
  @@index([account_id, state, customer_name])
  @@index([account_id, state, document_id])
  @@index([account_id, state, effective_date])
  @@index([account_id, state, parent_id])
  @@index([account_id, state, payment_date])
  @@index([account_id, state, policy_id])
  @@index([account_id, state, processing_date])
  @@index([account_id, state, processing_status])
  @@index([account_id, state, product_type])
  @@index([account_id, state, report_data_id])
  @@index([account_id, state, writing_carrier_name])
  @@index([account_id, state])
  @@index([str_id])
}

model users {
  id                 Int       @id(map: "settings_pkey") @default(autoincrement())
  str_id             String    @unique @default(dbgenerated("\"substring\"(md5((random())::text), 0, 21)")) @db.VarChar(36)
  uid                String?   @unique(map: "users_uid_unique")
  state              String    @default("active")
  created_at         DateTime  @default(now()) @db.Timestamp(6)
  created_by         String?   @db.VarChar(36)
  created_proxied_by String?   @db.VarChar(36)
  updated_at         DateTime  @default(now()) @db.Timestamp(6)
  updated_by         String?   @db.VarChar(36)
  updated_proxied_by String?   @db.VarChar(36)
  last_visited_at    DateTime? @db.Timestamp(6)

  company                String?
  drive_folder_name      String?
  email                  String?   @unique
  first_name             String?
  invite_token           String?   @unique(map: "users_invite_token_unique")
  last_drive_synced_time DateTime?
  last_name              String?
  last_uploaded_time     String?
  mode                   String?
  notes                  String?
  phone                  String?
  plaid_access_token     String?
  reconciliation_view    Json?
  refresh_token          String?
  sub_type               String?
  tos_accepted_at        DateTime?
  type                   String?

  account_user_roles                       account_user_roles[]
  accounts                                 accounts[]
  data_processing                          data_processing[]       @relation("data_processing_user")
  documents_documents_created_byTousers    documents[]             @relation("documents_created_byTousers")
  documents_documents_updated_byTousers    documents[]             @relation("documents_updated_byTousers")
  processors_processors_created_byTousers  processors[]            @relation("processors_created_byTousers")
  processors_processors_reviewed_byTousers processors[]            @relation("processors_reviewed_byTousers")
  processors_processors_updated_byTousers  processors[]            @relation("processors_updated_byTousers")
  proxied_data_processing                  data_processing[]       @relation("data_processing_proxy_user")
  rep_saved_rep_created_byTousers          saved_reports_history[] @relation("saved_rep_created_byTousers")
  rep_saved_rep_reviewed_byTousers         saved_reports_history[] @relation("saved_rep_reviewed_byTousers")
  reports_reports_reviewed_byTousers       saved_reports[]         @relation("reports_reviewed_byTousers")
  user_contact                             contacts[]              @relation("user_contact")
}

model validation_results {
  id                 Int      @id @default(autoincrement())
  str_id             String?  @unique @default(dbgenerated("\"substring\"(md5((random())::text), 0, 21)"))
  account_id         String?  @db.VarChar(36)
  state              String   @default("active")
  created_at         DateTime @default(now()) @db.Timestamp(6)
  created_by         String?  @db.VarChar(36)
  created_proxied_by String?  @db.VarChar(36)
  updated_at         DateTime @default(now()) @db.Timestamp(6)
  updated_by         String?  @db.VarChar(36)
  updated_proxied_by String?  @db.VarChar(36)

  data_entity           String
  data_id               Int
  data_update_config_id Int
  validation_level      Json?
  validation_log        Json?

  data_update_config data_update_config @relation(fields: [data_update_config_id], references: [id], name: "DataUpdateConfigToValidationResults")

  @@index([data_id, data_entity], map: "data_entity_idx")
  @@index([data_update_config_id], map: "data_update_config_idx")
}

model widgets {
  id                 Int      @id @default(autoincrement())
  str_id             String?  @unique @default(dbgenerated("\"substring\"(md5((random())::text), 0, 21)"))
  account_id         String?  @db.VarChar(36)
  state              String   @default("active")
  uid                String?
  created_at         DateTime @default(now()) @db.Timestamp(6)
  created_by         String?  @db.VarChar(36)
  created_proxied_by String?  @db.VarChar(36)
  updated_at         DateTime @default(now()) @db.Timestamp(6)
  updated_by         String?  @db.VarChar(36)
  updated_proxied_by String?  @db.VarChar(36)

  access String? @default("global")
  name   String?
  slug   String
  spec   Json?
}

model tmp_payout_report {
  id         Int      @id @default(autoincrement())
  str_id     String?  @unique @default(dbgenerated("\"substring\"(md5((random())::text), 0, 21)"))
  created_at DateTime @default(now()) @db.Timestamp(6)
  updated_at DateTime @default(now()) @db.Timestamp(6)

  shortcode     String
  policy_number String
  first_name    String
  last_name     String
  date_from     DateTime
  date_to       DateTime
  carrier       String
  business      String
  agent         String
  policy_holder String
  premium       Decimal
  mode          String?
  split         Int
  percentage    Decimal
  payment       Decimal
  remark        String?
  type          String
}

enum customer_gender {
  male
  female
}

enum customer_type {
  person
  company
  individual
  group
}

enum document_type_create_type {
  manully
  automatically
}

enum document_types_status {
  draft
  in_review
  need_update
  approved
}

enum history_state {
  new
  processed
}

enum history_status {
  active
  deActive
}

enum history_type {
  statement
  report
}

enum extractions_status {
  new
  extracted
}

enum file_type {
  pdf
  spreadsheet
  img
  html
}

enum processor_status {
  new
  processed
}

enum document_extract_method {
  extractTable
  documentAI
  spreadsheet
  adobeExtract
  nanonets
  htmlExtract
}

enum saved_report_type {
  view
  report
  file
}

enum data_processing_type {
  agent_commission_calc
  grouping
  reconciliation
  data_sync
  sync
  document_processing
  document_extraction
  gmail_sync
  google_drive_sync
  payment_allocation
}

enum data_imports_type {
  statement
  report
}

enum DateRangesTypes {
  comp_grid_criterion
  comp_grid_rates
  any
}

enum virtual_type {
  grouped
  scheduled
  partial_payment
  remain_payment
}
