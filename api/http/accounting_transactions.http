###
# @name Get transactions for agent
# @import ./auth.http
# @ref fintaryAdmin 
GET {{baseUrl}}/api/accounting/transactions/per-agent?agent_str_id=COc5Tpvr10OtcVEdtyi66&start_date=2023-01-01&end_date=2023-01-31
Content-Type: application/json; charset=UTF-8
Accept: application/json
accountid: {{fintaryAdminAccountId}} 
authentication: Bearer {{fintaryAdminIdToken}} 

> {% 
  client.test("Given valid agent ID, when fetching transactions, should return 200 with transactions", function() {
    client.assert(response.status === 200, "Response status is not 200");
    client.assert(response.body.transactions.length > 0, "No transactions found");
    client.assert(response.body.transactions[0].str_id === "AOc5Tpvr_0ORcVEdtyi6d", "Transaction str_id doesn't match");
  })
%}

###
# @name Get transaction by specific ID
# @import ./auth.http
# @ref fintaryAdmin 
GET {{baseUrl}}/api/accounting/transactions/per-agent?agent_str_id=COc5Tpvr10OtcVEdtyi66&transaction_str_id=AOc5Tpvr_0ORcVEdtyi6d
Content-Type: application/json; charset=UTF-8
Accept: application/json
accountid: {{fintaryAdminAccountId}} 
authentication: Bearer {{fintaryAdminIdToken}} 

> {% 
  client.test("Given transaction ID, when fetching specific transaction, should return 200 with transaction", function() {
    client.assert(response.status === 200, "Response status is not 200");
    client.assert(response.body.transactions.length === 1, "Should return exactly one transaction");
    client.assert(response.body.transactions[0].str_id === "AOc5Tpvr_0ORcVEdtyi6d", "Transaction str_id doesn't match");
  })
%}

###
# @name Create transactions for agent
# @import ./auth.http
# @ref fintaryAdmin 
POST {{baseUrl}}/api/accounting/transactions/per-agent
Content-Type: application/json; charset=UTF-8
Accept: application/json
accountid: {{fintaryAdminAccountId}} 
authentication: Bearer {{fintaryAdminIdToken}} 

{
  "agent_str_id": "COc5Tpvr10OtcVEdtyi66",
  "transactions": { 
   "updates": [
    {
      "amount": 2500,
      "transaction_date": "2023-02-15",
      "description": "New test transaction"
    }
  ],
  "deletes": []
  }
}

> {% 
  client.test("Given valid transaction data, when creating transactions, should return 200 with success", function() {
    client.assert(response.status === 200, "Response status is not 200");
    client.assert(response.body.success === true, "Success flag not true");
    client.assert(response.body.data === true, "Should return true for created transaction");
  })
%}

###
# @name Get updated transactions for agent
# @import ./auth.http
# @ref fintaryAdmin 
GET {{baseUrl}}/api/accounting/transactions/per-agent?agent_str_id=COc5Tpvr10OtcVEdtyi66&start_date=2023-02-01&end_date=2023-02-28
Content-Type: application/json; charset=UTF-8
Accept: application/json
accountid: {{fintaryAdminAccountId}} 
authentication: Bearer {{fintaryAdminIdToken}} 

> {% 
  client.test("Given date range after creating new transaction, when fetching transactions, should include newly created transaction", function() {
    client.assert(response.status === 200, "Response status is not 200");
    client.assert(response.body.data = true, "Should return true for updated transaction");
  })
%}
