import { injectable } from 'inversify';
import { account_role_settings, Prisma } from '@prisma/client';
import { customViewDefault } from 'common/constants/account_role_settings';

import { prismaClient } from '@/lib/prisma';
import { BusinessException } from '@/lib/exceptionHandler';
import {
  ContactSettings,
  DataStates,
  Roles,
  ViewsAndFieldsPageSettings,
} from '@/types';
import {
  defaultLandingPage,
  defaultRoleList,
} from '@/pages/api/accounts/settings/defaults';

interface PagesSettings {
  add_documents?: ViewsAndFieldsPageSettings;
  agent_schedules?: ViewsAndFieldsPageSettings;
  agents?: ViewsAndFieldsPageSettings;
  agents_groups?: ViewsAndFieldsPageSettings;
  agents_production?: ViewsAndFieldsPageSettings;
  carriers_schedules?: ViewsAndFieldsPageSettings;
  commissions?: ViewsAndFieldsPageSettings;
  companies?: ViewsAndFieldsPageSettings;
  comp_grids_schedules?: ViewsAndFieldsPageSettings;
  documents?: ViewsAndFieldsPageSettings;
  incentives_schedules?: ViewsAndFieldsPageSettings;
  insights?: ViewsAndFieldsPageSettings;
  nameOptions?: ViewsAndFieldsPageSettings;
  options?: ViewsAndFieldsPageSettings;
  policies?: ViewsAndFieldsPageSettings;
  products?: ViewsAndFieldsPageSettings;
  reconciliation?: ViewsAndFieldsPageSettings;
  reports?: ViewsAndFieldsPageSettings;
  settings_data_processing?: ViewsAndFieldsPageSettings;
  summaries?: ViewsAndFieldsPageSettings;
  custom_reports?: ViewsAndFieldsPageSettings;
  views?: ViewsAndFieldsPageSettings;
}

interface Settings {
  pages_settings: PagesSettings;
  agent_settings: ContactSettings;
  companies_view: string[];
  default_page: string;
  insights_widgets: any; // TODO: define insights_widgets type
}

@injectable()
export class SettingsService implements ISettingsService {
  async getDefaultLandingPages(
    accountId: string,
    roleId?: number
  ): Promise<{ role_id: Roles; default_page: string }[] | string> {
    const whereClause: any = {
      account_id: accountId,
      state: 'active',
    };

    if (roleId) {
      whereClause.role_id = roleId;
    }

    const results = await prismaClient.account_role_settings.findMany({
      where: whereClause,
      select: {
        role_id: true,
        default_page: true,
      },
    });

    if (!roleId) {
      if (!results) {
        return [];
      }

      // Ensure all roles in defaultRoleList exist in the response
      const updatedResponse = results.map((result) => ({
        role_id: result.role_id,
        default_page: result.default_page || defaultLandingPage.id,
      }));
      defaultRoleList.forEach((defaultRole) => {
        if (!results.some((r) => r.role_id === defaultRole.id)) {
          updatedResponse.push({
            role_id: defaultRole.id,
            default_page: defaultLandingPage.id,
          });
        }
      });
      return updatedResponse;
    }

    if (results.length === 0 || !results[0].default_page) {
      return defaultLandingPage.id;
    }
    return results[0].default_page;
  }

  async getAgentCommissionsDownlineDataAccess(accountId: string): Promise<{
    agentCommissionsDirectDownlineDataAccess: boolean;
    agentCommissionsExtendedDownlineDataAccess: boolean;
  }> {
    const result = await prismaClient.account_role_settings.findUnique({
      where: {
        account_id_role_id_custom_view_name: {
          account_id: accountId,
          role_id: Roles.PRODUCER,
          custom_view_name: customViewDefault,
        },
      },
      select: {
        agent_settings: true,
      },
    });

    const agentSettings = result?.agent_settings as unknown as ContactSettings;

    if (!agentSettings) {
      return {
        agentCommissionsDirectDownlineDataAccess: false,
        agentCommissionsExtendedDownlineDataAccess: false,
      };
    }

    return {
      agentCommissionsDirectDownlineDataAccess:
        agentSettings.directDownlineDataAccess?.commissionsConfig === 'Yes'
          ? true
          : false,
      agentCommissionsExtendedDownlineDataAccess:
        agentSettings.extendedDownlineDataAccess?.commissionsConfig === 'Yes'
          ? true
          : false,
    };
  }

  async getRoleSettingsByAccountAndRole(
    accountId: string,
    roleId: number,
    customViewName: string = customViewDefault
  ): Promise<Settings> {
    const result = await prismaClient.account_role_settings.findUnique({
      where: {
        account_id_role_id_custom_view_name: {
          account_id: accountId,
          role_id: roleId,
          custom_view_name: customViewName,
        },
        state: 'active',
      },
      select: {
        pages_settings: true,
        agent_settings: true,
        companies_view: true,
        insights_widgets: true,
        default_page: true,
      },
    });

    if (!result) {
      return {
        pages_settings: {} as PagesSettings,
        agent_settings: {} as ContactSettings,
        companies_view: [] as string[],
        default_page: '',
        insights_widgets: {},
      };
    }

    return {
      pages_settings: result.pages_settings as unknown as PagesSettings,
      agent_settings: result.agent_settings as unknown as ContactSettings,
      companies_view: result.companies_view as unknown as string[],
      default_page: result.default_page,
      insights_widgets: result.insights_widgets,
    };
  }

  async upsertAccountRoleSettings(
    accountId: string,
    roleId: number,
    updateBody: any,
    uid: string,
    ouid: string,
    id?: {
      account_id: string;
      role_id: number;
      custom_view_name: string;
    }
  ): Promise<account_role_settings> {
    // If the customViewName is not customViewDefault
    // create a record with customViewDefault first if it doesn't exist
    const existingRecord = await prismaClient.account_role_settings.findUnique({
      where: {
        account_id_role_id_custom_view_name: {
          account_id: accountId,
          role_id: roleId,
          custom_view_name: customViewDefault,
        },
      },
    });

    if (!existingRecord) {
      await prismaClient.account_role_settings.create({
        data: {
          custom_view_name: customViewDefault,
          created_at: new Date(),
          created_by: uid,
          created_proxied_by: ouid,
          account: { connect: { str_id: accountId } },
          role: { connect: { id: roleId } },
        },
      });
    }

    if (id) {
      return await prismaClient.account_role_settings.update({
        where: {
          account_id_role_id_custom_view_name: {
            account_id: id.account_id,
            role_id: id.role_id,
            custom_view_name: id.custom_view_name,
          },
        },
        data: {
          ...updateBody,
          updated_by: uid,
          updated_proxied_by: ouid,
          updated_at: new Date(),
        },
      });
    } else {
      return await prismaClient.account_role_settings.create({
        data: {
          ...updateBody,
          created_at: new Date(),
          created_by: uid,
          created_proxied_by: ouid,
          account: { connect: { str_id: accountId } },
          role: { connect: { id: roleId } },
        },
      });
    }
  }

  async updateDefaultLandingPage(
    accountId: string,
    roleId: number,
    defaultLandingPage: string,
    uid: string,
    ouid: string
  ): Promise<account_role_settings> {
    return await prismaClient.account_role_settings.upsert({
      where: {
        account_id_role_id_custom_view_name: {
          account_id: accountId,
          role_id: roleId,
          custom_view_name: customViewDefault,
        },
      },
      update: {
        default_page: defaultLandingPage,
        updated_by: uid,
        updated_proxied_by: ouid,
        updated_at: new Date(),
      },
      create: {
        default_page: defaultLandingPage,
        created_at: new Date(),
        created_by: uid,
        created_proxied_by: ouid,
        account: { connect: { str_id: accountId } },
        role: { connect: { id: roleId } },
        pages_settings: {} as PagesSettings,
        agent_settings: {} as ContactSettings,
        companies_view: [] as string[],
        insights_widgets: {},
      },
    });
  }

  async getSettingsByContact({
    uid,
    accountId,
    roleId,
    contactStrId,
  }: {
    uid: string;
    accountId: string;
    roleId: number;
    contactStrId?: string;
  }): Promise<Settings> {
    let whereClause: Prisma.contactsWhereInput = {};
    // If contactStrId is provided, get the contact by str_id
    // If contactStrId is not provided, get the contact by uid
    if (contactStrId) {
      whereClause = {
        str_id: contactStrId,
      };
    } else {
      whereClause = {
        user_contact: {
          uid,
        },
      };
    }

    const contact = await prismaClient.contacts.findFirst({
      where: whereClause,
      include: {
        account_role_settings: true,
      },
    });

    let result = contact?.account_role_settings;
    // Get the contact by uid
    // If account_role_settings is not found, get the account_role_settings by accountId and roleId and customViewName
    // If account_role_settings is not found, return the default settings
    if (!result) {
      result = await prismaClient.account_role_settings.findFirst({
        where: {
          account_id: accountId,
          role_id: roleId,
          custom_view_name: customViewDefault,
        },
      });
    }

    if (!result) {
      return {
        pages_settings: {} as PagesSettings,
        agent_settings: {} as ContactSettings,
        companies_view: [] as string[],
        default_page: '',
        insights_widgets: {},
      };
    }

    return {
      pages_settings: result.pages_settings as unknown as PagesSettings,
      agent_settings: result.agent_settings as unknown as ContactSettings,
      companies_view: result.companies_view as unknown as string[],
      default_page: result.default_page,
      insights_widgets: result.insights_widgets,
    };
  }

  /**
   * Retrieves commission fields configuration for a specific account and role
   * @param accountId - The account identifier
   * @param roleId - The role identifier
   * @returns Record<string, boolean> mapping fields to their enabled status - this format is
   * meant to be used directly in a prisma select object for statement_data, enabling query
   * of only the configured fields for each account and role
   */
  async getCommissionFieldsByAccountAndRole(
    accountId: string,
    roleId: number
  ): Promise<Record<string, boolean | object>> {
    const result = await prismaClient.account_role_settings.findUnique({
      where: {
        account_id_role_id_custom_view_name: {
          account_id: accountId,
          role_id: roleId,
          custom_view_name: customViewDefault,
        },
      },
      select: {
        pages_settings: true,
      },
    });

    if (!result) {
      throw new BusinessException(
        `No settings found for account ${accountId} and role ${roleId}`
      );
    }

    const pagesSettings = result.pages_settings as PagesSettings | undefined;
    const commissionFields = pagesSettings?.commissions?.fields || [];

    const selectObj = commissionFields.reduce<Record<string, boolean | object>>(
      (acc, field) => {
        if (
          field !== 'comp_calc_status' &&
          field !== 'comp_calc' &&
          field !== 'comp_calc_log'
        ) {
          acc[field] = true;
        }
        return acc;
      },
      {}
    );

    // Check if fields related to accounting_transaction_details are enabled
    const hasCompCalcStatus = commissionFields.includes('comp_calc_status');
    const hasCompCalc = commissionFields.includes('comp_calc');
    const hasCompCalcLog = commissionFields.includes('comp_calc_log');

    if (hasCompCalcStatus || hasCompCalc || hasCompCalcLog) {
      const accountingSelect: Record<string, boolean | object> = {};

      if (hasCompCalcStatus) {
        accountingSelect.status = true;
      }

      if (hasCompCalc) {
        accountingSelect.amount = true;
      }

      if (hasCompCalcLog) {
        accountingSelect.logs = true;
      }

      accountingSelect.contact = {
        select: {
          id: true,
          str_id: true,
          first_name: true,
          last_name: true,
          email: true,
        },
      };

      selectObj.accounting_transaction_details = {
        where: { state: DataStates.ACTIVE },
        select: accountingSelect,
      };
    }

    return selectObj;
  }
}

export interface ISettingsService {
  getDefaultLandingPages(
    accountId: string
  ): Promise<{ role_id: Roles; default_page: string }[] | string>;
  getRoleSettingsByAccountAndRole(
    accountId: string,
    roleId: number
  ): Promise<Settings>;
  upsertAccountRoleSettings(
    accountId: string,
    roleId: number,
    updateBody: any,
    uid: string,
    ouid: string
  ): Promise<account_role_settings>;
  updateDefaultLandingPage(
    accountId: string,
    roleId: number,
    defaultLandingPage: string,
    uid: string,
    ouid: string
  ): Promise<account_role_settings>;
  getAgentCommissionsDownlineDataAccess(accountId: string): Promise<{
    agentCommissionsDirectDownlineDataAccess: boolean;
    agentCommissionsExtendedDownlineDataAccess: boolean;
  }>;
  getSettingsByContact({
    uid,
    accountId,
    roleId,
    contactStrId,
  }: {
    uid: string;
    accountId: string;
    roleId: number;
    contactStrId?: string;
  }): Promise<Settings>;
  getCommissionFieldsByAccountAndRole(
    accountId: string,
    roleId: number
  ): Promise<Record<string, boolean | object>>;
}
