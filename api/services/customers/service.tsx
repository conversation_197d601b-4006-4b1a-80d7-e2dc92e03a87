import { Prisma } from '@prisma/client';
import { CreateCustomerFromPolicies } from 'common/customer/customer.types';
import {
  BulkEditCustomerDTO,
  bulkEditCustomerSchema,
  CreateCustomerDTO,
  createCustomerSchema,
  DeleteCustomerDTO,
  deleteCustomerSchema,
  GetCustomerDTO,
  GetCustomersByNamesDTO,
  getCustomersByNamesSchema,
  getCustomerSchema,
  UpdateCustomerDTO,
  updateCustomerSchema,
} from 'common/customer/customer.validator';
import { splitCustomerName } from 'common/utils';
import { inject, injectable } from 'inversify';
import { isNil, omitBy } from 'lodash-es';

import { prismaClient } from '@/lib/prisma';
import { SyncFieldService } from '@/pages/api/data_processing/sync/syncFieldService';
import { ReportService } from '@/services/report';
import { ExtAccountInfo } from '@/types';
import { CustomerFiled } from './type';

@injectable()
export class CustomerService {
  @inject(ReportService) reportDataService: ReportService;
  @inject(SyncFieldService) syncFieldService: SyncFieldService;

  private selecteFields: { [key in CustomerFiled]?: boolean } = {
    id: true,
    first_name: true,
    middle_name: true,
    website: true,
    phone: true,
    nickname: true,
    last_name: true,
    str_id: true,
    address: true,
    company_name: true,
    dob: true,
    email: true,
    gender: true,
    type: true,
    start_date: true,
    end_date: true,
    group_id: true,
    sync_id: true,
    config: true,
    sync_worker: true,
  };

  async bulkEdit(data: BulkEditCustomerDTO & { isTypeNull: boolean }) {
    const { ids, type, account_id, updated_by, updated_proxied_by } =
      bulkEditCustomerSchema.parse(data);
    const res = await prismaClient.customers.updateMany({
      where: {
        id: {
          in: ids,
        },
        account_id,
      },
      data: {
        type: data.isTypeNull ? null : type,
        updated_at: new Date(),
        updated_by,
        updated_proxied_by,
      },
    });
    return res.count;
  }

  async getExistingCustomer(data: GetCustomersByNamesDTO) {
    const { first_names, last_names, company_names, account_id } =
      getCustomersByNamesSchema.parse(data);

    const res = await prismaClient.customers.findMany({
      where: {
        OR: [
          {
            AND: [
              {
                first_name: {
                  in: first_names,
                },
              },
              {
                last_name: {
                  in: last_names,
                },
              },
            ],
          },
          { company_name: { in: company_names } },
        ],
        account_id,
        state: 'active',
      },
    });

    return res;
  }
  /**
   * This function is used for autocomplete feature
   */
  async searchName(q: string, account_id: string) {
    if (!q?.trim() || !account_id) return [];

    const { first_name, last_name } = splitCustomerName(q);

    return prismaClient.customers.findMany({
      where: {
        account_id,
        state: 'active',
        OR: [
          {
            first_name: {
              contains: first_name,
              mode: 'insensitive',
            },
          },
          {
            last_name: {
              contains: last_name,
              mode: 'insensitive',
            },
          },
        ],
      },
      take: 10,
    });
  }

  async createCustomerFromPolicies(
    customers: (CreateCustomerDTO &
      Omit<
        CreateCustomerFromPolicies,
        | 'clientId'
        | 'dedupe_group_ids'
        | 'policies_ids'
        | 'dedupe_customers_names'
      >)[]
  ) {
    const _customers = customers.map((c) => omitBy(c, isNil));
    for (let i = 0; i < _customers.length; i++) {
      const { policy_str_ids } = _customers[i] as CreateCustomerFromPolicies;
      let customer_id = _customers[i].customer_id;
      if (!customer_id) {
        const createCustomerData = createCustomerSchema.parse(_customers[i]);
        const res = await prismaClient.customers.create({
          data: createCustomerData,
        });
        customer_id = res.id;
      }

      await this.reportDataService.updateCustomerId({
        str_ids: policy_str_ids,
        customer_id,
        account_id: customers[0].account_id,
      });
    }

    return _customers.length;
  }

  async bulkAdd(customers: CreateCustomerDTO[]) {
    const _customers = customers.map((c) => omitBy(c, isNil));

    for (let i = 0; i < _customers.length; i++) {
      _customers[i] = createCustomerSchema.parse(_customers[i]);
    }

    const result = await prismaClient.customers.createMany({
      data: _customers,
    });
    return result.count;
  }

  async getCustomers({
    skip,
    take,
    ...params
  }: GetCustomerDTO & { skip?: number; take?: number }) {
    const {
      orderBy,
      sort = 'desc',
      q,
      account_id,
    } = getCustomerSchema.parse(params);

    const conditions: Prisma.customersFindManyArgs = {
      where: {
        account_id,
        state: 'active',
      },
      take,
      skip,
      select: {
        ...this.selecteFields,
        report_data: {
          select: {
            effective_date: true,
            group_id: true,
            id: true,
            policy_id: true,
            policy_status: true,
            policy_term_months: true,
            product_name: true,
            product_type: true,
            str_id: true,
            writing_carrier_name: true,
          },
        },
      },
    };

    if (q) {
      conditions.where!.OR = [
        { first_name: { contains: q, mode: 'insensitive' } },
        { middle_name: { contains: q, mode: 'insensitive' } },
        { last_name: { contains: q, mode: 'insensitive' } },
        { nickname: { contains: q, mode: 'insensitive' } },
        { company_name: { contains: q, mode: 'insensitive' } },
        { website: { contains: q, mode: 'insensitive' } },
        { email: { contains: q, mode: 'insensitive' } },
        { group_id: { contains: q, mode: 'insensitive' } },
        {
          address: {
            string_contains: q,
            path: ['country'],
          },
        },
        {
          address: {
            string_contains: q,
            path: ['zipcode'],
          },
        },
        {
          address: {
            string_contains: q,
            path: ['street'],
          },
        },
        {
          address: {
            string_contains: q,
            path: ['city'],
          },
        },
      ];
    }

    if (orderBy) {
      conditions.orderBy = {
        [orderBy]: sort,
      };
    }

    const data = await prismaClient.customers.findMany(conditions);
    const total = await prismaClient.customers.count({
      where: conditions.where,
    });

    return { data, count: total };
  }

  async addCustomer(params: CreateCustomerDTO) {
    params = omitBy(params, isNil) as CreateCustomerDTO;

    const customerData = createCustomerSchema.parse(params);

    await prismaClient.customers.create({ data: customerData as any });
    return true;
  }

  async updateCustomer(params: UpdateCustomerDTO) {
    params = omitBy(params, isNil) as UpdateCustomerDTO;

    const { id, ...newData } = updateCustomerSchema.parse(params);
    const existing = await prismaClient.customers.findFirst({
      where: {
        id,
        account_id: params.account_id,
        state: 'active',
      },
    });

    if (!existing) {
      throw new Error('Customer not found');
    }

    await this.syncFieldService.canUpdateIfChanged({
      newData,
      tableName: 'customers',
      id: id,
      account: { account_id: params.account_id } as ExtAccountInfo,
      config: params.config || {},
    });
    await prismaClient.customers.update({
      data: { ...newData, updated_at: new Date() } as any,
      where: { id, account_id: params.account_id },
    });
    return true;
  }

  async deleteMany(params: DeleteCustomerDTO) {
    const { ids, account_id } = deleteCustomerSchema.parse(params);
    const existing = await prismaClient.customers.findMany({
      where: {
        id: {
          in: ids,
        },
        account_id,
        state: 'active',
      },
    });

    if (existing.length !== ids.length) {
      throw new Error('Some customers are not found');
    }

    await prismaClient.customers.updateMany({
      where: {
        id: {
          in: ids,
        },
      },
      data: {
        state: 'deleted',
      },
    });
    return true;
  }
}
