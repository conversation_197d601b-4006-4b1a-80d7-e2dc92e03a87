import { file_type, Prisma } from '@prisma/client';
import * as Sentry from '@sentry/nextjs';
import { captureException } from '@sentry/nextjs';
import { WorkerNames } from 'common/constants';
import {
  GetDocumentGroupDTO,
  getDocumentGroupSchema,
} from 'common/documents/document-group.validator';
import { GROUP_BY_VALUES } from 'common/documents/documents.constants';
import { DocumentGroupItem } from 'common/documents/documents.types';
import { DocumentUploadParams } from 'common/dto/open/documents/upload';
import { sha256 } from 'crypto-hash';
import dayjs from 'dayjs';
import fs from 'fs/promises';
import { inject, injectable } from 'inversify';
import { nanoid } from 'nanoid';
import { BadRequestException } from 'next-api-decorators';
import {
  normalizeDate,
  normalizeCurrency,
  normalizePercentage,
  normalizeBoolean,
  normalizeInt,
  extractNumbers,
} from 'common/tools/normalizer';

import { storage } from '@/lib/firebase-admin';
import { updateStartDateAndEndDate } from '@/lib/helpers/updateStartDateAndEndDate';
import { prismaClient } from '@/lib/prisma';
import { AppLoggerService } from '@/services/logger/appLogger';
import { DataStates } from '@/types';
import { BulkEditDto, bulkEditSchema } from './bulk-edit-validator';
import { DocumentFileService } from '@/services/documents/fileService';

@injectable()
export class DocumentsService {
  @inject(DocumentFileService) private documentFileService: DocumentFileService;

  private readonly logger = new AppLoggerService({
    defaultMeta: {
      service: 'DocumentsService',
    },
  });

  async bulkEdit(params: BulkEditDto) {
    const { ids, account_id, clearDataFields, uid, ouid, ...data } =
      await bulkEditSchema.parseAsync(params).catch(() => {
        throw { validationErr: true };
      });

    if (clearDataFields) {
      clearDataFields.filter(Boolean).forEach((field) => {
        data[field] = null;
      });
    }

    await prismaClient.documents.updateMany({
      where: {
        id: {
          in: ids,
        },
        account_id,
      },
      data: {
        ...data,
        updated_at: new Date(),
        updated_by: uid,
        updated_proxied_by: ouid,
      },
    });
    return true;
  }

  getFileType(fileName: string) {
    const path = fileName.toLowerCase();
    const isSpreadsheetFile =
      path.endsWith('.csv') || path.endsWith('.xlsx') || path.endsWith('.xls');
    if (isSpreadsheetFile) {
      return file_type.spreadsheet;
    } else if (
      path.endsWith('.png') ||
      path.endsWith('.jpg') ||
      path.endsWith('.jpeg')
    ) {
      return file_type.img;
    }
    return file_type.pdf;
  }

  async uploadFile(
    params: Omit<DocumentUploadParams, 'file'> & {
      localFilePath: string;
      accountId: string;
      uid: string;
      validations?: Record<string, boolean>;
      upload_source?: string;
    }
  ) {
    const {
      localFilePath,
      accountId,
      company_name,
      sync_id,
      company_id,
      upload_source,
      ...rest
    } = params;

    // Accept 'commission' and 'policy' for 'statement' and 'report respectively
    // TODO: Eventually migrate code/data to use policy (possibly commission) instead of report (and statement)
    const typeMap = {
      commission: 'statement',
      policy: 'report',
    };

    const companyCondition = {
      account_id: accountId,
      state: DataStates.ACTIVE,
    };

    // Just in case the company_name and external_company_id are not point to the same company
    if (company_name && company_id) {
      companyCondition['company_name'] = company_name;
      companyCondition['sync_id'] = company_id;
    } else {
      companyCondition['OR'] = [
        { company_name: company_name },
        { sync_id: company_id },
      ];
    }
    const company = await prismaClient.companies.findMany({
      where: companyCondition,
    });
    if (company.length === 0) {
      throw new BadRequestException('Company not found');
    }
    if (company.length > 1) {
      throw new BadRequestException('Multiple companies found');
    }

    if (sync_id) {
      const existingDocument = await prismaClient.documents.findFirst({
        where: { sync_id: sync_id, account_id: accountId },
      });
      if (existingDocument) {
        throw new BadRequestException(
          'Document already exists, sync_id should be unique'
        );
      }
    }

    const companyId = company[0].str_id;

    const filePath = `uploads/${accountId}/${nanoid()}-${rest.filename}`;
    const content = await fs.readFile(localFilePath);
    await storage.file(filePath).save(content);

    const fileHash = await sha256(content);

    const document = await prismaClient.documents.create({
      data: {
        ...rest,
        account_id: accountId,
        company_str_id: companyId,
        created_by: rest.uid,
        file_hash: fileHash,
        file_path: filePath,
        file_type: this.getFileType(rest.filename),
        sync_id: sync_id || null,
        sync_worker: WorkerNames.OpenAPI,
        type: typeMap[rest.type] ?? rest.type,
        uid: rest.uid,
        validations: rest.validations || {},
        upload_source: upload_source || null,
      },
      include: {
        companies: {
          where: { state: 'active' },
        },
      },
    });
    return document;
  }

  async getDocument(documentId: number, accountId: string) {
    try {
      return await prismaClient.documents.findFirst({
        where: {
          id: documentId,
          account_id: accountId,
        },
      });
    } catch (error) {
      captureException(error);
      throw error;
    }
  }

  // Use DocumentFileService to get files from storage to avoid circular dependencies
  async getFileFromStorage(document, maxRetries = 3, delay = 5000) {
    return this.documentFileService.getFileFromStorage(
      document,
      maxRetries,
      delay
    );
  }

  async getMonthList(params: GetDocumentGroupDTO) {
    const { companies, start_date, end_date, account_id, group_by } =
      getDocumentGroupSchema.parse(params);

    const { startDate, endDate } = updateStartDateAndEndDate(
      start_date,
      end_date
    );

    // Default is uploaded date (created_at)
    let dateTruncQuery = Prisma.sql`DATE_TRUNC('month', d.created_at) as date`;
    switch (group_by) {
      case GROUP_BY_VALUES.DEPOSIT_DATE:
        dateTruncQuery = Prisma.sql`DATE_TRUNC('month', d.deposit_date) as date`;
        break;

      case GROUP_BY_VALUES.PROCESSING_DATE:
        dateTruncQuery = Prisma.sql`DATE_TRUNC('month', s.processing_date) as date`;
        break;

      case GROUP_BY_VALUES.PAYMENT_DATE:
        dateTruncQuery = Prisma.sql`DATE_TRUNC('month', s.payment_date) as date`;
        break;

      default:
        break;
    }

    const res: DocumentGroupItem[] = await prismaClient.$queryRaw`
     SELECT 
        ${dateTruncQuery},
        CAST(COUNT(*) AS INTEGER) AS file_count, 
        CAST(SUM(d.statement_amount) AS DECIMAL) as total_statement_amount,
        CAST(SUM(d.bank_total_amount) AS DECIMAL) as total_bank_amount,
        CAST(SUM(s.commission_amount) AS DECIMAL) as total_commission_amount
      FROM documents d
        ${companies?.length ? Prisma.sql`JOIN companies c ON d.company_str_id = c.str_id` : Prisma.empty}
        LEFT JOIN (
        SELECT
          document_id,
          processing_date,
          payment_date,
          CAST(SUM(commission_amount) AS DECIMAL) as commission_amount
        FROM statement_data
        WHERE account_id=${account_id}
        AND state='active'
        GROUP BY document_id, processing_date, payment_date
      ) AS s
      ON d.str_id = s.document_id
      WHERE d.account_id=${account_id}
      AND d.state='active'
      ${companies?.length ? Prisma.sql`AND d.company_str_id IN(${Prisma.join(companies)})` : Prisma.empty}
      ${companies?.length ? Prisma.sql`AND c.state='active'` : Prisma.empty}
      ${start_date ? Prisma.sql`AND d.created_at >= ${startDate}` : Prisma.empty}
      ${end_date ? Prisma.sql`AND d.created_at < ${endDate}` : Prisma.empty}
      GROUP BY date
      ORDER BY date DESC;
    `;

    return res.map(
      ({
        date,
        file_count,
        total_bank_amount,
        total_commission_amount,
        total_statement_amount,
      }) => ({
        date: date ? dayjs(date).format('YYYY-MM-DD') : date,
        file_count,
        total_statement_amount: parseFloat(
          total_statement_amount as any as string
        ),
        total_bank_amount: parseFloat(total_bank_amount as any as string),
        total_commission_amount: parseFloat(
          total_commission_amount as any as string
        ),
      })
    );
  }

  async getDocumentsGroupByCompanies(params: GetDocumentGroupDTO) {
    const { account_id, start_date, end_date, companies } =
      getDocumentGroupSchema.parse(params);

    const { startDate, endDate } = updateStartDateAndEndDate(
      start_date,
      end_date
    );

    const res: DocumentGroupItem[] = await prismaClient.$queryRaw`
      SELECT 
        d.company_str_id,
        c.company_name,
        CAST(COUNT(*) AS INTEGER) AS file_count,
        CAST(SUM(d.statement_amount) AS DECIMAL) as total_statement_amount,
        CAST(SUM(d.bank_total_amount) AS DECIMAL) as total_bank_amount,
        CAST(SUM(s.commission_amount) AS DECIMAL) as total_commission_amount
      FROM documents d
        JOIN companies c ON d.company_str_id = c.str_id
        LEFT JOIN (
          SELECT
            document_id,
            CAST(SUM(commission_amount) AS DECIMAL) as commission_amount
          FROM statement_data
          WHERE account_id=${account_id}
          AND state='active'
          GROUP BY document_id
        ) AS s
      ON d.str_id = s.document_id
      WHERE d.account_id=${account_id}
      AND d.state='active'
      AND c.state='active'
      ${start_date ? Prisma.sql`AND d.created_at >= ${startDate}` : Prisma.empty}
      ${end_date ? Prisma.sql`AND d.created_at < ${endDate}` : Prisma.empty}
      ${companies?.length ? Prisma.sql`AND d.company_str_id IN(${Prisma.join(companies)})` : Prisma.empty}
      GROUP BY d.company_str_id, c.company_name;
    `;

    return res.map(
      ({
        company_str_id,
        company_name,
        file_count,
        total_bank_amount,
        total_statement_amount,
        total_commission_amount,
      }) => ({
        company_str_id,
        company_name,
        file_count,
        total_statement_amount: parseFloat(
          total_statement_amount as any as string
        ),
        total_bank_amount: parseFloat(total_bank_amount as any as string),
        total_commission_amount: parseFloat(
          total_commission_amount as any as string
        ),
      })
    );
  }

  async normalizeData(
    rawData: { data: any[]; fields: string[]; version?: string },
    type: string
  ) {
    try {
      if (!rawData || !Array.isArray(rawData.data)) {
        return rawData;
      }

      const fieldsConfig = await prismaClient.fields.findMany({
        where: {
          model: type + 's',
          state: DataStates.ACTIVE,
        },
      });

      const fieldMapping = rawData.fields.map((header) => {
        const matchedField = fieldsConfig.find(
          (config) =>
            config.key?.toLowerCase() === header.toLowerCase() ||
            config.matches?.some(
              (match) => header.toLowerCase() === match.toLowerCase()
            )
        );
        return matchedField || { key: header, type: '' };
      });

      const normalizedData = rawData.data.map((row) =>
        row.map((value, index) => {
          const field = fieldMapping[index];

          if (!field) return value;

          switch (field.type) {
            case 'date':
              return normalizeDate(value);
            case 'currency':
              return normalizeCurrency(value);
            case 'percentage':
              return normalizePercentage(value);
            case 'boolean':
              return normalizeBoolean(value);
            case 'integer':
              return normalizeInt(value);
            case 'decimal':
              return extractNumbers(value);
            default:
              return value;
          }
        })
      );

      return {
        ...rawData,
        data: normalizedData,
      };
    } catch (error) {
      Sentry.captureException(error);
      throw error;
    }
  }
}
