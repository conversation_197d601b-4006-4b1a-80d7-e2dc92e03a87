import { describe, it, expect, vi, beforeEach } from 'vitest';
import { Prisma } from '@prisma/client';

import { ManageAccountingDataByContactInput } from './index';
import { AccountingService } from '@/services/accounting';
import { prismaClient } from '@/lib/prisma';
import { accountingHelpers } from './accounting-helpers';
import { DataStates } from '@/types';

vi.mock('@/lib/prisma', () => ({
  prismaClient: {
    accounting_transactions: {
      findMany: vi.fn(),
      upsert: vi.fn(),
      updateMany: vi.fn(),
      count: vi.fn(),
    },
    accounting_transaction_details: {
      upsert: vi.fn(),
      updateMany: vi.fn(),
    },
    contacts: {
      update: vi.fn(),
    },
    $transaction: vi.fn((queries) => Promise.all(queries)),
  },
}));

vi.mock('./accounting-helpers', () => ({
  accountingHelpers: {
    prepareTransactionUpsertData: vi.fn(),
    prepareTransactionDetailsUpsertData: vi.fn(),
    calculateNewBalance: vi.fn(),
    getPagination: vi.fn().mockImplementation((total, page, limit) => ({
      total_transactions: total,
      current_page: page,
      total_pages: Math.ceil(total / limit),
    })),
  },
}));

describe('AccountingService.getAccountingTransactionsByContact', () => {
  let accountingService: AccountingService;
  const mockContactStrId = 'contact-123';
  const mockContactId = 123;
  const mockStartDate = new Date('2023-01-01');
  const mockEndDate = new Date('2023-12-31');
  const mockTransactionStrId = 'transaction-123';
  const mockTransactions = [{ id: 1 }, { id: 2 }];
  const mockCount = 2;

  beforeEach(() => {
    accountingService = new AccountingService();
    vi.resetAllMocks();

    accountingService.contactService = {
      getContactIdsByStrIdList: vi.fn(),
    } as any;

    vi.mocked(
      accountingService.contactService.getContactIdsByStrIdList
    ).mockResolvedValue([mockContactId]);
    vi.mocked(prismaClient.accounting_transactions.findMany).mockResolvedValue(
      mockTransactions
    );
    vi.mocked(prismaClient.accounting_transactions.count).mockResolvedValue(
      mockCount
    );
  });

  it('Should return transactions and count with valid contact ID', async () => {
    const result = await accountingService.getAccountingTransactionsByContact({
      agent_str_id: mockContactStrId,
    });

    expect(
      accountingService.contactService.getContactIdsByStrIdList
    ).toHaveBeenCalledWith([mockContactStrId]);
    expect(prismaClient.accounting_transactions.findMany).toHaveBeenCalledWith(
      expect.objectContaining({
        where: {
          contact_id: mockContactId,
          state: DataStates.ACTIVE,
        },
        select: expect.any(Object),
      })
    );
    expect(result).toEqual({
      transactions: mockTransactions,
      total_transactions: mockCount,
      pagination: undefined,
    });
  });

  it('Should return empty list and count with 0 when receives invalid contact ID', async () => {
    vi.mocked(
      accountingService.contactService.getContactIdsByStrIdList
    ).mockResolvedValue([]);

    const result = await accountingService.getAccountingTransactionsByContact({
      agent_str_id: mockContactStrId,
    });

    expect(
      accountingService.contactService.getContactIdsByStrIdList
    ).toHaveBeenCalledWith([mockContactStrId]);
    expect(prismaClient.$transaction).not.toHaveBeenCalled();
    expect(result).toEqual({
      total_transactions: 0,
      transactions: [],
    });
  });

  it('Should apply date filters correctly with start and end dates', async () => {
    await accountingService.getAccountingTransactionsByContact({
      agent_str_id: mockContactStrId,
      start_date: mockStartDate,
      end_date: mockEndDate,
    });

    const expectedWhereClause: Prisma.accounting_transactionsWhereInput = {
      contact_id: mockContactId,
      state: DataStates.ACTIVE,
      date: {
        gte: mockStartDate,
        lte: mockEndDate,
      },
    };

    expect(prismaClient.accounting_transactions.findMany).toHaveBeenCalledWith(
      expect.objectContaining({
        where: expectedWhereClause,
      })
    );
  });

  it('Should apply start date filter correctly with only start date', async () => {
    await accountingService.getAccountingTransactionsByContact({
      agent_str_id: mockContactStrId,
      start_date: mockStartDate,
    });

    const expectedWhereClause: Prisma.accounting_transactionsWhereInput = {
      contact_id: mockContactId,
      state: DataStates.ACTIVE,
      date: {
        gte: mockStartDate,
      },
    };

    expect(prismaClient.accounting_transactions.findMany).toHaveBeenCalledWith(
      expect.objectContaining({
        where: expectedWhereClause,
      })
    );
  });

  it('Should apply end date filter correctly with only end date', async () => {
    await accountingService.getAccountingTransactionsByContact({
      agent_str_id: mockContactStrId,
      start_date: undefined,
      end_date: mockEndDate,
    });

    const expectedWhereClause: Prisma.accounting_transactionsWhereInput = {
      contact_id: mockContactId,
      state: DataStates.ACTIVE,
      date: {
        lte: mockEndDate,
      },
    };

    expect(prismaClient.accounting_transactions.findMany).toHaveBeenCalledWith(
      expect.objectContaining({
        where: expectedWhereClause,
      })
    );
  });

  it('Should apply transaction filter correctly with transaction string ID', async () => {
    await accountingService.getAccountingTransactionsByContact({
      agent_str_id: mockContactStrId,
      start_date: undefined,
      end_date: undefined,
      transaction_str_id: mockTransactionStrId,
    });

    const expectedWhereClause: Prisma.accounting_transactionsWhereInput = {
      contact_id: mockContactId,
      state: DataStates.ACTIVE,
      str_id: mockTransactionStrId,
    };

    expect(prismaClient.accounting_transactions.findMany).toHaveBeenCalledWith(
      expect.objectContaining({
        where: expectedWhereClause,
      })
    );
  });

  it('Should apply all filters correctly with multiple filter parameters', async () => {
    await accountingService.getAccountingTransactionsByContact({
      agent_str_id: mockContactStrId,
      start_date: mockStartDate,
      end_date: mockEndDate,
      transaction_str_id: mockTransactionStrId,
    });

    const expectedWhereClause: Prisma.accounting_transactionsWhereInput = {
      contact_id: mockContactId,
      state: DataStates.ACTIVE,
      date: {
        gte: mockStartDate,
        lte: mockEndDate,
      },
      str_id: mockTransactionStrId,
    };

    expect(prismaClient.accounting_transactions.findMany).toHaveBeenCalledWith(
      expect.objectContaining({
        where: expectedWhereClause,
      })
    );
  });

  it('Should execute transaction with correct queries with valid parameters', async () => {
    await accountingService.getAccountingTransactionsByContact({
      agent_str_id: mockContactStrId,
    });

    expect(prismaClient.$transaction).toHaveBeenCalledWith([
      expect.any(Promise),
      expect.any(Promise),
    ]);
  });

  describe('When pagination is defined', () => {
    it('Should call findMany with correct skip and take', async () => {
      const page = 2;
      const limit = 5;
      await accountingService.getAccountingTransactionsByContact({
        agent_str_id: mockContactStrId,
        pagination: { page, limit },
      });

      expect(
        prismaClient.accounting_transactions.findMany
      ).toHaveBeenCalledWith(
        expect.objectContaining({
          skip: 5,
          take: 5,
        })
      );
    });

    it('Should call findMany with skip 0 when page is 1', async () => {
      const page = 1;
      const limit = 10;
      await accountingService.getAccountingTransactionsByContact({
        agent_str_id: mockContactStrId,
        pagination: { page, limit },
      });

      expect(
        prismaClient.accounting_transactions.findMany
      ).toHaveBeenCalledWith(
        expect.objectContaining({
          skip: 0,
          take: 10,
        })
      );
    });
  });
  describe('AccountingService.manageAccountingDataByContact', () => {
    let accountingService: AccountingService;
    const mockInput: ManageAccountingDataByContactInput = {
      contactId: 1,
      accountId: 'acc-123',
      uid: 'user-123',
      ouid: 'org-user-123',
      updatedAccountingData: [{ str_id: 'updated-1' }],
      deletedAccountingData: [{ str_id: 'deleted-1', details: [] }],
    };
    const mockUpsertData = [{ where: {}, create: {}, update: {} }];
    const mockUpsertResults = [{ id: 1 }];
    const mockDetailsUpsertData = [{ where: {}, create: {}, update: {} }];
    const mockNewBalance = 1000;

    beforeEach(() => {
      accountingService = new AccountingService();
      vi.resetAllMocks();

      vi.mocked(accountingHelpers.prepareTransactionUpsertData).mockReturnValue(
        mockUpsertData as any
      );
      vi.mocked(
        accountingHelpers.prepareTransactionDetailsUpsertData
      ).mockReturnValue(mockDetailsUpsertData as any);
      vi.mocked(accountingHelpers.calculateNewBalance).mockReturnValue(
        mockNewBalance
      );
      vi.mocked(prismaClient.$transaction).mockImplementation(
        async (queries) => {
          if (Array.isArray(queries)) {
            // If upsert queries, return mockUpsertResults
            if (
              queries.some(
                (q) =>
                  q && 'model' in q && q.model === 'accounting_transactions'
              )
            ) {
              return mockUpsertResults;
            }
            // For delete/details upserts, return an array of objects to match expected shape
            return mockUpsertResults;
          }
          return Promise.resolve([]);
        }
      );
    });

    it('Given valid data, should process updates, deletions, and update balance', async () => {
      const result =
        await accountingService.manageAccountingDataByContact(mockInput);

      expect(
        accountingHelpers.prepareTransactionUpsertData
      ).toHaveBeenCalledWith(
        mockInput.updatedAccountingData,
        mockInput.contactId,
        mockInput.accountId,
        mockInput.uid,
        mockInput.ouid
      );

      expect(
        accountingHelpers.prepareTransactionDetailsUpsertData
      ).toHaveBeenCalledWith(
        mockInput.updatedAccountingData,
        mockUpsertResults,
        mockInput.contactId,
        mockInput.uid,
        mockInput.ouid
      );

      expect(accountingHelpers.calculateNewBalance).toHaveBeenCalled();
      expect(prismaClient.contacts.update).toHaveBeenCalledWith({
        where: { id: mockInput.contactId },
        data: {
          balance: mockNewBalance,
          updated_at: expect.any(Date),
          updated_by: mockInput.uid,
          updated_proxied_by: mockInput.ouid,
        },
      });

      expect(result).toBe(true);
    });

    it('Given only updated data, should process upserts and skip deletions', async () => {
      const input = { ...mockInput, deletedAccountingData: [] };
      await accountingService.manageAccountingDataByContact(input);

      expect(
        prismaClient.accounting_transactions.updateMany
      ).not.toHaveBeenCalled();
      expect(
        prismaClient.accounting_transaction_details.updateMany
      ).not.toHaveBeenCalled();
      expect(
        accountingHelpers.prepareTransactionUpsertData
      ).toHaveBeenCalledTimes(1);
      expect(
        accountingHelpers.prepareTransactionDetailsUpsertData
      ).toHaveBeenCalledTimes(1);
      expect(prismaClient.contacts.update).toHaveBeenCalledTimes(1);
    });

    it('Given only deleted data, should process deletions and skip upserts', async () => {
      const deletedDetails = [{ str_id: 'deleted-detail-1' }];
      const input = {
        ...mockInput,
        updatedAccountingData: [],
        deletedAccountingData: [
          { str_id: 'deleted-1', details: deletedDetails },
        ],
      };
      vi.mocked(accountingHelpers.prepareTransactionUpsertData).mockReturnValue(
        []
      );
      vi.mocked(
        accountingHelpers.prepareTransactionDetailsUpsertData
      ).mockReturnValue([]);

      await accountingService.manageAccountingDataByContact(input);

      expect(
        accountingHelpers.prepareTransactionUpsertData
      ).toHaveBeenCalledWith(
        [],
        input.contactId,
        input.accountId,
        input.uid,
        input.ouid
      );
      expect(
        accountingHelpers.prepareTransactionDetailsUpsertData
      ).toHaveBeenCalled();
      expect(
        prismaClient.accounting_transactions.updateMany
      ).toHaveBeenCalledWith({
        where: { str_id: { in: ['deleted-1'] } },
        data: expect.any(Object),
      });
      expect(
        prismaClient.accounting_transaction_details.updateMany
      ).toHaveBeenCalledWith({
        where: { str_id: { in: ['deleted-detail-1'] } },
        data: expect.any(Object),
      });
      expect(prismaClient.contacts.update).toHaveBeenCalledTimes(1);
    });

    it('Given an error occurs, should log the error and return false', async () => {
      const error = new Error('Database error');
      vi.mocked(prismaClient.$transaction).mockRejectedValue(error);

      const result =
        await accountingService.manageAccountingDataByContact(mockInput);

      expect(result).toBe(false);
    });
  });
});
