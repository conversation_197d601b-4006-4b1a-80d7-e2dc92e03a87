import { isNil } from 'lodash-es';
import * as Sentry from '@sentry/nextjs';
import currency from 'currency.js';
import { inject, injectable } from 'inversify';
import { TransactionStatuses } from 'common/globalTypes';
import { AccountingTransactionsType } from 'common/constants/accounting_transactions';
import { Prisma } from '@prisma/client';

import { prismaClient } from '@/lib/prisma';
import { DataStates } from '@/types';
import { BalanceReponse } from './types';
import { accountingHelpers } from './accounting-helpers';
import { ContactService } from '@/services/contact';

@injectable()
export class AccountingService implements IAccountingService {
  @inject(ContactService) contactService: ContactService;

  async manageAccountingDataByContact(
    input: ManageAccountingDataByContactInput
  ): Promise<boolean> {
    const {
      contactId,
      accountId,
      uid,
      ouid,
      updatedAccountingData,
      deletedAccountingData,
    } = input;
    try {
      // 1. Prepare and execute transaction upserts
      const upsertData = accountingHelpers.prepareTransactionUpsertData(
        updatedAccountingData,
        contactId,
        accountId,
        uid,
        ouid
      );

      const upsertResults = await prismaClient.$transaction(
        upsertData.map((data) =>
          prismaClient.accounting_transactions.upsert(data)
        )
      );

      // 2. Prepare transaction details upsert operations
      const transactionDetailsUpsertData =
        accountingHelpers.prepareTransactionDetailsUpsertData(
          updatedAccountingData,
          upsertResults,
          contactId,
          uid,
          ouid
        );

      // 3. Execute remaining operations in a transaction
      const transactionOperations = [];
      if (deletedAccountingData.length > 0) {
        transactionOperations.push(
          prismaClient.accounting_transactions.updateMany({
            where: {
              str_id: { in: deletedAccountingData.map(({ str_id }) => str_id) },
            },
            data: {
              state: DataStates.DELETED,
              updated_at: new Date(),
              updated_by: uid,
              updated_proxied_by: ouid,
            },
          })
        );
      }

      const detailsIdsToDelete = deletedAccountingData.flatMap(({ details }) =>
        details.map(({ str_id }) => str_id)
      );

      if (detailsIdsToDelete.length > 0) {
        transactionOperations.push(
          prismaClient.accounting_transaction_details.updateMany({
            where: { str_id: { in: detailsIdsToDelete } },
            data: {
              state: DataStates.DELETED,
              updated_at: new Date(),
              updated_by: uid,
              updated_proxied_by: ouid,
            },
          })
        );
      }

      transactionOperations.push(
        ...transactionDetailsUpsertData.map((data) =>
          prismaClient.accounting_transaction_details.upsert(data)
        )
      );

      const res = await prismaClient.$transaction(transactionOperations);

      // 4. Update contact balance
      const newBalance = accountingHelpers.calculateNewBalance(res);
      await prismaClient.contacts.update({
        where: { id: contactId },
        data: {
          balance: newBalance,
          updated_at: new Date(),
          updated_by: uid,
          updated_proxied_by: ouid,
        },
      });

      return true;
    } catch (error) {
      console.error('Error in manageAccountingDataByContact:', error);
      Sentry.captureException(error);
      return false;
    }
  }

  async approveTransactionWithDetails(
    accountId: string,
    transactionId: number,
    uid: string,
    ouid: string
  ): Promise<boolean> {
    const transaction = await prismaClient.accounting_transactions.findFirst({
      where: {
        id: transactionId,
        account_id: accountId,
        state: DataStates.ACTIVE,
        status: {
          equals: TransactionStatuses.DRAFT,
          mode: 'insensitive',
        },
      },
      select: {
        id: true,
        amount: true,
        contact_id: true,
      },
    });

    if (!transaction) {
      return false;
    }

    // Get current contact balance and calculate new balance using the report transaction
    const contactBalance = await prismaClient.contacts.findUnique({
      where: {
        id: transaction.contact_id,
        account_id: accountId,
        state: DataStates.ACTIVE,
      },
      select: {
        balance: true,
      },
    });

    const newBalance = currency(Number(contactBalance.balance)).add(
      currency(Number(transaction.amount))
    ).value;

    await prismaClient.$transaction([
      prismaClient.accounting_transactions.update({
        where: {
          id: transactionId,
        },
        data: {
          status: TransactionStatuses.APPROVED,
          date: new Date(),
          updated_at: new Date(),
          updated_by: uid,
          updated_proxied_by: ouid,
          accounting_transaction_details: {
            updateMany: {
              where: {
                state: DataStates.ACTIVE,
                status: {
                  equals: TransactionStatuses.DRAFT,
                  mode: 'insensitive',
                },
              },
              data: {
                status: TransactionStatuses.APPROVED,
                date: new Date(),
                updated_at: new Date(),
                updated_by: uid,
                updated_proxied_by: ouid,
              },
            },
          },
        },
      }),
      prismaClient.contacts.update({
        where: {
          id: transaction.contact_id,
        },
        data: {
          balance: newBalance,
          updated_at: new Date(),
          updated_by: uid,
          updated_proxied_by: ouid,
        },
      }),
    ]);

    return true;
  }

  async payTransactionWithDetails(
    reportTransactionIdToPay: number,
    payedTransactionId: number,
    savedReportId: number,
    accountId: string,
    contactId: number,
    uid: string,
    ouid: string
  ): Promise<boolean> {
    const transaction = await prismaClient.accounting_transactions.findFirst({
      where: {
        id: reportTransactionIdToPay,
        account_id: accountId,
        state: DataStates.ACTIVE,
        status: {
          equals: TransactionStatuses.APPROVED,
          mode: 'insensitive',
        },
      },
      select: {
        id: true,
        amount: true,
        contact_id: true,
      },
    });

    if (!transaction) {
      return false;
    }

    // Get current contact balance and calculate new balance using the report transaction
    const contactBalance = await prismaClient.contacts.findUnique({
      where: {
        id: transaction.contact_id,
        account_id: accountId,
        state: DataStates.ACTIVE,
      },
      select: {
        balance: true,
      },
    });

    const amountToPay = currency(Number(transaction.amount));
    const newBalance = currency(Number(contactBalance.balance)).subtract(
      amountToPay
    ).value;

    await prismaClient.$transaction([
      prismaClient.accounting_transactions.update({
        where: {
          id: reportTransactionIdToPay,
        },
        data: {
          status: TransactionStatuses.PAID,
          updated_at: new Date(),
          updated_by: uid,
          updated_proxied_by: ouid,
          accounting_transaction_details: {
            updateMany: {
              where: {
                state: DataStates.ACTIVE,
                status: {
                  equals: TransactionStatuses.APPROVED,
                  mode: 'insensitive',
                },
              },
              data: {
                status: TransactionStatuses.PAID,
                updated_at: new Date(),
                updated_by: uid,
                updated_proxied_by: ouid,
              },
            },
          },
        },
      }),
      prismaClient.accounting_transaction_details.create({
        data: {
          account_id: accountId,
          contact_id: contactId,
          created_by: uid,
          created_proxied_by: ouid,
          status: TransactionStatuses.SETTLEMENT,
          amount: -transaction.amount,
          date: new Date(),
          notes: 'Paid transaction',
          accounting_transactions: {
            connect: {
              id: payedTransactionId,
            },
          },
          saved_report_id: savedReportId,
        },
      }),
      prismaClient.contacts.update({
        where: {
          id: transaction.contact_id,
        },
        data: {
          balance: newBalance,
          updated_at: new Date(),
          updated_by: uid,
          updated_proxied_by: ouid,
        },
      }),
    ]);

    return true;
  }

  async linkTransactionToReport(
    accountId: string,
    transactionId: number,
    reportId: number,
    uid: string,
    ouid: string
  ): Promise<boolean> {
    const report = await prismaClient.saved_reports.findUnique({
      where: {
        id: reportId,
        account_id: accountId,
        state: DataStates.ACTIVE,
      },
    });

    if (!report) {
      return false;
    }

    const transaction = await prismaClient.accounting_transactions.update({
      where: {
        id: transactionId,
        account_id: accountId,
        state: DataStates.ACTIVE,
      },
      data: {
        saved_report_id: reportId,
        updated_at: new Date(),
        updated_by: uid,
        updated_proxied_by: ouid,
      },
    });

    return !!transaction;
  }

  async createTransactionWithDetails(
    accountId: string,
    contactId: number,
    transactionDetailsIds: number[],
    transactionDetailsAmounts: number[],
    uid: string,
    ouid: string
  ): Promise<BalanceReponse> {
    const totalAmount = transactionDetailsAmounts.reduce(
      (acc, amount) => currency(acc).add(currency(amount)).value,
      currency(0).value
    );

    let transactionId = null;
    if (transactionDetailsIds.length > 0) {
      const transaction = await prismaClient.accounting_transactions.create({
        data: {
          account_id: accountId,
          contact_id: contactId,
          created_by: uid,
          created_proxied_by: ouid,
          status: TransactionStatuses.DRAFT,
          amount: totalAmount,
          date: new Date(),
          type: AccountingTransactionsType.COMP_REPORT,
          accounting_transaction_details: {
            connect: transactionDetailsIds.map((id) => ({ id })),
          },
        },
      });

      transactionId = transaction.id;
    }

    const contactBalance = await prismaClient.contacts.findUnique({
      where: {
        id: contactId,
        account_id: accountId,
        state: DataStates.ACTIVE,
      },
      select: {
        balance: true,
      },
    });

    return {
      reportBalance: Number(totalAmount),
      contactBalance: Number(contactBalance.balance),
      currentBalance: Number(
        currency(Number(contactBalance.balance)).add(currency(totalAmount))
          .value
      ),
      transactionId: transactionId,
    };
  }

  async getAccountingTransactionsByContact(
    params: GetAccountingTransactionsByContactInput
  ): Promise<GetAccountingTransactionsByContactResponse> {
    const {
      agent_str_id: contactStrId,
      start_date: startDate,
      end_date: endDate,
      transaction_str_id: transactionStrId,
    } = params;

    const contactId = await this.contactService.getContactIdsByStrIdList([
      contactStrId,
    ]);

    if (!contactId || contactId.length === 0) {
      return { transactions: [], total_transactions: 0 };
    }

    const whereClause: Prisma.accounting_transactionsWhereInput = {
      contact_id: contactId[0],
      state: DataStates.ACTIVE,
    };

    if (startDate || endDate) {
      whereClause.date = {};

      if (startDate) {
        whereClause.date.gte = startDate;
      }

      if (endDate) {
        whereClause.date.lte = endDate;
      }
    }

    if (transactionStrId) {
      whereClause.str_id = transactionStrId;
    }

    const { page, limit: take } = params.pagination || {};
    // Calculate 'skip' only if both page are valid, otherwise, 'skip' will be should be undefined
    // If 'page' is undefined, it will default to 0, which is the first page.
    // If 'page' is 1, we want to skip 0 items, so we need to use the formula: (page - 1) * take
    const skip = !isNil(page) && !isNil(take) ? (page - 1) * take : undefined;

    const [transactions, totalTransactions] = await prismaClient.$transaction([
      prismaClient.accounting_transactions.findMany({
        where: whereClause,
        skip,
        take,
        select: {
          id: true,
          str_id: true,
          amount: true,
          date: true,
          status: true,
          type: true,
          notes: true,
          saved_report_id: true,
          contact_id: true,
          saved_report: {
            select: {
              id: true,
              str_id: true,
              name: true,
            },
          },
          accounting_transaction_details: {
            where: { state: DataStates.ACTIVE },
            select: {
              id: true,
              str_id: true,
              amount: true,
              date: true,
              status: true,
              notes: true,
              tags: true,
              statement_id: true,
            },
          },
        },
      }),
      prismaClient.accounting_transactions.count({
        where: whereClause,
      }),
    ]);

    const pagination =
      params.pagination &&
      accountingHelpers.getPagination({
        currentPage: params.pagination.page,
        totalItemsPerPage: params.pagination.limit,
        totalItems: totalTransactions,
      });

    return { transactions, total_transactions: totalTransactions, pagination };
  }
}

type GetAccountingTransactionsByContactInput = {
  agent_str_id: string;
  start_date?: Date;
  end_date?: Date;
  transaction_str_id?: string;
  pagination?: { page: number; limit: number };
};

type GetAccountingTransactionsByContactResponse = {
  transactions: any[];
  total_transactions: number;
  pagination?: {
    current_page: number;
    previous_page: number;
    next_page: number;
    last_page: number;
    total_pages: number;
    total_items_per_page: number;
  };
};

export type ManageAccountingDataByContactInput = {
  contactId: number;
  accountId: string;
  uid: string;
  ouid: string;
  updatedAccountingData: any[];
  deletedAccountingData: any[];
};

export interface IAccountingService {
  manageAccountingDataByContact(
    input: ManageAccountingDataByContactInput
  ): Promise<boolean>;
  createTransactionWithDetails(
    accountId: string,
    contactId: number,
    transactionDetailsIds: number[],
    transactionDetailsAmounts: number[],
    uid: string,
    ouid: string
  ): Promise<BalanceReponse>;
  approveTransactionWithDetails(
    accountId: string,
    transactionId: number,
    uid: string,
    ouid: string
  ): Promise<boolean>;
  payTransactionWithDetails(
    reportTransactionIdToPay: number,
    payedTransactionId: number,
    savedReportId: number,
    accountId: string,
    contactId: number,
    uid: string,
    ouid: string
  ): Promise<boolean>;
  linkTransactionToReport(
    accountId: string,
    transactionId: number,
    reportId: number,
    uid: string,
    ouid: string
  ): Promise<boolean>;
  getAccountingTransactionsByContact(
    params: GetAccountingTransactionsByContactInput
  ): Promise<GetAccountingTransactionsByContactResponse>;
}
