import { convertDateFields } from 'common/helpers';
import { TransactionStatuses } from 'common/globalTypes';
import BigNumber from 'bignumber.js';

import { AccountingData, AccountingTransactionDetail } from './types';
import { DataStates } from '@/types';

/**
 * Converts mixed tag formats to a uniform string array
 */
const processTags = (tags?: string | string[]): string[] => {
  if (!tags) return [];

  return typeof tags === 'string'
    ? tags.split(',').map((s) => s.trim())
    : Array.isArray(tags)
      ? tags
      : [];
};

/**
 * Maps transaction detail data for create operations
 */
const mapTransactionDetailForCreate = (
  detail: AccountingTransactionDetail,
  contactId: number,
  uid: string,
  ouid: string
): any => {
  return {
    amount: +detail.amount,
    contact_id: contactId,
    date: new Date(detail.date),
    status: detail.status,
    notes: detail.notes,
    created_by: uid,
    created_proxied_by: ouid,
    tags: processTags(detail.tags),
  };
};

/**
 * Prepares accounting transaction data for upsert operations
 */
const prepareTransactionUpsertData = (
  accountingData: AccountingData[],
  contactId: number,
  accountId: string,
  uid: string,
  ouid: string
): any[] => {
  return accountingData.map((datum) => {
    datum = convertDateFields(datum) as AccountingData;
    return {
      where: {
        // If str_id is falsy that means that the record is new so we use the FE generated id
        str_id: datum.transaction_str_id
          ? datum.transaction_str_id
          : String(datum.id),
      },
      update: {
        updated_at: new Date(),
        updated_by: uid,
        updated_proxied_by: ouid,
        amount: +datum.amount,
        date: datum.date,
        notes: datum.notes,
        status: datum.status,
      },
      create: {
        contact_id: contactId,
        account_id: accountId,
        created_by: uid,
        created_proxied_by: ouid,
        amount: +datum.amount,
        date: datum.date,
        notes: datum.notes,
        status: datum.status,
      },
    };
  });
};

/**
 * Prepares transaction details data for upsert operations
 */
const prepareTransactionDetailsUpsertData = (
  accountingData: AccountingData[],
  upsertResults: any[],
  contactId: number,
  uid: string,
  ouid: string
): any[] => {
  return accountingData.flatMap((datum, index) => {
    const transactionDetails = datum.details;
    if (
      !transactionDetails ||
      !Array.isArray(transactionDetails) ||
      transactionDetails.length === 0
    ) {
      return [];
    }

    return transactionDetails.map((detail) => {
      detail = convertDateFields(detail) as AccountingTransactionDetail;
      const tagArray = processTags(detail.tags);

      return {
        where: {
          // If str_id is falsy that means that the record is new so we use the FE generated id
          str_id: detail.transaction_detail_str_id
            ? detail.transaction_detail_str_id
            : String(detail.id),
        },
        update: {
          updated_at: new Date(),
          updated_by: uid,
          updated_proxied_by: ouid,
          contact: { connect: { id: contactId } },
          amount: detail.amount,
          status: detail.status,
          notes: detail.notes,
          date: detail.date,
          tags: tagArray,
        },
        create: {
          accounting_transactions: { connect: { id: upsertResults[index].id } },
          contact: { connect: { id: contactId } },
          created_by: uid,
          created_proxied_by: ouid,
          amount: detail.amount,
          status: detail.status,
          notes: detail.notes,
          date: detail.date,
          tags: tagArray,
        },
      };
    });
  });
};

/**
 * Calculates the new balance based on transaction results
 */
const calculateNewBalance = (transactionResults: any[]): number => {
  return transactionResults.reduce((acc, result) => {
    if (
      [TransactionStatuses.APPROVED, TransactionStatuses.PAID].includes(
        result.status
      ) &&
      result.state === DataStates.ACTIVE &&
      result.amount
    ) {
      return new BigNumber(acc).plus(new BigNumber(result.amount)).toNumber();
    }
    return acc;
  }, 0);
};

const getPagination = (input: {
  currentPage: number;
  totalItemsPerPage: number;
  totalItems: number;
}) => {
  const { currentPage, totalItems } = input;

  const totalPages = Math.ceil(totalItems / input.totalItemsPerPage);
  const totalItemsPerPage =
    input.totalItemsPerPage < totalItems ? input.totalItemsPerPage : totalItems;

  const isFirstPage = currentPage === 0;
  const isLastPage = currentPage >= totalPages - 1;

  const nextPage = isLastPage ? undefined : currentPage + 1;
  const previousPage = isFirstPage ? undefined : currentPage - 1;
  const lastPage = totalPages > 0 ? totalPages : undefined;

  return {
    current_page: currentPage,
    previous_page: previousPage,
    next_page: nextPage,
    last_page: lastPage,
    total_pages: totalPages,
    total_items_per_page: totalItemsPerPage,
  };
};

export const accountingHelpers = {
  processTags,
  mapTransactionDetailForCreate,
  prepareTransactionUpsertData,
  prepareTransactionDetailsUpsertData,
  calculateNewBalance,
  getPagination,
};
