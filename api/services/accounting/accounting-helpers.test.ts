import { describe, it, expect, vi, beforeEach } from 'vitest';
import { convertDateFields } from 'common/helpers';
import { TransactionStatuses } from 'common/globalTypes';

import { accountingHelpers } from './accounting-helpers';
import { AccountingData } from './types';

vi.mock('common/helpers', () => ({
  convertDateFields: vi.fn((data) => data),
}));

describe('accountingHelpers.prepareTransactionDetailsUpsertData', () => {
  beforeEach(() => {
    vi.resetAllMocks();
  });

  it('Should create proper upsert data structure for transaction details', () => {
    const accountingData: AccountingData[] = [
      {
        id: 1,
        transaction_str_id: 'txn1',
        amount: 100,
        date: new Date('2023-01-01'),
        notes: 'Test transaction',
        status: TransactionStatuses.APPROVED,
        details: [
          {
            id: 101,
            transaction_detail_str_id: 'dtl101',
            amount: 50,
            date: new Date('2023-01-01'),
            status: TransactionStatuses.APPROVED,
            notes: 'Detail 1',
            tags: 'tag1,tag2',
          },
        ],
      },
    ];

    const upsertResults = [{ id: 1000 }];
    const contactId = 5000;
    const uid = 'user123';
    const ouid = 'org456';

    const result = accountingHelpers.prepareTransactionDetailsUpsertData(
      accountingData,
      upsertResults,
      contactId,
      uid,
      ouid
    );

    expect(result).toHaveLength(1);
    expect(result[0]).toEqual({
      where: { str_id: 'dtl101' },
      update: {
        updated_at: expect.any(Date),
        updated_by: uid,
        updated_proxied_by: ouid,
        contact: { connect: { id: contactId } },
        amount: 50,
        status: TransactionStatuses.APPROVED,
        notes: 'Detail 1',
        date: new Date('2023-01-01'),
        tags: ['tag1', 'tag2'],
      },
      create: {
        accounting_transactions: { connect: { id: 1000 } },
        contact: { connect: { id: contactId } },
        created_by: uid,
        created_proxied_by: ouid,
        amount: 50,
        status: TransactionStatuses.APPROVED,
        notes: 'Detail 1',
        date: new Date('2023-01-01'),
        tags: ['tag1', 'tag2'],
      },
    });
    expect(convertDateFields).toHaveBeenCalled();
  });

  it('Should return empty array when accounting data has no transaction details', () => {
    const accountingData: AccountingData[] = [
      {
        id: 1,
        transaction_str_id: 'txn1',
        amount: 100,
        date: new Date('2023-01-01'),
        notes: 'Test transaction',
        status: TransactionStatuses.APPROVED,
        details: [], // Empty transaction details
      },
      {
        id: 2,
        transaction_str_id: 'txn2',
        amount: 200,
        date: new Date('2023-01-02'),
        notes: 'Another transaction',
        status: 'PENDING',
        details: undefined, // Undefined transaction details
      },
    ];

    const upsertResults = [{ id: 1000 }, { id: 1001 }];
    const result = accountingHelpers.prepareTransactionDetailsUpsertData(
      accountingData,
      upsertResults,
      5000,
      'user123',
      'org456'
    );

    expect(result).toEqual([]);
  });

  it('Should handle multiple transaction details per record', () => {
    const accountingData: AccountingData[] = [
      {
        id: 1,
        transaction_str_id: 'txn1',
        amount: 100,
        date: new Date('2023-01-01'),
        notes: 'Transaction with multiple details',
        status: TransactionStatuses.APPROVED,
        details: [
          {
            id: 101,
            transaction_detail_str_id: 'dtl101',
            amount: 30,
            date: new Date('2023-01-01'),
            status: TransactionStatuses.APPROVED,
            notes: 'Detail 1',
            tags: 'tag1',
          },
          {
            id: 102,
            transaction_detail_str_id: 'dtl102',
            amount: 70,
            date: new Date('2023-01-01'),
            status: TransactionStatuses.APPROVED,
            notes: 'Detail 2',
            tags: 'tag2',
          },
        ],
      },
    ];

    const upsertResults = [{ id: 1000 }];
    const result = accountingHelpers.prepareTransactionDetailsUpsertData(
      accountingData,
      upsertResults,
      5000,
      'user123',
      'org456'
    );

    expect(result).toHaveLength(2);
    expect(result[0].where.str_id).toBe('dtl101');
    expect(result[1].where.str_id).toBe('dtl102');
    expect(result[0].create.amount).toBe(30);
    expect(result[1].create.amount).toBe(70);
  });

  it('Should handle different tag formats correctly', () => {
    const accountingData: AccountingData[] = [
      {
        id: 1,
        transaction_str_id: 'txn1',
        amount: 100,
        date: new Date('2023-01-01'),
        notes: 'Test transaction',
        status: TransactionStatuses.APPROVED,
        details: [
          {
            id: 101,
            transaction_detail_str_id: 'dtl101',
            amount: 50,
            date: new Date('2023-01-01'),
            status: TransactionStatuses.APPROVED,
            notes: 'String tags',
            tags: 'tag1, tag2, tag3', // String with spaces
          },
          {
            id: 102,
            transaction_detail_str_id: 'dtl102',
            amount: 50,
            date: new Date('2023-01-01'),
            status: TransactionStatuses.APPROVED,
            notes: 'Array tags',
            tags: ['tag4', 'tag5'], // Array tags
          },
          {
            id: 103,
            transaction_detail_str_id: 'dtl103',
            amount: 50,
            date: new Date('2023-01-01'),
            status: TransactionStatuses.APPROVED,
            notes: 'No tags',
            tags: undefined, // No tags
          },
        ],
      },
    ];

    const upsertResults = [{ id: 1000 }];
    const result = accountingHelpers.prepareTransactionDetailsUpsertData(
      accountingData,
      upsertResults,
      5000,
      'user123',
      'org456'
    );

    expect(result).toHaveLength(3);
    expect(result[0].create.tags).toEqual(['tag1', 'tag2', 'tag3']);
    expect(result[1].create.tags).toEqual(['tag4', 'tag5']);
    expect(result[2].create.tags).toEqual([]);
  });

  it('Should use string id when str_id is not available', () => {
    const accountingData: AccountingData[] = [
      {
        id: 1,
        amount: 100,
        date: new Date('2023-01-01'),
        notes: 'Test transaction',
        status: TransactionStatuses.APPROVED,
        details: [
          {
            id: 101, // No str_id
            amount: 50,
            date: new Date('2023-01-01'),
            status: TransactionStatuses.APPROVED,
            notes: 'Detail without str_id',
            tags: [],
          },
        ],
      },
    ];

    const upsertResults = [{ id: 1000 }];
    const result = accountingHelpers.prepareTransactionDetailsUpsertData(
      accountingData,
      upsertResults,
      5000,
      'user123',
      'org456'
    );

    expect(result).toHaveLength(1);
    expect(result[0].where.str_id).toBe('101');
  });
});
describe('accountingHelpers.getPagination', () => {
  it('Should calculate correct total_pages and last_page', () => {
    const result = accountingHelpers.getPagination({
      currentPage: 0,
      totalItemsPerPage: 10,
      totalItems: 30,
    });

    expect(result).toEqual({
      current_page: 0,
      previous_page: undefined,
      next_page: 1,
      last_page: 3,
      total_pages: 3,
      total_items_per_page: 10,
    });
  });

  it('Should round up total_pages when totalItems not divisible by totalItemsPerPage', () => {
    const result = accountingHelpers.getPagination({
      currentPage: 0,
      totalItemsPerPage: 7,
      totalItems: 15,
    });

    expect(result).toEqual({
      current_page: 0,
      previous_page: undefined,
      next_page: 1,
      last_page: 3,
      total_pages: 3,
      total_items_per_page: 7,
    });
  });

  it('Should set next_page as undefined when currentPage is last page', () => {
    const result = accountingHelpers.getPagination({
      currentPage: 2,
      totalItemsPerPage: 10,
      totalItems: 25,
    });

    expect(result).toEqual({
      current_page: 2,
      previous_page: 1,
      next_page: undefined,
      last_page: 3,
      total_pages: 3,
      total_items_per_page: 10,
    });
  });

  it('Should set previous_page as null when currentPage is first page', () => {
    const result = accountingHelpers.getPagination({
      currentPage: 0,
      totalItemsPerPage: 5,
      totalItems: 12,
    });

    expect(result.previous_page).toBeUndefined();
    expect(result.current_page).toBe(0);
  });

  it('Should set previous_page and next_page correctly when currentPage in the middle', () => {
    const result = accountingHelpers.getPagination({
      currentPage: 1,
      totalItemsPerPage: 5,
      totalItems: 12,
    });

    expect(result.previous_page).toBe(0);
    expect(result.next_page).toBe(2);
    expect(result.current_page).toBe(1);
  });

  it('Should set total_pages and last_page as 0 and undefined when totalItems is 0', () => {
    const result = accountingHelpers.getPagination({
      currentPage: 0,
      totalItemsPerPage: 10,
      totalItems: 0,
    });

    expect(result).toEqual({
      current_page: 0,
      previous_page: undefined,
      next_page: undefined,
      last_page: undefined,
      total_pages: 0,
      total_items_per_page: 0,
    });
  });

  it('Should set last_page and next_page as undefined when currentPage greater than totalPages', () => {
    const result = accountingHelpers.getPagination({
      currentPage: 5,
      totalItemsPerPage: 2,
      totalItems: 6,
    });

    expect(result.current_page).toBe(5);
    expect(result.next_page).toBeUndefined();
    expect(result.last_page).toBe(3);
    expect(result.total_pages).toBe(3);
  });
});
