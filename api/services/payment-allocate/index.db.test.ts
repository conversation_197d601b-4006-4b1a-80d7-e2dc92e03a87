import { describe, expect, it, vi, beforeEach, afterEach } from 'vitest';
import { Prisma, statement_data, virtual_type } from '@prisma/client';
import { Decimal } from '@prisma/client/runtime/library';

import { PaymentAllocateService } from './index';
import { testGuard } from '@/lib/testGuard';
import { prismaClient } from '@/lib/prisma';
import { DataStates } from '@/types';

type StatementWithDetails = Prisma.statement_dataGetPayload<{
  include: {
    details: true;
  };
}>;

const createStatement = (
  id: number,
  commission: number,
  remain: number | null = null,
  other: Partial<statement_data> = {}
): StatementWithDetails =>
  ({
    id,
    commission_amount: new Decimal(commission),
    remain_amount:
      remain !== null ? new Decimal(remain) : new Decimal(commission),
    details: [],
    ...other,
  }) as StatementWithDetails;

describe('PaymentAllocateService', () => {
  let service: PaymentAllocateService;

  beforeEach(async () => {
    service = new PaymentAllocateService();
    // Mock the injected services
    (service as any).groupDedupeService = {
      deleteOrphanVirtualRecords: vi.fn().mockResolvedValue(undefined),
    };
    (service as any).dataProcessingService = {
      create: vi.fn(),
      updateTaskStatus: vi.fn(),
    };
    vi.clearAllMocks();
  });
  testGuard(() => {
    describe('getPolicies', () => {
      const accountId = 'test-get-policies-account-id';

      const createReportData = async (
        data: Partial<Prisma.report_dataCreateInput>
      ) => {
        return prismaClient.report_data.create({
          data: {
            account_id: accountId,
            policy_id: `policy-${Date.now()}-${Math.random()}`,
            state: DataStates.ACTIVE,
            premium_amount: new Decimal(1000),
            ...data,
          },
        });
      };

      const createStatementData = async (
        data: Partial<Prisma.statement_dataCreateInput>
      ) => {
        return prismaClient.statement_data.create({
          data: {
            account_id: accountId,
            state: DataStates.ACTIVE,
            remain_amount: new Decimal(100),
            ...data,
          },
        });
      };

      beforeEach(async () => {
        await prismaClient.statement_data.deleteMany({
          where: { account_id: accountId },
        });
        await prismaClient.report_data.deleteMany({
          where: { account_id: accountId },
        });
      });

      it('should return empty array when no policies exist', async () => {
        const policies = await service.getPolicies(accountId);
        expect(policies).toEqual([]);
      });

      it('should return policies for given account_id', async () => {
        await createReportData({
          policy_id: 'policy-1',
          premium_amount: new Decimal(1000),
          effective_date: new Date('2023-01-01'),
          policy_term_months: 12,
        });
        await createReportData({
          policy_id: 'policy-2',
          premium_amount: new Decimal(2000),
          effective_date: new Date('2023-01-01'),
          policy_term_months: 12,
        });

        await createStatementData({
          policy_id: 'policy-1',
        });
        await createStatementData({
          policy_id: 'policy-2',
        });

        const policies = await service.getPolicies(accountId);
        expect(policies).toHaveLength(2);
        expect(policies.map((p) => p.policy_id)).toEqual(
          expect.arrayContaining(['policy-1', 'policy-2'])
        );
      });

      it('should order policies by policy_id and effective_date', async () => {
        await createReportData({
          policy_id: 'policy-older',
          effective_date: new Date('2023-01-01'),
          policy_term_months: 12,
        });

        await createReportData({
          policy_id: 'policy-newer',
          effective_date: new Date('2023-01-02'),
          policy_term_months: 12,
        });

        await createStatementData({
          policy_id: 'policy-older',
        });
        await createStatementData({
          policy_id: 'policy-newer',
        });

        const policies = await service.getPolicies(accountId);
        expect(policies).toHaveLength(2);
        expect(policies[0].policy_id).toBe('policy-newer');
        expect(policies[1].policy_id).toBe('policy-older');
      });

      it('should only return policies for specified account_id', async () => {
        const otherAccountId = 'other-account-id';

        await createReportData({
          policy_id: 'policy-for-test-account',
          effective_date: new Date('2023-01-01'),
          policy_term_months: 12,
        });

        await prismaClient.report_data.create({
          data: {
            account_id: otherAccountId,
            policy_id: 'policy-for-other-account',
            state: DataStates.ACTIVE,
            premium_amount: new Decimal(1000),
          },
        });

        await createStatementData({
          policy_id: 'policy-for-test-account',
        });

        await prismaClient.statement_data.create({
          data: {
            account_id: otherAccountId,
            policy_id: 'policy-for-other-account',
            state: DataStates.ACTIVE,
            remain_amount: new Decimal(100),
          },
        });

        const policies = await service.getPolicies(accountId);
        expect(policies).toHaveLength(1);
        expect(policies[0].policy_id).toBe('policy-for-test-account');
        expect(policies[0].account_id).toBe(accountId);

        await prismaClient.statement_data.deleteMany({
          where: { account_id: otherAccountId },
        });
        await prismaClient.report_data.deleteMany({
          where: { account_id: otherAccountId },
        });
      });

      it('should return empty array when policies exist but no statements exist', async () => {
        await createReportData({
          policy_id: 'policy-without-statements',
        });

        const policies = await service.getPolicies(accountId);
        expect(policies).toEqual([]);
      });

      it('should only return policies that have corresponding statements', async () => {
        await createReportData({
          policy_id: 'policy-with-statements',
          effective_date: new Date('2023-01-01'),
          policy_term_months: 12,
        });
        await createReportData({
          policy_id: 'policy-without-statements',
          effective_date: new Date('2023-01-01'),
          policy_term_months: 12,
        });

        await createStatementData({
          policy_id: 'policy-with-statements',
        });

        const policies = await service.getPolicies(accountId);
        expect(policies).toHaveLength(1);
        expect(policies[0].policy_id).toBe('policy-with-statements');
      });
    });
  });
  describe('calculateAllocations', () => {
    // Mock getStatementRemainingAmount to avoid database calls
    beforeEach(() => {
      vi.spyOn(service, 'getStatementRemainingAmount').mockImplementation(
        (statement) =>
          Promise.resolve(
            statement.remain_amount ?? statement.commission_amount
          )
      );
    });

    afterEach(() => {
      vi.restoreAllMocks();
    });

    it('should return isFulfilled true when targetAmount is 0', async () => {
      const statements = [createStatement(1, 100)];
      const targetAmount = new Decimal(0);
      const { allocations, isFulfilled } = await service.calculateAllocations(
        statements,
        targetAmount
      );
      expect(allocations).toHaveLength(0);
      expect(isFulfilled).toBe(true);
    });

    it('should allocate nothing if statements are empty', async () => {
      const statements: StatementWithDetails[] = [];
      const targetAmount = new Decimal(100);
      const { allocations, isFulfilled } = await service.calculateAllocations(
        statements,
        targetAmount
      );
      expect(allocations).toHaveLength(0);
      expect(isFulfilled).toBe(false);
    });

    it('should allocate partially when one statement is enough', async () => {
      const statements = [createStatement(1, 100)];
      const targetAmount = new Decimal(50);
      const { allocations, isFulfilled } = await service.calculateAllocations(
        statements,
        targetAmount
      );
      expect(allocations).toHaveLength(1);
      expect(allocations[0].statement.id).toBe(1);
      expect(allocations[0].amount.equals(new Decimal(50))).toBe(true);
      expect(isFulfilled).toBe(true);
    });

    it('should allocate fully one statement', async () => {
      const statements = [createStatement(1, 100)];
      const targetAmount = new Decimal(100);
      const { allocations, isFulfilled } = await service.calculateAllocations(
        statements,
        targetAmount
      );
      expect(allocations).toHaveLength(1);
      expect(allocations[0].statement.id).toBe(1);
      expect(allocations[0].amount.equals(new Decimal(100))).toBe(true);
      expect(isFulfilled).toBe(true);
    });

    it('should allocate multiple statements to meet target', async () => {
      const statements = [createStatement(1, 50), createStatement(2, 50)];
      const targetAmount = new Decimal(100);
      const { allocations, isFulfilled } = await service.calculateAllocations(
        statements,
        targetAmount
      );
      expect(allocations).toHaveLength(2);
      expect(allocations[0].amount.equals(new Decimal(50))).toBe(true);
      expect(allocations[1].amount.equals(new Decimal(50))).toBe(true);
      expect(isFulfilled).toBe(true);
    });

    it('should allocate partially from the last statement to meet target', async () => {
      const statements = [createStatement(1, 50), createStatement(2, 100)];
      const targetAmount = new Decimal(100);
      const { allocations, isFulfilled } = await service.calculateAllocations(
        statements,
        targetAmount
      );
      expect(allocations).toHaveLength(2);
      expect(allocations[0].amount.equals(new Decimal(50))).toBe(true);
      expect(allocations[1].amount.equals(new Decimal(50))).toBe(true);
      expect(isFulfilled).toBe(true);
    });

    it('should not be fulfilled if total is less than target', async () => {
      const statements = [createStatement(1, 50)];
      const targetAmount = new Decimal(100);
      const { allocations, isFulfilled } = await service.calculateAllocations(
        statements,
        targetAmount
      );
      expect(allocations).toHaveLength(1);
      expect(allocations[0].amount.equals(new Decimal(50))).toBe(true);
      expect(isFulfilled).toBe(false);
    });

    it('should use remain_amount when available', async () => {
      const statements = [createStatement(1, 100, 30)];
      const targetAmount = new Decimal(50);
      const { allocations, isFulfilled } = await service.calculateAllocations(
        statements,
        targetAmount
      );
      expect(allocations).toHaveLength(1);
      expect(allocations[0].amount.equals(new Decimal(30))).toBe(true);
      expect(isFulfilled).toBe(false);
    });

    it('should ignore statements with zero or negative remain_amount', async () => {
      const statements = [
        createStatement(1, 100, 0),
        createStatement(2, 100, -10),
        createStatement(3, 50),
      ];
      const targetAmount = new Decimal(100);
      const { allocations, isFulfilled } = await service.calculateAllocations(
        statements,
        targetAmount
      );
      expect(allocations).toHaveLength(1);
      expect(allocations[0].statement.id).toBe(3);
      expect(allocations[0].amount.equals(new Decimal(50))).toBe(true);
      expect(isFulfilled).toBe(false);
    });
  });

  describe('groupedTotalAmount', () => {
    it('should sum commission_amount from children_data', () => {
      const statement = {
        children_data: [
          { commission_amount: new Decimal(25) },
          { commission_amount: new Decimal(75) },
          { commission_amount: null },
          { commission_amount: new Decimal(10) },
        ],
      };
      const total = service.groupedTotalAmount(statement as any);
      expect(total.equals(new Decimal(110))).toBe(true);
    });

    it('should return 0 if no children_data', () => {
      const statement = {
        children_data: [],
      };
      const total = service.groupedTotalAmount(statement as any);
      expect(total.equals(new Decimal(0))).toBe(true);
    });
  });

  describe('cloneStatement', () => {
    it('should clone a statement and remove specific fields', () => {
      const originalStatement: statement_data = {
        id: 1,
        account_id: 'acc_123',
        commission_amount: new Decimal('100.50'),
        state: 'ACTIVE',
        agent_commissions_status: 'pending',
        created_at: new Date(),
        updated_at: new Date(),
        details: [],
        str_id: 'str_123',
        is_virtual: false,
        virtual_type: null,
      } as any;

      const cloned = service.cloneStatement(originalStatement);

      expect(cloned).not.toHaveProperty('id');
      expect(cloned).not.toHaveProperty('created_at');
      expect(cloned).not.toHaveProperty('updated_at');
      expect(cloned).not.toHaveProperty('agent_commissions_status');
      expect(cloned).not.toHaveProperty('details');
      expect(cloned).not.toHaveProperty('str_id');
      expect(cloned).not.toHaveProperty('is_virtual');
      expect(cloned).not.toHaveProperty('virtual_type');
      expect(cloned).toHaveProperty('account_id', 'acc_123');
      expect(cloned.commission_amount.equals(new Decimal('100.50'))).toBe(true);
    });
  });

  describe('getStatementRemainingAmount', () => {
    it('should return remain_amount if it is set', async () => {
      const statement = {
        id: 1,
        commission_amount: new Decimal(100),
        remain_amount: new Decimal(25),
        details: [
          {
            virtual_type: virtual_type.partial_payment,
            commission_amount: new Decimal(20),
          },
        ],
      };

      const mockPrisma = {
        statement_data: {
          findFirst: vi.fn().mockResolvedValue(statement),
        },
      };

      const remaining = await service.getStatementRemainingAmount(
        statement as any,
        mockPrisma as any
      );
      expect(remaining.equals(new Decimal(25))).toBe(true);
    });

    it('should calculate remaining amount from partial payment details when remain_amount is null', async () => {
      const statement = {
        id: 1,
        commission_amount: new Decimal(100),
        remain_amount: null,
        details: [
          {
            virtual_type: virtual_type.partial_payment,
            commission_amount: new Decimal(20),
          },
          {
            virtual_type: virtual_type.partial_payment,
            commission_amount: new Decimal(30),
          },
          {
            virtual_type: virtual_type.grouped,
            commission_amount: new Decimal(10),
          },
        ],
      };

      const mockPrisma = {
        statement_data: {
          findFirst: vi.fn().mockResolvedValue(statement),
        },
      };

      const remaining = await service.getStatementRemainingAmount(
        statement as any,
        mockPrisma as any
      );
      expect(remaining.equals(new Decimal(50))).toBe(true);
    });

    it('should return full amount if no partial payment details and remain_amount is null', async () => {
      const statement = {
        id: 1,
        commission_amount: new Decimal(100),
        remain_amount: null,
        details: [
          {
            virtual_type: virtual_type.grouped,
            commission_amount: new Decimal(10),
          },
        ],
      };

      const mockPrisma = {
        statement_data: {
          findFirst: vi.fn().mockResolvedValue(statement),
        },
      };

      const remaining = await service.getStatementRemainingAmount(
        statement as any,
        mockPrisma as any
      );
      expect(remaining.equals(new Decimal(100))).toBe(true);
    });

    it('should return full amount if no details array and remain_amount is null', async () => {
      const statement = {
        id: 1,
        commission_amount: new Decimal(100),
        remain_amount: null,
        details: [],
      };

      const mockPrisma = {
        statement_data: {
          findFirst: vi.fn().mockResolvedValue(statement),
        },
      };

      const remaining = await service.getStatementRemainingAmount(
        statement as any,
        mockPrisma as any
      );
      expect(remaining.equals(new Decimal(100))).toBe(true);
    });

    it('should return 0 if statement is not found', async () => {
      const statement = {
        id: 1,
        commission_amount: new Decimal(100),
      };

      const mockPrisma = {
        statement_data: {
          findFirst: vi.fn().mockResolvedValue(null),
        },
      };

      const remaining = await service.getStatementRemainingAmount(
        statement as any,
        mockPrisma as any
      );
      expect(remaining.equals(new Decimal(0))).toBe(true);
    });
  });

  testGuard(() => {
    describe('getAllocatableStatements', () => {
      const accountId = 'test-account-id';
      const policyId = 'test-policy-id';

      const createReportData = async (config = {}) => {
        return prismaClient.report_data.create({
          data: {
            account_id: accountId,
            policy_id: policyId,
            state: DataStates.ACTIVE,
            premium_amount: new Decimal(1200),
            config,
          },
        });
      };

      const createStatementData = async (
        data: Partial<Prisma.statement_dataCreateInput>
      ) => {
        return prismaClient.statement_data.create({
          data: {
            account_id: accountId,
            policy_id: policyId,
            state: DataStates.ACTIVE,
            remain_amount: new Decimal(100),
            ...data,
          },
        });
      };

      beforeEach(async () => {
        await prismaClient.statement_data.deleteMany({
          where: { account_id: accountId },
        });
        await prismaClient.report_data.deleteMany({
          where: { account_id: accountId },
        });
      });

      it('should return all allocatable active statements', async () => {
        const policy = await createReportData();
        await createStatementData({
          product_type: 'Dental',
          remain_amount: new Decimal(100),
        });
        await createStatementData({
          product_type: 'Vision',
          state: DataStates.ALLOCATED,
          remain_amount: new Decimal(50),
        });
        // Should be ignored
        await createStatementData({
          product_type: 'Medical',
          remain_amount: new Decimal(0),
        });
        await createStatementData({
          product_type: 'Other',
          state: DataStates.GROUPED,
        });

        const baseStatement = { account_id: accountId, policy_id: policyId };
        const statements = await service.getAllocatableStatements(
          baseStatement as any,
          policy
        );

        expect(statements).toHaveLength(3);
        expect(statements.map((s) => s.product_type)).toEqual(
          expect.arrayContaining(['Dental', 'Vision', 'Other'])
        );
      });

      it('should only return dental products if rule is dental_only', async () => {
        const policy = await createReportData({
          allied_payment_rule: { mode: 'dental_only' },
        });
        await createStatementData({ product_type: 'Dental' });
        await createStatementData({ product_type: 'Vision' });

        const baseStatement = { account_id: accountId, policy_id: policyId };
        const statements = await service.getAllocatableStatements(
          baseStatement as any,
          policy
        );

        expect(statements).toHaveLength(1);
        expect(statements[0].product_type).toBe('Dental');
      });

      it('should return dental and vision products if rule is dental_vision', async () => {
        const policy = await createReportData({
          allied_payment_rule: { mode: 'dental_vision' },
        });
        await createStatementData({ product_type: 'Dental' });
        await createStatementData({ product_type: 'Vision' });
        await createStatementData({ product_type: 'Medical' });

        const baseStatement = { account_id: accountId, policy_id: policyId };
        const statements = await service.getAllocatableStatements(
          baseStatement as any,
          policy
        );

        expect(statements).toHaveLength(2);
        expect(statements.map((s) => s.product_type)).toEqual(
          expect.arrayContaining(['Dental', 'Vision'])
        );
      });

      it('should sort by priority when rule has priority', async () => {
        const policy = await createReportData({
          allied_payment_rule: {
            mode: 'dental_vision',
            priority: ['Vision', 'Dental'],
          },
        });
        await createStatementData({
          product_type: 'Dental',
          remain_amount: new Decimal(100),
        });
        await createStatementData({
          product_type: 'Vision',
          remain_amount: new Decimal(50),
        });

        const baseStatement = { account_id: accountId, policy_id: policyId };
        const statements = await service.getAllocatableStatements(
          baseStatement as any,
          policy
        );

        expect(statements).toHaveLength(2);
        expect(statements.map((s) => s.product_type)).toEqual([
          'Vision',
          'Dental',
        ]);
      });

      it('should sort by period_date by default if no priority is given', async () => {
        const policy = await createReportData({
          allied_payment_rule: { mode: 'dental_vision' },
        });
        await createStatementData({
          product_type: 'Dental',
          remain_amount: new Decimal(100),
          period_date: new Date('2023-01-02'),
        });
        await createStatementData({
          product_type: 'Vision',
          remain_amount: new Decimal(50),
          period_date: new Date('2023-01-01'),
        });

        const baseStatement = { account_id: accountId, policy_id: policyId };
        const statements = await service.getAllocatableStatements(
          baseStatement as any,
          policy
        );
        expect(statements.map((s) => s.product_type)).toEqual([
          'Vision',
          'Dental',
        ]);
      });
    });
  });

  describe('generatePeriodDates', () => {
    it('should generate correct period dates for given terms', () => {
      const effectiveDate = new Date('2023-01-15');
      const terms = 3;

      const dates = service.generatePeriodDates(effectiveDate, terms);

      expect(dates).toEqual(['2023-01-01', '2023-02-01', '2023-03-01']);
    });

    it('should handle single term', () => {
      const effectiveDate = new Date('2023-06-15');
      const terms = 1;

      const dates = service.generatePeriodDates(effectiveDate, terms);

      expect(dates).toEqual(['2023-06-01']);
    });

    it('should handle zero terms', () => {
      const effectiveDate = new Date('2023-01-15');
      const terms = 0;

      const dates = service.generatePeriodDates(effectiveDate, terms);

      expect(dates).toEqual([]);
    });

    it('should handle year boundary correctly', () => {
      const effectiveDate = new Date('2023-11-15');
      const terms = 3;

      const dates = service.generatePeriodDates(effectiveDate, terms);

      expect(dates).toEqual(['2023-11-01', '2023-12-01', '2024-01-01']);
    });
  });

  describe('findMissingPeriodDates', () => {
    it('should return all periods when no statements exist (up to current date)', () => {
      const policy = {
        effective_date: new Date('2023-01-01'),
        policy_term_months: 3,
      };
      const statements = [];

      const missing = service.findMissingPeriodDates(policy, statements);

      // When no statements exist, it uses current date as fallback
      // Since we're in 2025, all periods from 2023 should be missing
      expect(missing).toEqual(['2023-01-01', '2023-02-01', '2023-03-01']);
    });

    it('should find missing periods between existing statements', () => {
      const policy = {
        effective_date: new Date('2023-01-01'),
        policy_term_months: 4,
      };
      const statements = [
        { period_date: new Date('2023-01-01') },
        { period_date: new Date('2023-03-01') },
      ];

      const missing = service.findMissingPeriodDates(policy, statements);

      expect(missing).toEqual(['2023-02-01']);
    });

    it('should only return periods up to latest statement date', () => {
      const policy = {
        effective_date: new Date('2023-01-01'),
        policy_term_months: 6,
      };
      const statements = [
        { period_date: new Date('2023-01-01') },
        { period_date: new Date('2023-03-01') },
      ];

      const missing = service.findMissingPeriodDates(policy, statements);

      expect(missing).toEqual(['2023-02-01']);
    });

    it('should return empty array when all periods are covered', () => {
      const policy = {
        effective_date: new Date('2023-01-01'),
        policy_term_months: 3,
      };
      const statements = [
        { period_date: new Date('2023-01-01') },
        { period_date: new Date('2023-02-01') },
        { period_date: new Date('2023-03-01') },
      ];

      const missing = service.findMissingPeriodDates(policy, statements);

      expect(missing).toEqual([]);
    });
  });

  testGuard(() => {
    describe('fullFillGroupedCommission', () => {
      const accountId = 'test-account-id';

      beforeEach(async () => {
        await prismaClient.statement_data.deleteMany({
          where: { account_id: accountId },
        });
      });

      it('should update statement with grouped commission amounts', async () => {
        // Create parent statement
        const parentStatement = await prismaClient.statement_data.create({
          data: {
            account_id: accountId,
            state: DataStates.ACTIVE,
            is_virtual: true,
            virtual_type: virtual_type.grouped,
            commission_amount: new Decimal(0),
            premium_amount: new Decimal(100),
          },
        });

        // Create grouped child statements
        await prismaClient.statement_data.create({
          data: {
            account_id: accountId,
            parent_id: parentStatement.id,
            state: DataStates.GROUPED,
            commission_amount: new Decimal(30),
          },
        });

        await prismaClient.statement_data.create({
          data: {
            account_id: accountId,
            parent_id: parentStatement.id,
            state: DataStates.GROUPED,
            commission_amount: new Decimal(20),
          },
        });

        const result = await service.fullFillGroupedCommission({
          statementId: parentStatement.id,
          targetAmount: new Decimal(100),
          accountId: accountId,
        });

        expect(result.premium_amount?.equals(new Decimal(100))).toBe(true);
        expect(result.commission_amount?.equals(new Decimal(50))).toBe(true);
        expect(result.allocated_amount?.equals(new Decimal(50))).toBe(true);
        expect(result.commission_rate).toBeNull();
      });

      it('should set commission rate to 100% when fulfilled', async () => {
        const parentStatement = await prismaClient.statement_data.create({
          data: {
            account_id: accountId,
            state: DataStates.ACTIVE,
            is_virtual: true,
            virtual_type: virtual_type.grouped,
            commission_amount: new Decimal(0),
            premium_amount: new Decimal(100),
          },
        });

        await prismaClient.statement_data.create({
          data: {
            account_id: accountId,
            parent_id: parentStatement.id,
            state: DataStates.GROUPED,
            commission_amount: new Decimal(100),
          },
        });

        const result = await service.fullFillGroupedCommission({
          statementId: parentStatement.id,
          targetAmount: new Decimal(100),
          accountId: accountId,
        });

        expect(result.commission_rate).toBe('100');
        expect(result.new_commission_rate?.equals(new Decimal(1))).toBe(true);
        expect(result.commission_rate_percent?.equals(new Decimal(100))).toBe(
          true
        );
      });
    });
  });

  testGuard(() => {
    describe('createMissedPeriodStatements', () => {
      const accountId = 'test-account-id';
      const policyId = 'test-policy-id';

      beforeEach(async () => {
        await prismaClient.statement_data.deleteMany({
          where: { account_id: accountId },
        });
        await prismaClient.report_data.deleteMany({
          where: { account_id: accountId },
        });
      });

      it('should create missed period statements', async () => {
        const policy = await prismaClient.report_data.create({
          data: {
            account_id: accountId,
            policy_id: policyId,
            state: DataStates.ACTIVE,
            premium_amount: new Decimal(1200),
          },
        });

        const missedPeriods = [
          new Date('2023-01-01'),
          new Date('2023-02-01'),
          new Date('2023-03-01'),
        ];

        const statements = await service.createMissedPeriodStatements(
          policy,
          missedPeriods
        );

        expect(statements).toHaveLength(3);

        statements.forEach((statement, index) => {
          expect(statement.period_date).toEqual(missedPeriods[index]);
          expect(statement.state).toBe(DataStates.ACTIVE);
          expect(statement.is_virtual).toBe(true);
          expect(statement.virtual_type).toBe(virtual_type.grouped);
          expect(statement.premium_amount?.equals(new Decimal(100))).toBe(true);
          expect(statement.commission_amount?.equals(new Decimal(0))).toBe(
            true
          );
          expect(statement.allocated_amount?.equals(new Decimal(0))).toBe(true);
          expect(statement.remain_amount?.equals(new Decimal(0))).toBe(true);
          expect(statement.policy_id).toBe(policyId);
          expect(statement.account_id).toBe(accountId);
        });
      });

      it('should handle empty missed periods array', async () => {
        const policy = await prismaClient.report_data.create({
          data: {
            account_id: accountId,
            policy_id: policyId,
            state: DataStates.ACTIVE,
            premium_amount: new Decimal(1200),
          },
        });

        const statements = await service.createMissedPeriodStatements(
          policy,
          []
        );

        expect(statements).toHaveLength(0);
      });
    });
  });

  testGuard(() => {
    describe('getGroupedStatements', () => {
      const accountId = 'test-account-id';
      const policyId = 'test-policy-id';

      beforeEach(async () => {
        await prismaClient.statement_data.deleteMany({
          where: { account_id: accountId },
        });
        await prismaClient.report_data.deleteMany({
          where: { account_id: accountId },
        });
      });

      it('should return grouped statements within policy term', async () => {
        const policy = await prismaClient.report_data.create({
          data: {
            account_id: accountId,
            policy_id: policyId,
            state: DataStates.ACTIVE,
            premium_amount: new Decimal(1200),
            effective_date: new Date('2023-01-01'),
            policy_term_months: 3,
          },
        });

        // Create statements within term
        await prismaClient.statement_data.create({
          data: {
            account_id: accountId,
            policy_id: policyId,
            is_virtual: true,
            virtual_type: virtual_type.grouped,
            state: DataStates.ACTIVE,
            period_date: new Date('2023-01-01'),
          },
        });

        await prismaClient.statement_data.create({
          data: {
            account_id: accountId,
            policy_id: policyId,
            is_virtual: true,
            virtual_type: virtual_type.grouped,
            state: DataStates.ACTIVE,
            period_date: new Date('2023-02-01'),
          },
        });

        // Create statement outside term (should be excluded)
        await prismaClient.statement_data.create({
          data: {
            account_id: accountId,
            policy_id: policyId,
            is_virtual: true,
            virtual_type: virtual_type.grouped,
            state: DataStates.ACTIVE,
            period_date: new Date('2023-05-01'),
          },
        });

        const statements = await service.getGroupedStatements(policy);

        expect(statements).toHaveLength(2);
        expect(statements.map((s) => s.period_date)).toEqual([
          new Date('2023-01-01'),
          new Date('2023-02-01'),
        ]);
      });

      it('should return empty array when policy has no effective_date', async () => {
        const policy = await prismaClient.report_data.create({
          data: {
            account_id: accountId,
            policy_id: policyId,
            state: DataStates.ACTIVE,
            premium_amount: new Decimal(1200),
            effective_date: null,
            policy_term_months: 3,
          },
        });

        const statements = await service.getGroupedStatements(policy);

        expect(statements).toHaveLength(0);
      });

      it('should return empty array when policy has no policy_term_months', async () => {
        const policy = await prismaClient.report_data.create({
          data: {
            account_id: accountId,
            policy_id: policyId,
            state: DataStates.ACTIVE,
            premium_amount: new Decimal(1200),
            effective_date: new Date('2023-01-01'),
            policy_term_months: null,
          },
        });

        const statements = await service.getGroupedStatements(policy);

        expect(statements).toHaveLength(0);
      });
    });
  });

  testGuard(() => {
    describe('getPendingStatements', () => {
      const accountId = 'test-account-id';
      const policyId = 'test-policy-id';

      beforeEach(async () => {
        await prismaClient.statement_data.deleteMany({
          where: { account_id: accountId },
        });
      });

      it('should return pending grouped statements', async () => {
        // Create pending statements
        const statement1 = await prismaClient.statement_data.create({
          data: {
            account_id: accountId,
            policy_id: policyId,
            is_virtual: true,
            virtual_type: virtual_type.grouped,
            state: DataStates.ACTIVE,
            period_date: new Date('2023-01-01'),
            premium_amount: new Decimal(100),
            commission_amount: new Decimal(50),
          },
        });

        const statement2 = await prismaClient.statement_data.create({
          data: {
            account_id: accountId,
            policy_id: policyId,
            is_virtual: true,
            virtual_type: virtual_type.grouped,
            state: DataStates.ACTIVE,
            period_date: new Date('2023-02-01'),
            premium_amount: new Decimal(100),
            commission_amount: null,
          },
        });

        const policy = {
          policy_id: policyId,
          effective_date: new Date('2023-01-01'),
          terms: 12,
        };

        const statements = await service.getPendingStatements(
          accountId,
          policy
        );

        expect(statements).toHaveLength(2);
        expect(statements.map((s) => s.id)).toEqual(
          expect.arrayContaining([statement1.id, statement2.id])
        );
      });

      it('should return empty array when policy has no effective_date', async () => {
        const policy = {
          policy_id: policyId,
          effective_date: undefined,
          terms: 12,
        };

        const statements = await service.getPendingStatements(
          accountId,
          policy
        );

        expect(statements).toHaveLength(0);
      });

      it('should return empty array when policy has no terms', async () => {
        const policy = {
          policy_id: policyId,
          effective_date: new Date('2023-01-01'),
          terms: undefined,
        };

        const statements = await service.getPendingStatements(
          accountId,
          policy
        );

        expect(statements).toHaveLength(0);
      });
    });
  });

  describe('processRemainStatements', () => {
    it('should update remain statements to ALLOCATED state', async () => {
      const mockStatements = [createStatement(1, 100), createStatement(2, 50)];

      const mockPrisma = {
        statement_data: {
          updateMany: vi.fn().mockResolvedValue({ count: 2 }),
        },
      };

      await service.processRemainStatements(mockStatements, mockPrisma as any);

      expect(mockPrisma.statement_data.updateMany).toHaveBeenCalledTimes(2);
      expect(mockPrisma.statement_data.updateMany).toHaveBeenNthCalledWith(1, {
        where: { id: 1 },
        data: {
          state: DataStates.ALLOCATED,
          parent_id: null,
          allocated_amount: new Decimal(0),
          remain_amount: new Decimal(100),
        },
      });
      expect(mockPrisma.statement_data.updateMany).toHaveBeenNthCalledWith(2, {
        where: { id: 2 },
        data: {
          state: DataStates.ALLOCATED,
          parent_id: null,
          allocated_amount: new Decimal(0),
          remain_amount: new Decimal(50),
        },
      });
    });

    it('should handle empty statements array', async () => {
      const mockPrisma = {
        statement_data: {
          updateMany: vi.fn(),
        },
      };

      await service.processRemainStatements([], mockPrisma as any);

      expect(mockPrisma.statement_data.updateMany).not.toHaveBeenCalled();
    });
  });

  describe('fullyConsumeStatement', () => {
    it('should update statement to GROUPED state with parent_id', async () => {
      const statement = createStatement(1, 100);
      const parentStatement = createStatement(2, 200);

      const mockPrisma = {
        statement_data: {
          update: vi.fn().mockResolvedValue(statement),
        },
      };

      await service.fullyConsumeStatement(
        statement,
        parentStatement,
        mockPrisma as any
      );

      expect(mockPrisma.statement_data.update).toHaveBeenCalledWith({
        where: { id: 1 },
        data: {
          state: DataStates.GROUPED,
          parent_id: 2,
          allocated_amount: new Decimal(100),
          remain_amount: 0,
        },
      });
    });
  });

  testGuard(() => {
    describe('generatePartialPayments', () => {
      const accountId = 'test-account-id';

      beforeEach(async () => {
        await prismaClient.statement_data.deleteMany({
          where: { account_id: accountId },
        });
      });

      it('should create partial payment when sufficient remaining amount', async () => {
        const statement = await prismaClient.statement_data.create({
          data: {
            account_id: accountId,
            commission_amount: new Decimal(100),
            remain_amount: new Decimal(100),
            state: DataStates.ACTIVE,
          },
          include: { details: true },
        });

        const parentStatement = await prismaClient.statement_data.create({
          data: {
            account_id: accountId,
            commission_amount: new Decimal(200),
            state: DataStates.ACTIVE,
            period_date: new Date('2023-01-01'),
          },
          include: { details: true },
        });

        const result = await service.generatePartialPayments(
          statement,
          parentStatement,
          new Decimal(50)
        );

        expect(result).not.toBeNull();
        expect(result?.commission_amount?.equals(new Decimal(50))).toBe(true);
        expect(result?.state).toBe(DataStates.GROUPED);
        expect(result?.virtual_type).toBe(virtual_type.partial_payment);
        expect(result?.is_virtual).toBe(true);
        expect(result?.parent_id).toBe(parentStatement.id);
        expect(result?.master_id).toBe(statement.id);

        // Check that original statement was updated
        const updatedStatement = await prismaClient.statement_data.findUnique({
          where: { id: statement.id },
        });
        expect(updatedStatement?.state).toBe(DataStates.ALLOCATED);
        expect(
          updatedStatement?.allocated_amount?.equals(new Decimal(50))
        ).toBe(true);
        expect(updatedStatement?.remain_amount?.equals(new Decimal(50))).toBe(
          true
        );
      });

      it('should return null when insufficient remaining amount', async () => {
        const statement = await prismaClient.statement_data.create({
          data: {
            account_id: accountId,
            commission_amount: new Decimal(100),
            remain_amount: new Decimal(30),
            state: DataStates.ACTIVE,
          },
          include: { details: true },
        });

        const parentStatement = await prismaClient.statement_data.create({
          data: {
            account_id: accountId,
            commission_amount: new Decimal(200),
            state: DataStates.ACTIVE,
            period_date: new Date('2023-01-01'),
          },
          include: { details: true },
        });

        const result = await service.generatePartialPayments(
          statement,
          parentStatement,
          new Decimal(50)
        );

        expect(result).toBeNull();
      });
    });
  });

  describe('processAllocations', () => {
    it('should process allocations correctly', async () => {
      const allocations = [
        { statement: createStatement(1, 100), amount: new Decimal(50) },
        { statement: createStatement(2, 80), amount: new Decimal(80) },
      ];
      const remainStatements = [createStatement(3, 30)];
      const candidateIds = new Set([1]);
      const parentStatement = createStatement(4, 200);

      const mockPrisma = {
        statement_data: {
          update: vi.fn(),
          updateMany: vi.fn(),
          create: vi.fn(),
        },
      };

      // Mock the methods that will be called
      const generatePartialPaymentsSpy = vi
        .spyOn(service, 'generatePartialPayments')
        .mockResolvedValue(null);
      const fullyConsumeStatementSpy = vi
        .spyOn(service, 'fullyConsumeStatement')
        .mockResolvedValue();
      const processRemainStatementsSpy = vi
        .spyOn(service, 'processRemainStatements')
        .mockResolvedValue();

      await service.processAllocations(
        allocations,
        remainStatements,
        candidateIds,
        parentStatement,
        mockPrisma as any
      );

      expect(generatePartialPaymentsSpy).toHaveBeenCalledWith(
        allocations[0].statement,
        parentStatement,
        new Decimal(50),
        mockPrisma
      );
      expect(fullyConsumeStatementSpy).toHaveBeenCalledWith(
        allocations[1].statement,
        parentStatement,
        mockPrisma
      );
      expect(processRemainStatementsSpy).toHaveBeenCalledWith(
        remainStatements,
        mockPrisma
      );

      generatePartialPaymentsSpy.mockRestore();
      fullyConsumeStatementSpy.mockRestore();
      processRemainStatementsSpy.mockRestore();
    });
  });

  testGuard(() => {
    describe('reset', () => {
      const accountId = 'test-reset-account-id';

      beforeEach(async () => {
        await prismaClient.history.deleteMany({
          where: { account_id: accountId },
        });
        await prismaClient.statement_data.deleteMany({
          where: { account_id: accountId },
        });
        await prismaClient.history.deleteMany({
          where: { account_id: accountId },
        });
      });

      it('should reset allocated statements and delete partial payments', async () => {
        // Create master statement
        const masterStatement = await prismaClient.statement_data.create({
          data: {
            account_id: accountId,
            state: DataStates.ALLOCATED,
            commission_amount: new Decimal(100),
            allocated_amount: new Decimal(50),
            remain_amount: new Decimal(50),
          },
        });

        // Create partial payment detail
        const partialPayment = await prismaClient.statement_data.create({
          data: {
            account_id: accountId,
            master_id: masterStatement.id,
            state: DataStates.ACTIVE,
            is_virtual: true,
            virtual_type: virtual_type.partial_payment,
            commission_amount: new Decimal(50),
          },
        });

        // Create history record
        await prismaClient.history.create({
          data: {
            account_id: accountId,
            statement_data_id: masterStatement.id,
            json_data: '{"test": "data"}',
            table_type: 'statement',
          },
        });

        // Create grouped statement
        const groupedStatement = await prismaClient.statement_data.create({
          data: {
            account_id: accountId,
            state: DataStates.GROUPED,
            commission_amount: new Decimal(75),
            allocated_amount: new Decimal(75),
            remain_amount: new Decimal(0),
          },
        });

        // Create virtual grouped statement
        const virtualGrouped = await prismaClient.statement_data.create({
          data: {
            account_id: accountId,
            state: DataStates.ACTIVE,
            is_virtual: true,
            virtual_type: virtual_type.grouped,
            commission_amount: new Decimal(100),
            allocated_amount: new Decimal(100),
            premium_amount: new Decimal(100),
            commission_rate: '80',
          },
        });

        await service.reset(accountId);

        // Check that master statement was reset
        const resetMaster = await prismaClient.statement_data.findUnique({
          where: { id: masterStatement.id },
        });
        expect(resetMaster?.state).toBe(DataStates.ACTIVE);
        expect(resetMaster?.allocated_amount).toBeNull();
        expect(resetMaster?.remain_amount).toBeNull();

        // Check that partial payment was deleted
        const deletedPartial = await prismaClient.statement_data.findUnique({
          where: { id: partialPayment.id },
        });
        expect(deletedPartial).toBeNull();

        // Check that history was deleted
        const historyRecords = await prismaClient.history.findMany({
          where: {
            account_id: accountId,
            statement_data_id: masterStatement.id,
          },
        });
        expect(historyRecords).toHaveLength(0);

        // Check that grouped statement was reset
        const resetGrouped = await prismaClient.statement_data.findUnique({
          where: { id: groupedStatement.id },
        });
        expect(resetGrouped?.allocated_amount).toBeNull();
        expect(resetGrouped?.remain_amount).toBeNull();

        // Check that virtual grouped statement was reset
        const resetVirtualGrouped =
          await prismaClient.statement_data.findUnique({
            where: { id: virtualGrouped.id },
          });
        expect(resetVirtualGrouped?.allocated_amount).toBeNull();
        expect(resetVirtualGrouped?.remain_amount).toBeNull();
        expect(resetVirtualGrouped?.premium_amount).toBeNull();
        expect(resetVirtualGrouped?.commission_rate).toBeNull();
      });

      it('should handle account with no statements', async () => {
        // Should not throw error
        await expect(
          service.reset('non-existent-account')
        ).resolves.not.toThrow();
      });
    });
  });
});
