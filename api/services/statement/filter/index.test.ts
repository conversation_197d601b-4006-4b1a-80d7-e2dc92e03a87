import { describe, expect, it } from 'vitest';
import { faker } from '@faker-js/faker';

import {
  ApplyStatementFilterInput,
  StatementFilterService,
} from '@/services/statement/filter';

describe('StatementFilterService', () => {
  const service = new StatementFilterService();

  describe('getGlobalWhere', () => {
    it('should build the base globalWhere structure with no tags or flags', () => {
      const globalWhere = service.getGlobalWhere(
        faker.string.uuid(),
        [faker.string.uuid()],
        faker.string.uuid(),
        'test_query',
        faker.string.uuid(),
        false,
        false,
        { someField: 'someValue' },
        null,
        null
      );

      expect(globalWhere).toEqual({
        AND: expect.arrayContaining([]),
      });
    });

    it('should add tags condition when tags are provided', () => {
      const tags = ['tag1', 'tag2'];
      const globalWhere = service.getGlobalWhere(
        faker.string.uuid(),
        [faker.string.uuid()],
        faker.string.uuid(),
        'test_query',
        faker.string.uuid(),
        false,
        false,
        { someField: 'someValue' },
        tags,
        null
      );

      expect(globalWhere.AND).toEqual(
        expect.arrayContaining([{ tags: { hasSome: tags } }])
      );
    });

    it('should handle single flag correctly', () => {
      const flags = 'key:value';
      const globalWhere = service.getGlobalWhere(
        faker.string.uuid(),
        [faker.string.uuid()],
        faker.string.uuid(),
        'test_query',
        faker.string.uuid(),
        false,
        false,
        { someField: 'someValue' },
        null,
        flags
      );

      expect(globalWhere.AND).toEqual(
        expect.arrayContaining([
          {
            OR: [
              {
                flags: {
                  path: ['key'],
                  equals: 'value',
                },
              },
            ],
          },
        ])
      );
    });

    it('should handle multiple flags correctly', () => {
      const flags = ['key1:value1', 'key2:value2'];
      const globalWhere = service.getGlobalWhere(
        faker.string.uuid(),
        [faker.string.uuid()],
        faker.string.uuid(),
        'test_query',
        faker.string.uuid(),
        false,
        false,
        { someField: 'someValue' },
        null,
        flags
      );

      expect(globalWhere.AND).toEqual(
        expect.arrayContaining([
          {
            OR: [
              {
                flags: {
                  path: ['key1'],
                  equals: 'value1',
                },
              },
              {
                flags: {
                  path: ['key2'],
                  equals: 'value2',
                },
              },
            ],
          },
        ])
      );
    });

    it('should handle "blank" flag correctly', () => {
      const flags = ['blank'];
      const globalWhere = service.getGlobalWhere(
        faker.string.uuid(),
        [faker.string.uuid()],
        faker.string.uuid(),
        'test_query',
        faker.string.uuid(),
        false,
        false,
        { someField: 'someValue' },
        null,
        flags
      );

      expect(globalWhere.AND).toEqual(
        expect.arrayContaining([
          {
            OR: [
              {
                flags: {
                  not: null,
                },
              },
            ],
          },
        ])
      );
    });
  });

  describe('applyStatementFilter', () => {
    const defaultInput: ApplyStatementFilterInput = {
      query: {},
      where: { AND: [] },
      account: {
        account_id: faker.string.uuid(),
        uid: faker.string.uuid(),
        ouid: faker.string.uuid(),
        role_id: faker.string.uuid(),
      },
      contactAndChildrenStrIds: [],
      queryParams: [],
      userData: { user_contact: [] },
      isProducer: false,
    };

    it('should return the result correctly for the default input', async () => {
      const result = await service.applyStatementFilter({
        ...defaultInput,
        query: {},
      });

      expect(result).toEqual({
        additionalFilterFields: [],
        filterList: [
          'carrier_name',
          'status',
          'product_type',
          'product_name',
          'agent_name',
          'compensation_type',
          'payment_status',
          'writing_carrier_name',
          'document_id',
          'account_type',
          'transaction_type',
          'group_name',
          'contacts',
          'agent_commissions_status',
          'agent_commissions_status2',
          'reconciliation_status',
          'tags',
          'flags',
        ],
        where: {
          AND: [
            {
              OR: [
                {
                  AND: [
                    {
                      payment_date: undefined,
                    },
                    {
                      payment_date: undefined,
                    },
                    {
                      processing_date: undefined,
                    },
                    {
                      processing_date: undefined,
                    },
                    {
                      invoice_date: undefined,
                    },
                    {
                      invoice_date: undefined,
                    },
                    {
                      effective_date: undefined,
                    },
                    {
                      effective_date: undefined,
                    },
                  ],
                },
                {
                  OR: [
                    {
                      payment_date: undefined,
                    },
                    {
                      processing_date: undefined,
                    },
                    {
                      invoice_date: undefined,
                    },
                    {
                      effective_date: undefined,
                    },
                  ],
                },
              ],
            },
          ],
        },
      });
    });

    describe('query filters', () => {
      it('should return where field correctly when query contains hide_payout_calc_commissions', async () => {
        const result = await service.applyStatementFilter({
          ...defaultInput,
          query: {
            hide_payout_calc_commissions: true,
          },
        });

        expect(result).not.toBeUndefined();
        expect(result.where).toEqual({
          AND: expect.arrayContaining([
            {
              OR: [
                {
                  agent_commissions: {
                    equals: {},
                  },
                },
              ],
            },
          ]),
        });
      });
    });
  });
});
