import { type Page, Locator } from '@playwright/test';
import LocatorType from '@customTypes/locator-type';
import getDateLocator from '@utils/get-date-locator';

const policyLocatorsGenerator = (page: Page) => {
  const agentName: Locator = page.locator('#agent_name');
  const customerName: Locator = page.locator('#customer_name');
  const dba: Locator = page.locator('#dba');
  const policyNumber: Locator = page.locator('#policy_id');
  const internalId: Locator = page.locator('#internal_id');
  const effectiveDate: Locator = page
    .getByPlaceholder('MM/DD/YYYY')
    .locator('..')
    .locator('..')
    .getByText('Effective date')
    .nth(0)
    .locator('..')
    .locator('..');
  const annualizedRevenue: Locator = page.locator('#premium_amount');
  const masterCompany: Locator = page
    .locator('#writing_carrier_name')
    .locator('..');
  const productType: Locator = page.locator('#product_type');
  const productSubType: Locator = page.locator('#product_sub_type');
  const productName: Locator = page.locator('#product_name').locator('..');
  const productOptionName: Locator = page.locator('#product_option_name');
  const cancellationDate: Locator = getDateLocator(page, 'Cancellation date');
  const reinstatementDate: Locator = getDateLocator(page, 'Reinstatement date');

  const transactionType: Locator = page.locator('#transaction_type');
  const commissionsDue: Locator = page.locator('#commissions_expected');
  const status: Locator = page.locator('#policy_status');
  const accountType: Locator = page.locator('#account_type');
  const state: Locator = page.locator('#geo_state').locator('..');
  const splitPercentage: Locator = page.locator('#split_percentage');
  const groupName: Locator = page.locator('#group_name');
  const paymentMode: Locator = page.locator('#payment_mode');
  const targetPremium: Locator = page.locator('#commissionable_premium_amount');
  const policyTerm: Locator = page.locator('#policy_term_months');
  const notes: Locator = page.locator('#notes');
  const groupId: Locator = page.locator('#group_id');
  const agents: Locator = page.locator('#contacts').locator('..');
  const issueAge: Locator = page.locator('#issue_age');
  const customerPaidPremiumAmount: Locator = page.locator(
    '#customer_paid_premium_amount'
  );
  const aggregationId: Locator = page.locator('#aggregation_id');
  const headOfHousehold: Locator = page
    .getByText('Head of household')
    .locator('..')
    .locator('input[type="checkbox"]');
  const document: Locator = page.locator('#document_id').locator('..');
  const processingStatus: Locator = page
    .locator('#processing_status')
    .locator('..');

  const policyLocators = {
    agentName,
    customerName,
    dba,
    policyNumber,
    internalId,
    effectiveDate,
    masterCompany,
    annualizedRevenue,
    productType,
    productSubType,
    productName,
    productOptionName,
    cancellationDate,
    reinstatementDate,
    transactionType,
    commissionsDue,
    status,
    accountType,
    state,
    splitPercentage,
    groupName,
    paymentMode,
    policyTerm,
    targetPremium,
    notes,
    groupId,
    agents,
    issueAge,
    customerPaidPremiumAmount,
    aggregationId,
    headOfHousehold,
    document,
    processingStatus,
  };

  const policyUploadLocators: LocatorType[] = [
    { locator: agentName, name: 'agentName', type: 'text' },
    { locator: customerName, name: 'customerName', type: 'text' },
    { locator: dba, name: 'dba', type: 'text' },
    { locator: policyNumber, name: 'policyNumber', type: 'text' },
    { locator: internalId, name: 'internalId', type: 'text' },
    { locator: effectiveDate, name: 'effectiveDate', type: 'date' },
    { locator: annualizedRevenue, name: 'annualizedRevenue', type: 'text' },
    { locator: productType, name: 'productType', type: 'text' },
    { locator: productSubType, name: 'productSubType', type: 'text' },
    { locator: productName, name: 'productName', type: 'search-choose' },
    { locator: productOptionName, name: 'productOptionName', type: 'text' },
    { locator: cancellationDate, name: 'cancellationDate', type: 'date' },
    { locator: reinstatementDate, name: 'reinstatementDate', type: 'date' },
    { locator: transactionType, name: 'transactionType', type: 'text' },
    { locator: commissionsDue, name: 'commissionsDue', type: 'text' },
    { locator: status, name: 'status', type: 'text' },
    { locator: accountType, name: 'accountType', type: 'text' },
    // TODO: Add test id for EnhancedSelect
    // { locator: state, name: 'state', type: 'single-choose' },
    { locator: splitPercentage, name: 'splitPercentage', type: 'text' },
    { locator: groupName, name: 'groupName', type: 'text' },
    { locator: paymentMode, name: 'paymentMode', type: 'text' },
    { locator: policyTerm, name: 'policyTerm', type: 'text' },
    { locator: targetPremium, name: 'targetPremium', type: 'text' },
    { locator: notes, name: 'notes', type: 'text' },
    { locator: groupId, name: 'groupId', type: 'text' },
    // { locator: agents, name: 'agents', type: 'search-muti-choose' },
    { locator: issueAge, name: 'issueAge', type: 'text' },
    {
      locator: customerPaidPremiumAmount,
      name: 'customerPaidPremiumAmount',
      type: 'text',
    },
    { locator: aggregationId, name: 'aggregationId', type: 'text' },
    { locator: headOfHousehold, name: 'HeadOfHousehold', type: 'check' },
    // { locator: document, name: 'document', type: 'search-choose' },
    {
      locator: processingStatus,
      name: 'processingStatus',
      type: 'single-choose',
    },
  ];

  const policyBulkEditingLocators: LocatorType[] = [
    { locator: policyNumber, name: 'policyNumber', type: 'text' },
    { locator: agentName, name: 'agentName', type: 'text' },
    { locator: customerName, name: 'customerName', type: 'text' },
    { locator: productType, name: 'productType', type: 'text' },
    { locator: productName, name: 'productName', type: 'search-choose' },
    { locator: effectiveDate, name: 'effectiveDate', type: 'date' },
    // { locator: agents, name: 'agents', type: 'search-muti-choose' },
    { locator: notes, name: 'notes', type: 'text' },
    { locator: transactionType, name: 'transactionType', type: 'text' },
    { locator: status, name: 'status', type: 'text' },
    { locator: productOptionName, name: 'productOptionName', type: 'text' },
    { locator: internalId, name: 'internalId', type: 'text' },
    { locator: splitPercentage, name: 'splitPercentage', type: 'text' },
  ];

  return { policyLocators, policyUploadLocators, policyBulkEditingLocators };
};

export default policyLocatorsGenerator;
