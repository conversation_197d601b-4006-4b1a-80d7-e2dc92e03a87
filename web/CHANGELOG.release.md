# web

## Releases on 2025-07-14

### Version 10.19.14
<details>

### Patch Changes
 - Added pagination functionality to the agents transactions
- Updated dependencies [48371a9]
- Updated dependencies [cd9fde0]
  - common@0.24.7
</details>

### Version 10.19.13
<details>

### Patch Changes
 - Improved transaction management with pagination support and refactored backend logic
 - Added support for AI extraction(Gemini, ChatGPT, Claude, etc.) and enabled the ability to choose between multiple extraction methods in document processing.
 - - Introduced a new field 'processing_date' in the reconciliation configuration.
  - Enhanced filtering logic to handle fields specific to reconciliation version 2.
  - Added constants for reconciliation versions and fields to improve maintainability.
 - Added tooltips on document status fields to provide more detailed information about document processing results and errors.
- Updated dependencies [d6ee6a7]
- Updated dependencies [5547d60]
- Updated dependencies [fefa9a4]
  - common@0.24.6
</details>

### Version 10.19.12
<details>

### Patch Changes
 - Add custom report feature under the report to enable user upload custom report
 - Skip requirement for comp grid criteria if calc method doesn't require comp grid rates.
 - Customers page:
  - Move config from web to common, and add textFormatter for use in export.
  - Updated the `ExportCustomersHandler` to export based on the new config.
  - Added unit tests for textFormatter.
 - - Updated the useEffect hook in AdminDataSync to include initializeGoogleAuth as a dependency, ensuring proper execution.
  - Refactored initializeGoogleAuth to use useCallback for better performance and to prevent unnecessary re-creations of the function.
- Updated dependencies [f53d05c]
- Updated dependencies [1c9d5ce]
  - common@0.24.5
</details>

## Releases on 2025-07-11

### Version 10.19.11
<details>

### Patch Changes
 - Data actions tool: Add support to 'is not empty' operator for string fields (e.g. Group name field).
 - Added pagination functionality to the agents transactions
 - Added locks to Views and Fields which are not editable.
 - Allied payment allocation improvements including UI & allocation adjustment
</details>

## Releases on 2025-07-09

### Version 10.19.9
<details>

### Patch Changes
 - Fix the issue where the commission amount couldn’t be removed due to dependency checks, and optimize the loading UI for document type selection.
 - Fix companies using potential_match column that doesn't exist in our schema caused by: 1. Ignoring the potential_match field 2. Reverting deprecated code
 - Export alignment on Policy page
  - Move config from web to common, and add textFormatter for use in export
  - Move DataTransformation from web to common to be used by textFormatter
  - Refactor the report_data export logic with the following steps: getData → get fields based on config and accountSetting → fetch dynamicSelect data → formatData → export CSV
 - Display the carrier grid level name and the house payout grid level name for single carrier mode comp profiles
 - Include relational fields for params in data update criteria and actions. Avoid using asynchronous logic for executing custom rules.
 - Fix some issue on processor part: 1.missing data in company and some fields. 2. Crash when open crate a new processors.
 - - Introduced `policyDataIfEmptyFields` constant to manage fields sourced from policy data.
  - Updated `queryFieldValues` to utilize `findMany` for fetching statement data, enhancing flexibility in selecting fields.
  - Modified `StatementFilterService` to incorporate policy data fields in filter logic.
  - Refactored table formatting in `Statements.tsx` to improve tooltip handling for policy data discrepancies.
- Updated dependencies [db2a894]
- Updated dependencies [624c907]
- Updated dependencies [df70eef]
- Updated dependencies [d519450]
- Updated dependencies [07887a5]
- Updated dependencies [c09430e]
  - common@0.24.4
</details>

### Version 10.19.10
<details>

### Patch Changes
 - - Update TWC agent payout rate calculation to average result.
  - Fix invalid hook call in JsonStringToggle formatter
  - Add loading skeletons to DynamicSelect
  - Set max width in value on FieldConfig component to reduce overflow (still can happen)
  - Don't retry permission denied requests
 - Fix creating / saving commissions failing due to transaction_type formatter returning object.
</details>

## Releases on 2025-07-07

### Version 10.19.8
<details>

### Patch Changes
 - Fix handling of commission basis for is_virutal commission records by not adding child commission_amounts.
</details>

### Version 10.19.7
<details>

### Patch Changes
 - Show multiplier for a multiplier for each comp profile in match criteria when the calculation method is either "Pay commission to grid level" or "Share override to grid level" under enable com grid for setting
 - - Move config from web to common, and add textFormatter for use in export.
  - Implemented `getDocumentFieldConfig` to retrieve field configurations based on account mode and timezone.
  - Added `formatExportData` and `mapHeaderFromFieldConfigs` to process export data and headers.
  - Updated the `ExportDocumentsHandler` to include timezone as a parameter.
  - Introduced new constants for field types and labels to improve maintainability.
- Updated dependencies [9e548c2]
- Updated dependencies [0a84843]
  - common@0.24.3
</details>

## Releases on 2025-07-06

### Version 10.19.6
<details>

### Patch Changes
 - Fix file upload API failing due to missing service configuration
 - Update metrics view to fix the issue where the table doesn’t separate "auto" and "manual" data, and to resolve the legend overlap in the ‘Company Documents Count’ chart.
</details>

## Releases on 2025-07-04

### Version 10.19.5
<details>

### Patch Changes
 - Fixed agent transaction save failure when all transactions had been deleted
</details>

### Version 10.19.4
<details>

### Patch Changes
 - In admin accounts, sort users by active first. Add user state formatter.
- Updated dependencies [6a7dee2]
  - common@0.24.2
</details>

### Version 10.19.3
<details>

### Patch Changes
 - Support specifying a multiplier for each comp profile in match criteria when the calculation method is either "Pay commission to grid level" or "Share override to grid level".
 - Hotfix the document processing page crash issue
 - Minor improvements to report processor playground and admin view.
 - Show virtual & virtual type in commission data view
</details>

## Releases on 2025-07-03

### Version 10.19.2
<details>

### Patch Changes
 - Fix for search bar not working on multiple app pages.
</details>

### Version 10.19.1
<details>

### Patch Changes
 - Updated report summary labels
 - Allow user to select manual grouping calculation method
 - Fix lint issue on companies page.
 - Fix the issue where the page crashes when selecting an existing mapping.
- Updated dependencies [daa48af]
  - common@0.24.1
</details>

## Releases on 2025-07-02

### Version 10.19.0
<details>

### Minor Changes
 - Update Companies & Global Companies Page: 1. Link Suggestions: Added a feature to suggest potential account companies that can be linked to each global company. It shows how many unlinked companies are available and allows linking them directly. 2. Merge Function: Introduced a merge feature that lets users merge selected fields from one global company into another, helping maintain cleaner and more consistent data.

### Patch Changes
 - Fixed an error that occurred when updating data for the 'state' field
 - Optimise the Companies / Global Companies page:
  1. Fixed the issue preventing global companies from being updated.
  2. Optimized the global companies search function by removing irrelevant search options.
  3. Improved the UI for processors and profiles in the Companies / Global Companies page by adding item counts and collapse functionality.
 - Replace comp reports data source from snapshotdata to accounting transactions in new endpoint and new FE component in '.../comp-reports/report_str_id' route.
 - Trigger multi-company mode when multiple documents are uploaded and no classification results are available. And include company and document type details in the upload preview page. Also align the right of type selector.
 - Replace comp reports data source from snapshotdata to accounting transactions part IV
 - Allow user to override policy splits
 - In the past, importing a file would automatically create a new mapping, often resulting in many similar or duplicate mappings. Now, users must now click the ‘Save’ button to confirm whether they want to keep the mapping.
- Updated dependencies [dc66daf]
- Updated dependencies [2c59195]
- Updated dependencies [2c59195]
- Updated dependencies [034e734]
  - common@0.24.0
</details>

## Releases on 2025-07-01

### Version 10.18.0
<details>

### Minor Changes
 - Replace Select with EnhancedSelect for component ProcessMappingForm

### Patch Changes
 - Enhance the data update tool to support executing queries independently of custom code logic.
</details>

## Releases on 2025-06-30

### Version 10.17.4
<details>

### Patch Changes
 - Fixed crash on data actions tool due to new virtual_type field
 - Update front-end and API to support one-to-many carrier relationships.
 - Add bulk receivable calc for policy.
</details>

