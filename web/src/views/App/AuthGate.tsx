import { useEffect, useState } from 'react';

import { auth } from '@/firebase';
import LoadingMask from '@/components/atoms/LoadingMask';

export const AuthGate = ({ children }: { children: React.ReactNode }) => {
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const unsubscribe = auth.onAuthStateChanged(() => {
      setLoading(false);
    });

    return () => {
      unsubscribe();
    };
  }, []);

  if (loading) {
    return <LoadingMask />;
  }

  return <>{children}</>;
};
