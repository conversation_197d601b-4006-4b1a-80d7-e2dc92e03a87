import { Box, useMediaQuery } from '@mui/material';
import hotkeys from 'hotkeys-js';
import { ReactNode, useCallback, useEffect, useState } from 'react';
import { Helmet } from 'react-helmet';
import { Outlet, useLocation, useNavigate } from 'react-router-dom';

import AppDrawer from '@/components/AppDrawer';
import EmptyState from '@/components/EmptyState';
import Onboarding from '@/components/Onboarding';
import LandingImg from '@/illustrations/landing_tp.png';
import { useAccountStore } from '@/store';

const hotkeyDefs = {
  '/admin/accounts': 'g+a',
  '/admin/users': 'g+u',
  '/commissions': 'g+c',
  '/documents': 'g+d',
  '/insights': 'g+i',
  '/policies': 'g+p',
  '/reconciliation': 'g+o',
  '/reports': 'g+r',
  '/settings': 'g+s',
  '/views': 'g+v',
};

const capitalize = (s: string) => s && s[0].toUpperCase() + s.slice(1);

const pathToTitle = (path: string) =>
  path
    .split('/')
    .map(capitalize)
    .map((s) => s.replaceAll('-', ' '))
    .filter((s) => s)
    .join(' - ');

export default function Layout(props: { user: unknown; Header: ReactNode }) {
  const { user, Header } = props;
  const navigate = useNavigate();
  const isMobile = useMediaQuery('(max-width:600px)');
  const { selectedAccount, userState } = useAccountStore();
  const location = useLocation();
  const [slugLabel, setSlugLabel] = useState(pathToTitle(location.pathname));

  useEffect(() => {
    setSlugLabel(pathToTitle(location.pathname));
  }, [location.pathname]);

  const createHotkey = useCallback(
    (hotkey: string, path: string) => {
      hotkeys(hotkey, (e) => {
        e.preventDefault();
        navigate(path);
      });
    },
    [navigate]
  );

  const createHotkeys = useCallback(() => {
    Object.entries(hotkeyDefs).forEach(([k, v]) => {
      createHotkey(v, k);
    });
  }, [createHotkey]);

  useEffect(() => {
    createHotkeys();
    return () => {
      hotkeys.unbind();
    };
  }, [createHotkeys]);

  const appBarHeight = isMobile ? '56px' : '64px';

  const isValidUserLoggedIn = user != null && userState != null;
  const isUserOnboarding =
    isValidUserLoggedIn && userState?.userOverallState !== 'active';
  const isActiveUser =
    isValidUserLoggedIn &&
    selectedAccount &&
    Object.keys(selectedAccount).length > 0;

  const Content = () => {
    if (isUserOnboarding) {
      return <Onboarding userState={userState} />;
    }

    if (isActiveUser) {
      return (
        <Box
          sx={{
            height: `calc(100vh - ${appBarHeight})`,
            display: 'flex',
          }}
        >
          <AppDrawer />
          <Outlet />
        </Box>
      );
    }
    return (
      <EmptyState
        image={<img alt={'landing page image'} src={LandingImg} width="60%" />}
        title={
          <Box
            display="flex"
            alignItems="center"
            justifyContent="center"
            mb={2}
          >
            <Box
              component="img"
              sx={{ height: 60, width: 60 }}
              src={'/logo256.png'}
            />
            Fintary
          </Box>
        }
        description="One place for all your financial operations"
        size="large"
      />
    );
  };

  return (
    <>
      <Helmet>
        <title>Fintary{slugLabel && ` - ${slugLabel}`}</title>
      </Helmet>
      <Box className="flex flex-col w-screen h-screen overflow-hidden">
        <Box sx={{ height: appBarHeight }}>{Header}</Box>
        <Content />
      </Box>
    </>
  );
}
