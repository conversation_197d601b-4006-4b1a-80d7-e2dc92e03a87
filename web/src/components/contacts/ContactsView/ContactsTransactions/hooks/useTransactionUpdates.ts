import { useState, useCallback, useEffect } from 'react';

import { Transaction, TransactionsPagination } from '../types';

type UseTransactionUpdatesProps = {
  currentTransactions: Transaction[];
  currentPagination: TransactionsPagination;
};

export const useTransactionUpdates = (props: UseTransactionUpdatesProps) => {
  const [transactions, setTransactions] = useState(props.currentTransactions);
  const [pagination, setPagination] = useState(props.currentPagination);

  useEffect(() => {
    setTransactions(props.currentTransactions);
  }, [props.currentTransactions]);

  useEffect(() => {
    setPagination(props.currentPagination);
  }, [props.currentPagination]);

  const updateTransactions = useCallback((data: Transaction[]) => {
    setTransactions(data);
  }, []);

  return {
    updatedTransactions: transactions,
    updatedPagination: pagination,
    updateTransactions,
  };
};
