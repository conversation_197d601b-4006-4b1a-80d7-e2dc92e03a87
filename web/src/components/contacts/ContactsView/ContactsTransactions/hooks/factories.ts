import { TransactionStatuses } from 'common/globalTypes';

import { Transaction, TransactionsPagination } from '../types';

export const buildTransactionsFactory = (
  override?: Partial<Transaction>
): Transaction => {
  return {
    id: '5131',
    strId: 'ab3d390dfd2dd731c000',
    amount: '15',
    date: new Date(),
    status: TransactionStatuses.DRAFT,
    notes: 'test 2222',
    contactId: '132',
    details: buildTransactionsDetailsFactory(),
    ...(override ? override : {}),
  };
};

export const buildTransactionsDetailsFactory = (): Transaction['details'] => {
  return [
    {
      id: '3863948',
      strId: '8ec9a19ce1de69a2e861',
      amount: '15',
      date: new Date(),
      status: TransactionStatuses.DRAFT,
      notes: '',
      tags: [],
    },
  ];
};

export const buildTransactionsPaginationFactory = (
  override?: Partial<TransactionsPagination>
): TransactionsPagination => {
  return {
    transactionsPageMap: {},
    totalItemsPerPage: 10,
    totalItems: 0,
    currentPage: 0,
    previousPage: undefined,
    nextPage: undefined,
    lastPage: undefined,
    totalPages: 0,
    ...(override ? override : {}),
  };
};
