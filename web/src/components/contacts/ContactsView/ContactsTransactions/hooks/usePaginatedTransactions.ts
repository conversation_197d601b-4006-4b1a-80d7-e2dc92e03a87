import { useState, useMemo, useEffect, useCallback } from 'react';
import { isNill } from 'common/helpers';

import { OnSubmitResult, Transaction } from './../types/index';
import { useGetAgentTransactions } from './useGetAgentTransactions';
import { TransactionsPagination } from '../types';

export const usePaginatedTransactions = (agentStrId: string) => {
  const [transactions, setTransactions] = useState<{
    data: Transaction[];
    isLoading: boolean;
    hasError: boolean;
  }>({
    data: [],
    isLoading: true,
    hasError: false,
  });

  const [pagination, setPagination] = useState<TransactionsPagination>({
    transactionsPageMap: {},
    totalItems: 0,
    totalPages: 0,
    currentPage: 1,
    totalItemsPerPage: 10,
  });

  const agentQueryParams = useMemo(
    () => ({
      agentStrId,
      page: pagination.currentPage,
      limit: pagination.totalItemsPerPage,
    }),
    [agentStrId, pagination.currentPage, pagination.totalItemsPerPage]
  );

  const fetchResult = useGetAgentTransactions(agentQueryParams);
  const refetch = useMemo(() => fetchResult.refetch, [fetchResult]);

  useEffect(() => {
    setTransactions((prev) => ({
      ...prev,
      isLoading: fetchResult.data.isLoading,
      hasError: fetchResult.data.isError,
    }));
  }, [fetchResult.data.isLoading, fetchResult.data.isError]);

  useEffect(() => {
    if (
      !fetchResult.data.isLoading &&
      !fetchResult.data.isError &&
      fetchResult.data.transactions
    ) {
      const newTransactions = fetchResult.data.transactions;
      const { currentPage, totalItems } = fetchResult.pagination || {};

      setTransactions((prev) => ({
        ...prev,
        data: newTransactions,
      }));

      if (fetchResult.pagination) {
        setPagination((prev) => ({
          ...prev,
          ...(!isNill(currentPage) && { currentPage }),
          ...(!isNill(totalItems) && { totalItems }),
          transactionsPageMap: {
            ...prev.transactionsPageMap,
            ...(!isNill(currentPage) && {
              // IsNill ensures currentPage is a valid number, but still needs to cast here
              [currentPage as number]: newTransactions,
            }),
          },
        }));
      }
    }
  }, [
    fetchResult.data.transactions,
    fetchResult.data.isLoading,
    fetchResult.data.isError,
    fetchResult.pagination,
  ]);

  const handleNewPageSelected = useCallback((newPage: number) => {
    setPagination((prevState) => ({
      ...prevState,
      currentPage: newPage,
    }));
  }, []);

  const handleNewTransactionsLimitSelected = useCallback((newLimit: number) => {
    setPagination((prevState) => ({
      ...prevState,
      totalItemsPerPage: newLimit,
      currentPage: 0, // Reset to first page when limit changes
    }));
  }, []);

  const handleOnUpdateSubmitSuccess = useCallback(
    (result: OnSubmitResult) => {
      const currentPage = pagination.currentPage;
      const totalItemsPerPage = pagination.totalItemsPerPage;
      const totalTransactions = pagination.totalItems;

      const hasNewItemsExceededPageLimit =
        result.totalNewItemsAdded + transactions.data.length >
        totalItemsPerPage;

      if (hasNewItemsExceededPageLimit) {
        /**
         * TODO: add logic to handle with order by on dates
         *   1. when orderBy is 'newest', we need to re-fetch the first page
         *   2. when orderBy is 'oldest', re-fetch the last page (* corrent logic below)
         */
        const newTotalTransactions =
          totalTransactions + result.totalNewItemsAdded;
        const floatNumber = newTotalTransactions / totalItemsPerPage;
        const lastPage = Math.ceil(floatNumber);

        setPagination((prev) => ({ ...prev, currentPage: lastPage }));

        return;
      }

      const hasDeletedAllPageItems =
        result.totalDeleted >= transactions.data.length;

      if (hasDeletedAllPageItems) {
        const isFirstPage = currentPage === 0;

        if (isFirstPage) {
          return;
        }

        const previousPage = currentPage - 1;

        setPagination((prev) => ({ ...prev, currentPage: previousPage }));

        return;
      }
      refetch();
    },
    [
      pagination.currentPage,
      pagination.totalItemsPerPage,
      pagination.totalItems,
      transactions.data.length,
      refetch,
    ]
  );

  return {
    pagination,
    transactions,
    handleOnUpdateSubmitSuccess,
    handleNewPageSelected,
    handleNewTransactionsLimitSelected,
  };
};
