import { renderHook, act } from '@testing-library/react';

import { useTransactionUpdates } from './useTransactionUpdates';
import {
  buildTransactionsFactory,
  buildTransactionsPaginationFactory,
} from './factories';

describe.skip('useTransactionUpdates', () => {
  const transactions = [buildTransactionsFactory()];
  const pagination = buildTransactionsPaginationFactory();

  describe('when updateTransactions is called', () => {
    it('Given new transactions are added, should update transactions and pagination correctly', () => {
      const { result } = renderHook(() =>
        useTransactionUpdates({
          currentTransactions: transactions,
          currentPagination: pagination,
        })
      );

      const newTransaction = buildTransactionsFactory();
      const updatedTransactionsData = [...transactions, newTransaction];

      act(() => {
        result.current.updateTransactions(updatedTransactionsData);
      });

      expect(result.current.updatedTransactions).toEqual(
        updatedTransactionsData
      );
      expect(result.current.updatedPagination.totalItems).toBe(
        pagination.totalItems + 1
      );
    });

    it('Given transactions are removed, should update transactions and pagination correctly', () => {
      const multipleTransactions = [
        buildTransactionsFactory(),
        buildTransactionsFactory(),
        buildTransactionsFactory(),
      ];
      const { result } = renderHook(() =>
        useTransactionUpdates({
          currentTransactions: multipleTransactions,
          currentPagination: {
            ...pagination,
            totalItems: 3,
          },
        })
      );

      const updatedTransactionsData = [multipleTransactions[0]];

      act(() => {
        result.current.updateTransactions(updatedTransactionsData);
      });

      expect(result.current.updatedTransactions).toEqual(
        updatedTransactionsData
      );
      expect(result.current.updatedPagination.totalItems).toBe(1);
    });

    it('Given all transactions are removed, should update transactions and pagination correctly', () => {
      const { result } = renderHook(() =>
        useTransactionUpdates({
          currentTransactions: transactions,
          currentPagination: pagination,
        })
      );

      act(() => {
        result.current.updateTransactions([]);
      });

      expect(result.current.updatedTransactions).toEqual([]);
      expect(result.current.updatedPagination.totalItems).toBe(
        transactions.length - pagination.totalItems
      );
      expect(result.current.updatedPagination.totalPages).toBe(
        pagination.totalPages - 1
      );
    });

    it('Given the number of transactions is unchanged, should update transactions but keep totalItems the same', () => {
      const { result } = renderHook(() =>
        useTransactionUpdates({
          currentTransactions: transactions,
          currentPagination: pagination,
        })
      );

      const updatedTransaction = buildTransactionsFactory({
        ...transactions[0],
        amount: '500',
      });
      const updatedTransactionsData = [updatedTransaction];

      act(() => {
        result.current.updateTransactions(updatedTransactionsData);
      });

      expect(result.current.updatedTransactions).toEqual(
        updatedTransactionsData
      );
      expect(result.current.updatedPagination.totalItems).toBe(
        pagination.totalItems
      );
    });
  });
});
