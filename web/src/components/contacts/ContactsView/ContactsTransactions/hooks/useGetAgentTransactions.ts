import { TransactionStatuses } from 'common/globalTypes';
import { AccountingTransactionsType } from 'common/constants/accounting_transactions';
import { useMemo } from 'react';

import API from '@/services/API';
import { Pagination, Transaction } from '../types';

type GetBasicQueryWithoutData = Omit<
  ReturnType<typeof API.getBasicQuery>,
  'data'
> & {
  transactions: any[];
  total_transactions: number;
  pagination?: {
    current_page: number;
    previous_page: number;
    next_page: number;
    last_page: number;
    total_pages: number;
    total_items_per_page: number;
  };
};

type UseAgentTransactions = {
  refetch: VoidFunction;
  data: {
    transactions: Transaction[];
    isError: boolean;
    isLoading: boolean;
  };
  pagination?: Pagination;
};

const useGetAgentTransactions = (props: {
  agentStrId: string;
  page: number;
  limit: number;
}): UseAgentTransactions => {
  const querySearch = useMemo(() => {
    return `agent_str_id=${props.agentStrId}&page=${props.page}&limit=${props.limit}`;
  }, [props.agentStrId, props.page, props.limit]);

  const response = API.getBasicQuery<GetBasicQueryWithoutData>(
    'accounting/transactions/per-agent',
    querySearch
  );

  const memoizedData = useMemo(() => {
    if (!Array.isArray(response?.data?.transactions)) return [];

    return response.data.transactions.map((item) => {
      const transactionDetails = Array.isArray(
        item.accounting_transaction_details
      )
        ? item.accounting_transaction_details
        : [];

      const savedReport = item.saved_report
        ? {
            id: item.saved_report_id,
            strId: item.saved_report.str_id,
            name: item.saved_report.name,
          }
        : undefined;

      return {
        id: item.id.toString(),
        strId: item.str_id,
        amount: item.amount,
        date: item.date ? new Date(item.date) : undefined,
        status: TransactionStatuses[item.status] || TransactionStatuses.DRAFT,
        type: AccountingTransactionsType[item.type],
        notes: item.notes || '',
        contactId: item.contact_id,
        savedReport,
        details: transactionDetails.map((detail) => ({
          id: detail.id.toString(),
          strId: detail.str_id,
          commissionStrId: detail.commission_str_id,
          savedReport: detail.saved_report
            ? {
                id: detail.saved_report.id,
                strId: detail.saved_report.str_id,
                name: detail.saved_report.name,
              }
            : undefined,
          type: AccountingTransactionsType[detail.type],
          amount: detail.amount,
          date: detail.date ? new Date(detail.date) : undefined,
          status: detail.status as TransactionStatuses,
          notes: detail.notes || '',
          tags: detail.tags || [],
          statementId: detail.statement_id,
        })),
      };
    });
  }, [response?.data]);

  const memoizedPagination = useMemo(() => {
    if (!response?.data?.pagination) return undefined;

    return {
      currentPage: response.data.pagination.current_page,
      previousPage: response.data.pagination.previous_page,
      nextPage: response.data.pagination.next_page,
      lastPage: response.data.pagination.last_page,
      totalPages: response.data.pagination.total_pages,
      totalItemsPerPage: response.data.pagination.total_items_per_page,
      totalItems: response.data.total_transactions,
    };
  }, [response?.data?.pagination, response?.data?.total_transactions]);

  return {
    refetch: () => response.refetch(),
    data: {
      transactions: memoizedData,
      isError: response.isError,
      isLoading: response.isLoading,
    },
    pagination: memoizedPagination,
  };
};

const useSaveAgentTransactions = () => {
  return {
    mutation: API.getMutation('accounting/transactions/per-agent', 'POST'),
    convertTransactionsToPayload: (input: {
      agentStrId: string;
      updates: Transaction[];
      deletes: Transaction[];
    }) => {
      const deletes = input.deletes.map((t) => ({
        str_id: t.strId,
        details: t.details.map(({ strId }) => ({ str_id: strId })),
      }));

      return {
        agent_str_id: input.agentStrId,
        transactions: {
          updates: input.updates.map((transaction) => ({
            transaction_str_id: transaction.strId,
            date: transaction.date,
            amount: parseFloat(transaction.amount),
            status: transaction.status,
            notes: transaction.notes,
            details: transaction.details.map((detail) => ({
              transaction_detail_str_id: detail.strId,
              date: detail.date,
              amount: parseFloat(detail.amount),
              status: detail.status,
              notes: detail.notes,
              tags: detail.tags || [],
            })),
          })),
          deletes,
        },
      };
    },
  };
};

export { useGetAgentTransactions, useSaveAgentTransactions };
