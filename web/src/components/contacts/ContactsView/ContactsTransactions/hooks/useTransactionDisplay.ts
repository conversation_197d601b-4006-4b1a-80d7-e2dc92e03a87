import { useMemo } from 'react';

import { Transaction, TransactionsPagination } from '../types';

type TransactionData = {
  data: Transaction[];
  hasError: boolean;
  isLoading: boolean;
};

type UseTransactionDisplayProps = {
  editModeEnabled: boolean;
  updatedTransactions: Transaction[];
  updatedPagination: TransactionsPagination;
  currentTransactions: TransactionData;
  currentPagination: TransactionsPagination;
};

export const useTransactionDisplay = (props: UseTransactionDisplayProps) => {
  const {
    editModeEnabled,
    updatedTransactions,
    updatedPagination,
    currentTransactions,
    currentPagination,
  } = props;

  const updating = useMemo(
    () => ({
      transactions: {
        data: updatedTransactions,
        hasError: false,
        isLoading: false,
      },
      pagination: updatedPagination,
    }),
    [updatedTransactions, updatedPagination]
  );

  const readOnly = useMemo(
    () => ({
      transactions: currentTransactions,
      pagination: currentPagination,
    }),
    [currentTransactions, currentPagination]
  );

  const display = useMemo(
    () => (editModeEnabled ? updating : readOnly),
    [editModeEnabled, updating, readOnly]
  );

  return { display };
};
