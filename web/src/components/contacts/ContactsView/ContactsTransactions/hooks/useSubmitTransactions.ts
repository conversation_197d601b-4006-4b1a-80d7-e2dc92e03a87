import { useState, useCallback } from 'react';

import { useSaveAgentTransactions } from './useGetAgentTransactions';
import {
  OnSubmitResult,
  Transaction,
  TransactionsSaveUpdatesState,
  TransactionsUpdates,
} from '../types';

const initTransactionsSaveUpdatesState = (): TransactionsSaveUpdatesState => ({
  onLoading: false,
  success: false,
});

type UseSubmitTransactionsProps = {
  agentStrId: string;
  onSuccess: (result: OnSubmitResult) => void;
};

export const useSubmitTransactions = ({
  agentStrId,
  onSuccess,
}: UseSubmitTransactionsProps) => {
  const [transactionsSaveUpdates, setTransactionsSaveUpdates] =
    useState<TransactionsSaveUpdatesState>(initTransactionsSaveUpdatesState());

  const { mutation: saveTransactionsMutation, convertTransactionsToPayload } =
    useSaveAgentTransactions();

  const resetSubmitStatus = useCallback(() => {
    setTransactionsSaveUpdates(initTransactionsSaveUpdatesState());
  }, []);

  const getTotalNewItemsAdded = useCallback(
    (updates: TransactionsUpdates, transactions: Transaction[]) => {
      let count = 0;

      updates.updated.forEach((transaction) => {
        if (!transactions.find((t) => t.id === transaction.id)) {
          count++;
        }
      });

      return count;
    },
    []
  );

  const submitTransactionsUpdates = useCallback(
    (
      updates: TransactionsUpdates,
      initialTransactions: Transaction[]
    ): void => {
      setTransactionsSaveUpdates({
        onLoading: true,
        success: false,
        error: undefined,
      });

      const totalNewItemsAdded = getTotalNewItemsAdded(
        updates,
        initialTransactions
      );

      const deletes = initialTransactions.filter(
        ({ id }) =>
          !!updates.deleted.find(
            ({ transactionId }) => transactionId === id.toString()
          )
      );

      if (updates.deleted.length > 0 || updates.updated.length > 0) {
        const payload = convertTransactionsToPayload({
          deletes,
          updates: updates.updated,
          agentStrId: agentStrId,
        });

        saveTransactionsMutation.mutate(payload, {
          onSuccess: () => {
            setTransactionsSaveUpdates({
              success: true,
              onLoading: false,
              error: undefined,
            });
            onSuccess({
              totalDeleted: deletes.length,
              totalNewItemsAdded,
            });
          },
          onError: (error) => {
            const isDisabledUpdates = error.message.includes(
              'Updates are disabled'
            );
            const message = isDisabledUpdates
              ? error.message
              : 'An error occurred while saving transactions. Please try again later.';
            setTransactionsSaveUpdates({
              success: false,
              onLoading: false,
              error: { message, type: isDisabledUpdates ? 'warning' : 'error' },
            });
          },
        });
      } else {
        setTransactionsSaveUpdates({
          onLoading: false,
          success: true,
          error: undefined,
        });
      }
    },
    [
      agentStrId,
      convertTransactionsToPayload,
      saveTransactionsMutation,
      onSuccess,
      getTotalNewItemsAdded,
    ]
  );

  return {
    onSubmitUpdatesResult: transactionsSaveUpdates,
    submitTransactionsUpdates,
    resetSubmitStatus,
  };
};
