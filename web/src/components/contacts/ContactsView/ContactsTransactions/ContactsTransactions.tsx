import React, { useCallback, useState } from 'react';

import ContactsTransactionsView from './ContactsTransactionsView';
import { Roles } from '@/types';
import { usePaginatedTransactions } from './hooks/usePaginatedTransactions';
import { useSubmitTransactions } from './hooks/useSubmitTransactions';
import { useTransactionUpdates } from './hooks/useTransactionUpdates';
import { useTransactionDisplay } from './hooks/useTransactionDisplay';
import { Transaction, TransactionsUpdates } from './types';

type ContactsTransactionsAddProps = {
  field: { id: string; label: string };
  agentStrId: string;
  userRole: Roles | null;
};

export const ContactsTransactions: React.FC<ContactsTransactionsAddProps> = (
  props
) => {
  if (!props.agentStrId) {
    return null;
  }

  const [showSettledEnabled, setShowSettledEnabled] = useState<boolean>(false);
  const [editModeEnabled, setEditModeEnabled] = useState<boolean>(false);

  const {
    transactions,
    pagination,
    handleOnUpdateSubmitSuccess,
    handleNewPageSelected,
    handleNewTransactionsLimitSelected,
  } = usePaginatedTransactions(props.agentStrId);

  const { updateTransactions, updatedPagination, updatedTransactions } =
    useTransactionUpdates({
      currentTransactions: transactions.data,
      currentPagination: pagination,
    });

  const { display } = useTransactionDisplay({
    editModeEnabled,
    updatedTransactions,
    updatedPagination,
    currentTransactions: transactions,
    currentPagination: pagination,
  });

  const {
    onSubmitUpdatesResult,
    submitTransactionsUpdates,
    resetSubmitStatus,
  } = useSubmitTransactions({
    agentStrId: props.agentStrId,
    onSuccess: handleOnUpdateSubmitSuccess,
  });

  const handleEditModeOnClick = useCallback(
    () => setEditModeEnabled((prev) => !prev),
    []
  );
  const handleShowSettlementsOnClick = useCallback(
    () => setShowSettledEnabled((prev) => !prev),
    []
  );
  const handleSubmitTransactionsUpdates = useCallback(
    (updates: TransactionsUpdates) => {
      submitTransactionsUpdates(updates, transactions.data);
    },
    [submitTransactionsUpdates, transactions.data]
  );

  const handleUpdatedTransactions = useCallback(
    (transactions: Transaction[]) => {
      updateTransactions(transactions);

      if (onSubmitUpdatesResult.success || onSubmitUpdatesResult.error) {
        resetSubmitStatus();
      }
    },
    [
      updateTransactions,
      onSubmitUpdatesResult.success,
      onSubmitUpdatesResult.error,
      resetSubmitStatus,
    ]
  );

  return (
    <ContactsTransactionsView
      handleEditModeOnClick={handleEditModeOnClick}
      handleNewPageSelected={handleNewPageSelected}
      handleNewTransactionsLimitSelected={handleNewTransactionsLimitSelected}
      handleShowSettlementsOnClick={handleShowSettlementsOnClick}
      handleSubmitTransactionsUpdates={handleSubmitTransactionsUpdates}
      handleUpdatedTransactions={handleUpdatedTransactions}
      updateTransactions={updateTransactions}
      isEditModeEnabled={editModeEnabled}
      sectionLabel={props.field.label}
      showEditModeOption={!(props.userRole === Roles.PRODUCER)}
      showSettledEnabled={showSettledEnabled}
      transactions={display.transactions}
      pagination={display.pagination}
      onSubmitUpdatesResult={onSubmitUpdatesResult}
    />
  );
};

export default ContactsTransactions;
