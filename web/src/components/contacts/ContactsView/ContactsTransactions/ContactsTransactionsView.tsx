import React, { useEffect } from 'react';
import {
  Box,
  Checkbox,
  FormControlLabel,
  IconButton,
  Typography,
} from '@mui/material';
import { EditOffOutlined, EditOutlined } from '@mui/icons-material';

import { TransactionTable } from './components/TransactionTable';
import {
  Transaction,
  TransactionsPagination,
  TransactionsSaveUpdatesState,
  TransactionsUpdates,
} from './types';
import useSnackbar from '@/contexts/useSnackbar';

type ContactsTransactionsAddViewProps = {
  handleEditModeOnClick: VoidFunction;
  handleShowSettlementsOnClick: VoidFunction;
  handleSubmitTransactionsUpdates: (updates: TransactionsUpdates) => void;
  handleUpdatedTransactions: (transactions: Transaction[]) => void;
  updateTransactions: (transactions: Transaction[]) => void;
  handleNewPageSelected: (newPage: number) => void;
  handleNewTransactionsLimitSelected: (newLimit: number) => void;
  isEditModeEnabled: boolean;
  showSettledEnabled: boolean;
  sectionLabel: string;
  showEditModeOption: boolean;
  transactions: {
    data: Transaction[];
    isLoading: boolean;
    hasError: boolean;
  };
  pagination: TransactionsPagination;
  onSubmitUpdatesResult: TransactionsSaveUpdatesState;
};

const ContactsTransactionsView: React.FC<ContactsTransactionsAddViewProps> = (
  props
) => {
  const { showSnackbar } = useSnackbar();
  const { onSubmitUpdatesResult } = props;

  useEffect(() => {
    if (onSubmitUpdatesResult?.success) {
      showSnackbar('Transactions updated successfully', 'success');
    } else if (onSubmitUpdatesResult?.error) {
      const { message, type } = onSubmitUpdatesResult.error;
      showSnackbar(message, type);
    }
  }, [
    onSubmitUpdatesResult.success,
    onSubmitUpdatesResult.error,
    showSnackbar,
  ]);

  return (
    <Box sx={{ width: '100%', mt: 4, mb: 4 }}>
      <Box
        sx={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
        }}
      >
        <Typography variant="subtitle2">{props.sectionLabel}</Typography>

        <Box
          sx={{
            display: 'flex',
            flexDirection: 'row',
            alignItems: 'center',
            justifyContent: 'flex-end',
          }}
        >
          <FormControlLabel
            control={
              <Checkbox
                checked={props.showSettledEnabled}
                onClick={props.handleShowSettlementsOnClick}
              />
            }
            label="Show settlements"
            value={''}
          />

          {props.showEditModeOption && (
            <EditMode
              onClick={props.handleEditModeOnClick}
              enabled={props.isEditModeEnabled}
            />
          )}
        </Box>
      </Box>

      <TransactionTable
        pagination={props.pagination}
        onPageChange={props.handleNewPageSelected}
        onRowsPerPageChange={props.handleNewTransactionsLimitSelected}
        isEditMode={props.isEditModeEnabled}
        showSettledTransactions={props.showSettledEnabled}
        transactions={props.transactions.data}
        isTransactionsLoading={props.transactions.isLoading}
        isTransactionsError={props.transactions.hasError}
        onSubmitTransactionsUpdates={props.handleSubmitTransactionsUpdates}
        handleUpdatedTransactions={props.handleUpdatedTransactions}
        updateTransactions={props.updateTransactions}
        onSubmitUpdatesResult={{
          onLoading: onSubmitUpdatesResult.onLoading,
          success: onSubmitUpdatesResult.success,
        }}
      />
    </Box>
  );
};

export default ContactsTransactionsView;

/**
 *
 * Private components for the ContactsTransactionsView
 *
 */
const EditMode: React.FC<{
  onClick: VoidFunction;
  enabled?: boolean;
}> = (props) => {
  return (
    <IconButton onClick={props.onClick}>
      {props.enabled ? <EditOffOutlined /> : <EditOutlined />}
    </IconButton>
  );
};
