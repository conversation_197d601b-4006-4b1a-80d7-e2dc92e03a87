import React, { memo, ReactElement, useEffect, useState } from 'react';
import { styled, TableCell, TableRow } from '@mui/material';
import { TransactionStatuses } from 'common/globalTypes';
import { isNil } from 'lodash';

import { DateCell } from './cells/DateCell';
import { AmountCell } from './cells/AmountCell';
import { StatusCell } from './cells/StatusCell';
import { NotesCell } from './cells/NotesCell';
import { RowUpdateCell } from './cells/RowUpdateCell';
import { RowExpandCell } from './cells/RowExpandCell';
import { ReferenceCell, ReferenceCellProps } from './cells/ReferenceCell';
import { TagsCell } from './cells/TagsCell';

export type TransactionTableRowProps = {
  children?: never;

  handleRowDeleted: (rowTarged: RowTarged) => void;
  handleSaveRowUpdates: (paylaod: TransactionTableRowUpdates) => void;

  editRowEnabled: boolean;
  isRowOpen?: boolean;
  transactionId?: string;
  transactionDetailId?: string;

  amountCellValue?: string;
  amountCellLabel?: string;
  amountCellDisabled?: boolean;
  dateCellValue?: Date;
  expandCellEnabled?: boolean;
  expandCellOnClick?: (transactionId: string) => void;
  expandCellIsOpen?: boolean;
  notesCellValue?: string;
  statusCellValue?: TransactionStatuses;
  statusCellDisabledEdit?: boolean;
  tagsCellValues?: string[];
  referenceCellSavedReport?: ReferenceCellProps['savedReport'];
  referenceCellCommissionStrId?: string;
};

const TransactionTableRow: React.FC<TransactionTableRowProps> = memo(
  (props) => {
    const [rowValues, setRowValues] = useState<RowUpdatesState>(
      initRowValues(props)
    );

    const handleDiscardChanges = () => {
      setRowValues(initRowValues(props));
    };

    const isValidRowUpdates = (updates: RowUpdatesState) => {
      return (
        Number.isFinite(Number(updates.amountCellValue)) &&
        !isNil(updates.dateCellValue) &&
        !isNil(updates.statusCellValue)
      );
    };

    const handleUpdates = (updates: RowUpdatesState) => {
      const newValues = { ...rowValues, ...updates };

      setRowValues(newValues);
    };

    const handleOnSaveRowChanges = () => {
      props.handleSaveRowUpdates({
        rowTarget: {
          transactionId: props.transactionId,
          transactionDetailId: props.transactionDetailId,
        },
        updates: {
          date: rowValues.dateCellValue,
          amount: rowValues.amountCellValue,
          status: rowValues.statusCellValue,
          tags: rowValues.tagsCellValues,
          notes: rowValues.notesCellValue,
        },
      });
    };

    useEffect(() => {
      setRowValues((prevState) => {
        return {
          ...prevState,
          amountCellValue: props.amountCellValue,
        };
      });
    }, [props.amountCellValue]);

    const isValidRowUpdate = isValidRowUpdates(rowValues);
    const hasRowUpdate = Object.keys(rowValues).some((key) => {
      return rowValues[key] !== props[key];
    });

    return (
      <React.Fragment key={props.transactionId || props.transactionDetailId}>
        <VerticallyCenteredTableRow>
          <RowExpandCell
            enabled={props.expandCellEnabled}
            isOpen={props.isRowOpen}
            transactionId={props.transactionId}
            transactionDetailId={props.transactionDetailId}
            onClick={props.expandCellOnClick}
          />

          <DateCell
            editCellEnabled={props.editRowEnabled}
            value={rowValues.dateCellValue}
            onChange={(date) => handleUpdates({ dateCellValue: date })}
          />

          <AmountCell
            editCellEnabled={props.editRowEnabled}
            label={props.amountCellLabel}
            disabledEdit={props.amountCellDisabled}
            value={rowValues.amountCellValue}
            onChange={(amount) => handleUpdates({ amountCellValue: amount })}
          />

          <StatusCell
            editCellEnabled={props.editRowEnabled}
            value={rowValues.statusCellValue}
            disabledEdit={props.statusCellDisabledEdit}
            onChange={(status) => handleUpdates({ statusCellValue: status })}
          />

          <TagsCell
            hidden={props.tagsCellValues === undefined}
            editCellEnabled={props.editRowEnabled}
            values={rowValues.tagsCellValues}
            onChange={(tags) => handleUpdates({ tagsCellValues: tags })}
          />

          <NotesCell
            editCellEnabled={props.editRowEnabled}
            value={rowValues.notesCellValue}
            onChange={(notes) => handleUpdates({ notesCellValue: notes })}
          />

          <ReferenceCell
            savedReport={props.referenceCellSavedReport}
            commissionStrId={props.referenceCellCommissionStrId}
          />

          <RowUpdateCell
            hidden={!props.editRowEnabled}
            transactionId={props.transactionId}
            transactionDetailId={props.transactionDetailId}
            hasUpdate={hasRowUpdate}
            isValidUpdatedRow={isValidRowUpdate}
            handleOnSave={handleOnSaveRowChanges}
            handleOnDelete={props.handleRowDeleted}
            handleDiscardChanges={handleDiscardChanges}
          />
        </VerticallyCenteredTableRow>
      </React.Fragment>
    );
  }
);

export { TransactionTableRow };

/**
 *
 * Type definitions for the TransactionTable
 *
 */
export type RowTarged = {
  transactionId?: string;
  transactionDetailId?: string;
};

type RowUpdatesState = {
  amountCellValue?: string;
  dateCellValue?: Date;
  notesCellValue?: string;
  statusCellValue?: TransactionStatuses;
  tagsCellValues?: string[];
};

type TransactionTableRowUpdates = {
  rowTarget: RowTarged;
  updates: {
    date?: Date;
    amount?: string;
    status?: TransactionStatuses;
    tags?: string[];
    notes?: string;
  };
};

/**
 *
 * Private components for the TransactionTableRow
 *
 */
const VerticallyCenteredTableRow = styled(TableRow)(({ theme }) => ({
  '& .MuiTableCell-root': {
    verticalAlign: 'top',
    padding: theme.spacing(2, '', 2, ''),
  },
}));

export const DefaultTableRow: React.FC<{ children: ReactElement }> = ({
  children,
}) => {
  return (
    <VerticallyCenteredTableRow>
      <TableCell colSpan={8}>{children}</TableCell>
    </VerticallyCenteredTableRow>
  );
};

const initRowValues = (props: TransactionTableRowProps): RowUpdatesState => ({
  amountCellValue: props.amountCellValue,
  dateCellValue: props.dateCellValue,
  notesCellValue: props.notesCellValue,
  statusCellValue: props.statusCellValue,
  tagsCellValues: props.tagsCellValues,
});
