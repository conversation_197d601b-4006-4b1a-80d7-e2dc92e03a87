import React from 'react';
import {
  <PERSON>,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TablePagination,
  TableRow,
  Typography,
} from '@mui/material';
import { AccountingTransactionsType } from 'common/constants/accounting_transactions';
import { transactionTypes } from 'common/constants';
import LoadingButton from '@mui/lab/LoadingButton';

import { TransactionTableColumns } from './TransactionTableColumns';
import {
  DefaultTableRow,
  RowTarged,
  TransactionTableRow,
  TransactionTableRowProps,
} from './TransactionTableRow';
import { TransactionDetailTable } from './TransactionDetailTable';
import {
  Transaction,
  TransactionsPagination,
  TransactionsUpdates,
} from '../types';
import { RemovedRows, RowsOpenMap } from './TransactionTable';
import LoadingCircle from '@/components/atoms/LoadingCircle';

type Props = {
  children?: never;

  addNewDefaultTransactionDetail: (transactionId: string) => void;
  handleRowDeleted: (rowTarget: RowTarged) => void;
  handleSaveRowUpdates: HandleSaveRowUpdates;
  handleToggleExpandTransaction: (transactionId: string) => void;
  handleSaveTransactionsUpdates: VoidFunction;
  handleAddTransaction: VoidFunction;
  onSubmitTransactionsUpdates: (updates: TransactionsUpdates) => void;
  onRowsPerPageChange: (rowsLimit: number) => void;
  onPageChange: (page: number) => void;

  isEditMode: boolean;
  isTransactionsLoading: boolean;
  isTransactionsError: boolean;
  transactions: Transaction[];
  pagination: TransactionsPagination;
  rowsOpenMap: RowsOpenMap;
  removedRowsMap: RemovedRows;
  saveTransactionsSubmit: {
    enabled: boolean;
    loading: boolean;
  };
};

const TransactionTableView: React.FC<Props> = (props) => {
  return (
    <TableContainer>
      <Table>
        <TransactionTableColumns
          isEditMode={props.isEditMode}
          hiddenColumns={['AMOUNT', 'TAGS']}
        />

        <TableBody>
          <TransactionsInfoHeader
            isLoading={props.isTransactionsLoading}
            isError={props.isTransactionsError}
            emptyTransactions={props.transactions.length === 0}
          />

          {!props.isTransactionsLoading &&
            props.transactions.map((transaction, index) => {
              const isRowOpen = !!props.rowsOpenMap[transaction.id]?.isOpen;

              const isTrasactionCompReportPaymentType =
                transaction.type === transactionTypes.COMP_REPORT_PAYMENT;

              const { totalTransactionDetailsRemoved } = props.removedRowsMap
                .transactions[transaction.id] || {
                totalTransactionDetailsRemoved: 0,
              };

              return (
                <React.Fragment key={transaction.id}>
                  <TransactionTableRow
                    handleRowDeleted={props.handleRowDeleted}
                    handleSaveRowUpdates={props.handleSaveRowUpdates}
                    isRowOpen={isRowOpen}
                    editRowEnabled={props.isEditMode}
                    transactionId={transaction.id}
                    amountCellValue={transaction.amount}
                    amountCellLabel={'Total amount'}
                    amountCellDisabled={true}
                    dateCellValue={transaction.date}
                    expandCellEnabled={
                      transaction.details.length > 0 || props.isEditMode
                    }
                    expandCellIsOpen={isRowOpen}
                    expandCellOnClick={props.handleToggleExpandTransaction}
                    notesCellValue={transaction.notes}
                    statusCellValue={transaction.status}
                    statusCellDisabledEdit={
                      transaction.type ===
                      AccountingTransactionsType.COMP_REPORT
                    }
                    referenceCellSavedReport={transaction.savedReport}
                  />

                  {isRowOpen && (
                    <TableRow key={`${index + transaction.id}`}>
                      <TransactionDetailTable
                        handleAddDetailOnClick={
                          props.addNewDefaultTransactionDetail
                        }
                        handleSaveRowUpdates={props.handleSaveRowUpdates}
                        handleRowDeleted={props.handleRowDeleted}
                        isEditMode={props.isEditMode}
                        isTrasactionCompReportPaymentType={
                          isTrasactionCompReportPaymentType
                        }
                        totalTransactionDetailsNotShown={
                          totalTransactionDetailsRemoved
                        }
                        transactionDetails={transaction.details}
                        transactionId={transaction.id}
                      />
                    </TableRow>
                  )}
                </React.Fragment>
              );
            })}

          <TableFooterRow
            handleAddTransaction={props.handleAddTransaction}
            onPageChange={props.onPageChange}
            onRowsPerPageChange={props.onRowsPerPageChange}
            pagination={props.pagination}
            onSaveClick={props.handleSaveTransactionsUpdates}
            isEditMode={props.isEditMode}
            saveTransactionsSubmit={props.saveTransactionsSubmit}
          />
        </TableBody>
      </Table>
    </TableContainer>
  );
};

export { TransactionTableView };

/**
 *
 * Type definitions for the TransactionTable
 *
 */
type HandleSaveRowUpdates = TransactionTableRowProps['handleSaveRowUpdates'];

/**
 *
 * Private components for the TransactionDetailTable
 *
 */

const TableFooterRow: React.FC<{
  children?: never;

  onSaveClick: () => void;
  handleAddTransaction: Props['handleAddTransaction'];
  onPageChange: Props['onPageChange'];
  onRowsPerPageChange: Props['onRowsPerPageChange'];

  pagination: Props['pagination'];
  isEditMode: Props['isEditMode'];
  saveTransactionsSubmit: Props['saveTransactionsSubmit'];
}> = (props) => {
  const editModeVisibilityStyle = {
    visibility: props.isEditMode ? 'visible' : 'hidden',
  };
  return (
    <TableRow>
      <TableCell colSpan={8}>
        <Box
          sx={{
            display: 'flex',
            marginTop: 2,
            marginBottom: 4,
            alignItems: 'center',
            justifyContent: 'space-between',
            maxWidth: 1100,
          }}
        >
          <Box sx={editModeVisibilityStyle}>
            <Button onClick={props.handleAddTransaction}>
              Add transaction
            </Button>
          </Box>
          <Box>
            <TablePagination
              hidden={props.pagination.totalItems === 0}
              disabled={props.isEditMode}
              rowsPerPageOptions={[10, 20, 30, 50]}
              component="div"
              count={props.pagination.totalItems}
              rowsPerPage={props.pagination.totalItemsPerPage}
              page={props.pagination.currentPage - 1}
              onPageChange={(_e, page) => props.onPageChange(page + 1)}
              onRowsPerPageChange={(e) =>
                props.onRowsPerPageChange(Number(e.target.value) + 1)
              }
            />
          </Box>
          <Box sx={editModeVisibilityStyle}>
            <LoadingButton
              onClick={props.onSaveClick}
              variant="contained"
              hidden={!props.isEditMode}
              disabled={!props.saveTransactionsSubmit.enabled}
              loading={props.saveTransactionsSubmit.loading}
            >
              Save transactions updates
            </LoadingButton>
          </Box>
        </Box>
      </TableCell>
    </TableRow>
  );
};

const TransactionsInfoHeader: React.FC<{
  isLoading: boolean;
  isError: boolean;
  emptyTransactions: boolean;
}> = (props) => {
  if (props.isLoading) {
    return (
      <DefaultTableRow>
        <LoadingCircle />
      </DefaultTableRow>
    );
  }

  if (props.isError) {
    return (
      <DefaultTableRow>
        <Typography
          sx={{ textAlign: 'center', mt: 2, mb: 2 }}
          variant="body2"
          color="textSecondary"
        >
          An error occurred while loading transactions, please try again later.
        </Typography>
      </DefaultTableRow>
    );
  }

  if (props.emptyTransactions) {
    return (
      <TableRow>
        <TableCell colSpan={8}>
          <Typography
            sx={{ textAlign: 'center', mt: 2, mb: 2 }}
            variant="body2"
            color="textSecondary"
          >
            No transactions found.
          </Typography>
        </TableCell>
      </TableRow>
    );
  }

  return null;
};
