import { TransactionStatuses } from 'common/globalTypes';

import { TransactionTableHelper } from './transactionTable.helpers';
import { Transaction, TransactionDetail } from '../../types';

describe('deleteTransactionDetail', () => {
  it('Should remove the transaction detail for the found transaction in the transactions list by target input', () => {
    const transactions = [
      {
        id: '1',
        details: [{ id: 'd1' }, { id: 'd2' }],
      },
      {
        id: '2',
        details: [{ id: 'd3' }],
      },
    ];

    const target = { transactionId: '1', transactionDetailId: 'd1' };

    const result = TransactionTableHelper.deleteTransactionDetail(
      target,
      transactions as Transaction[]
    );

    expect(result.transactions).toEqual([
      {
        id: '1',
        details: [{ id: 'd2' }],
      },
      {
        id: '2',
        details: [{ id: 'd3' }],
      },
    ]);
    expect(result.transactionUpdated).toEqual({ id: '1' });
  });

  it('Should not change the transactions input when the transaction or transaction detail is not found', () => {
    const transactions = [
      {
        id: '1',
        details: [{ id: 'd1' }, { id: 'd2' }],
      },
      {
        id: '2',
        details: [{ id: 'd3' }],
      },
    ];

    const target = { transactionId: '3', transactionDetailId: 'd4' };

    const result = TransactionTableHelper.deleteTransactionDetail(
      target,
      transactions as Transaction[]
    );

    expect(result.transactions).toEqual(transactions);
    expect(result.transactionUpdated).toBeUndefined();
  });

  it('Should handle cases where the transaction exists but the detail does not', () => {
    const transactions = [
      {
        id: '1',
        details: [{ id: 'd1' }, { id: 'd2' }],
      },
    ];

    const target = { transactionId: '1', transactionDetailId: 'd3' };

    const result = TransactionTableHelper.deleteTransactionDetail(
      target,
      transactions as Transaction[]
    );

    expect(result.transactions).toEqual(transactions);
    expect(result.transactionUpdated).toBeUndefined();
  });

  it('Should handle cases where the transaction has no details', () => {
    const transactions = [
      {
        id: '1',
        details: [],
      } as any as Transaction,
    ];

    const target = { transactionId: '1', transactionDetailId: 'd1' };

    const result = TransactionTableHelper.deleteTransactionDetail(
      target,
      transactions
    );

    expect(result.transactions).toEqual(transactions);
    expect(result.transactionUpdated).toBeUndefined();
  });

  it('Should return a new transactions array and not mutate the original input', () => {
    const transactions = [
      {
        id: '1',
        details: [{ id: 'd1' }, { id: 'd2' }],
      },
    ];

    const target = { transactionId: '1', transactionDetailId: 'd1' };

    const result = TransactionTableHelper.deleteTransactionDetail(
      target,
      transactions as Transaction[]
    );

    expect(result.transactions).not.toBe(transactions);
    expect(transactions[0].details).toHaveLength(2);
  });
});
describe('deleteTransactionById', () => {
  it('Should remove the transaction with the given ID from the transactions list', () => {
    const transactions = [
      { id: '1', details: ['a'] },
      { id: '2', details: ['b'] },
    ];

    const result = TransactionTableHelper.deleteTransactionById(
      '1',
      transactions as any as Transaction[]
    );

    expect(result.transactions).toEqual([{ id: '2', details: ['b'] }]);
    expect(result.transactionDeleted).toEqual({ id: '1', details: ['a'] });
  });

  it('Should not modify the transactions list if the transaction ID is not found', () => {
    const transactions = [
      { id: '1', details: [] },
      { id: '2', details: [] },
    ];

    const result = TransactionTableHelper.deleteTransactionById(
      '3',
      transactions as any as Transaction[]
    );

    expect(result.transactions).toEqual(transactions);
    expect(result.transactionDeleted).toBeUndefined();
  });

  it('Should return a new transactions array and not mutate the original input', () => {
    const transactions = [
      { id: '1', details: [] },
      { id: '2', details: [] },
    ];

    const result = TransactionTableHelper.deleteTransactionById(
      '1',
      transactions as any as Transaction[]
    );

    expect(result.transactions).not.toBe(transactions);
    expect(transactions).toHaveLength(2);
  });

  it('Should handle an empty transactions list gracefully', () => {
    const transactions: Transaction[] = [];

    const result = TransactionTableHelper.deleteTransactionById(
      '1',
      transactions
    );

    expect(result.transactions).toEqual([]);
    expect(result.transactionDeleted).toBeUndefined();
  });
});
describe('getTransactionsUpdates', () => {
  it('Should return updated and deleted transactions based on updatedRows and removedRows', () => {
    const transactions = [
      { id: '1', details: [], status: TransactionStatuses.DRAFT },
      { id: '2', details: [], status: 'COMPLETED' },
      { id: '3', details: [], status: TransactionStatuses.DRAFT },
    ] as any[];

    const updatedRows = [{ transactionId: '1' }, { transactionId: '3' }];
    const removedRows = { '3': true } as any;

    const result = TransactionTableHelper.getTransactionsUpdates({
      transactions,
      updatedRows,
      removedRows,
    });

    expect(result.updated).toEqual([
      { id: '1', details: [], status: TransactionStatuses.DRAFT },
    ]);
    expect(result.deleted).toEqual([{ transactionId: '3' }]);
  });

  it('Should return only updated transactions when no rows are removed', () => {
    const transactions = [
      { id: '1', details: [], status: TransactionStatuses.DRAFT },
      { id: '2', details: [], status: 'COMPLETED' },
    ] as any;

    const updatedRows = [{ transactionId: '1' }];
    const removedRows = { totalRowsRemoved: 0, transactions: {} };

    const result = TransactionTableHelper.getTransactionsUpdates({
      transactions,
      updatedRows,
      removedRows,
    });

    expect(result.updated).toEqual([
      { id: '1', details: [], status: TransactionStatuses.DRAFT },
    ]);
    expect(result.deleted).toEqual([]);
  });

  it('Should return only deleted transactions when no rows are updated', () => {
    const transactions = [
      { id: '1', details: [], status: TransactionStatuses.DRAFT },
      { id: '2', details: [], status: 'COMPLETED' },
    ] as any;

    const updatedRows = [];
    const removedRows = { '2': true } as any;

    const result = TransactionTableHelper.getTransactionsUpdates({
      transactions,
      updatedRows,
      removedRows,
    });

    expect(result.updated).toEqual([]);
    expect(result.deleted).toEqual([{ transactionId: '2' }]);
  });

  it('Should return empty arrays when there are no updated or removed rows', () => {
    const transactions = [
      { id: '1', details: [], status: TransactionStatuses.DRAFT },
      { id: '2', details: [], status: 'COMPLETED' },
    ] as any;

    const updatedRows = [];
    const removedRows = { totalRowsRemoved: 0, transactions: {} };

    const result = TransactionTableHelper.getTransactionsUpdates({
      transactions,
      updatedRows,
      removedRows,
    });

    expect(result.updated).toEqual([]);
    expect(result.deleted).toEqual([]);
  });

  it('Should handle cases where updatedRows reference non-existent transactions', () => {
    const transactions = [{ id: '1', details: [], status: 'PENDING' }] as any;

    const updatedRows = [{ transactionId: '2' }];
    const removedRows = { totalRowsRemoved: 0, transactions: {} };

    const result = TransactionTableHelper.getTransactionsUpdates({
      transactions,
      updatedRows,
      removedRows,
    });

    expect(result.updated).toEqual([]);
    expect(result.deleted).toEqual([]);
  });

  it('Should handle cases where removedRows reference non-existent transactions', () => {
    const transactions = [{ id: '1', details: [], status: 'PENDING' }] as any;

    const updatedRows = [];
    const removedRows = {
      totalRowsRemoved: 1,
      transactions: { '223132': { totalTransactionDetailsRemoved: 0 } },
    };

    const result = TransactionTableHelper.getTransactionsUpdates({
      transactions,
      updatedRows,
      removedRows,
    });

    expect(result.updated).toEqual([]);
    expect(result.deleted).toEqual([{ transactionId: '2' }]);
  });
});
describe('getNonSettledTransactions', () => {
  it('Should return transactions that are not settled', () => {
    const transactions = [
      {
        id: '1',
        status: TransactionStatuses.DRAFT,
        details: [
          { id: 'd1', status: TransactionStatuses.DRAFT },
          { id: 'd2', status: TransactionStatuses.SETTLEMENT },
        ],
      },
      {
        id: '2',
        status: TransactionStatuses.SETTLEMENT,
        details: [{ id: 'd3', status: TransactionStatuses.SETTLEMENT }],
      },
      {
        id: '3',
        status: 'COMPLETED',
        details: [{ id: 'd4', status: TransactionStatuses.DRAFT }],
      },
    ] as any;

    const result =
      TransactionTableHelper.getNonSettledTransactions(transactions);

    expect(result).toEqual([
      {
        id: '1',
        status: TransactionStatuses.DRAFT,
        details: [{ id: 'd1', status: TransactionStatuses.DRAFT }],
      },
      {
        id: '3',
        status: 'COMPLETED',
        details: [{ id: 'd4', status: TransactionStatuses.DRAFT }],
      },
    ]);
  });

  it('Should return an empty array if all transactions are settled', () => {
    const transactions = [
      {
        id: '1',
        status: TransactionStatuses.SETTLEMENT,
        details: [{ id: 'd1', status: TransactionStatuses.SETTLEMENT }],
      },
      {
        id: '2',
        status: TransactionStatuses.SETTLEMENT,
        details: [{ id: 'd2', status: TransactionStatuses.SETTLEMENT }],
      },
    ] as any;

    const result =
      TransactionTableHelper.getNonSettledTransactions(transactions);

    expect(result).toEqual([]);
  });

  it('Should return transactions with filtered details if some details are settled', () => {
    const transactions = [
      {
        id: '1',
        status: TransactionStatuses.DRAFT,
        details: [
          { id: 'd1', status: TransactionStatuses.SETTLEMENT },
          { id: 'd2', status: TransactionStatuses.DRAFT },
        ],
      },
    ] as any;

    const result =
      TransactionTableHelper.getNonSettledTransactions(transactions);

    expect(result).toEqual([
      {
        id: '1',
        status: TransactionStatuses.DRAFT,
        details: [{ id: 'd2', status: TransactionStatuses.DRAFT }],
      },
    ]);
  });

  it('Should handle an empty transactions list gracefully', () => {
    const transactions: any[] = [];

    const result =
      TransactionTableHelper.getNonSettledTransactions(transactions);

    expect(result).toEqual([]);
  });

  it('Should return transactions unchanged if none are settled', () => {
    const transactions = [
      {
        id: '1',
        status: TransactionStatuses.DRAFT,
        details: [{ id: 'd1', status: TransactionStatuses.DRAFT }],
      },
      {
        id: '2',
        status: 'COMPLETED',
        details: [{ id: 'd2', status: 'COMPLETED' }],
      },
    ] as any;

    const result =
      TransactionTableHelper.getNonSettledTransactions(transactions);

    expect(result).toEqual(transactions);
  });
});
describe('addNewTransactionDetail', () => {
  it('Should add a new transaction detail to the specified transaction', () => {
    const transactions = [
      {
        id: '1',
        details: [{ id: 'd1' }],
      },
      {
        id: '2',
        details: [{ id: 'd2' }],
      },
    ];

    const newTransactionDetail = { id: 'd3' };

    const result = TransactionTableHelper.addNewTransactionDetail({
      transactions: transactions as Transaction[],
      transactionId: '1',
      newTransactionDetail: newTransactionDetail as TransactionDetail,
    });

    expect(result).toEqual([
      {
        id: '1',
        details: [{ id: 'd1' }, { id: 'd3' }],
      },
      {
        id: '2',
        details: [{ id: 'd2' }],
      },
    ]);
  });

  it('Should not modify transactions if the transaction ID is not found', () => {
    const transactions = [
      {
        id: '1',
        details: [{ id: 'd1' }],
      },
      {
        id: '2',
        details: [{ id: 'd2' }],
      },
    ];

    const newTransactionDetail = { id: 'd3' };

    const result = TransactionTableHelper.addNewTransactionDetail({
      transactions: transactions as Transaction[],
      transactionId: '3',
      newTransactionDetail: newTransactionDetail as TransactionDetail,
    });

    expect(result).toEqual(transactions);
  });

  it('Should return a new transactions array and not mutate the original input', () => {
    const transactions = [
      {
        id: '1',
        details: [{ id: 'd1' }],
      },
    ];

    const newTransactionDetail = { id: 'd2' };

    const result = TransactionTableHelper.addNewTransactionDetail({
      transactions: transactions as Transaction[],
      transactionId: '1',
      newTransactionDetail: newTransactionDetail as TransactionDetail,
    });

    expect(result).not.toBe(transactions);
    expect(transactions[0].details).toHaveLength(2);
  });

  it('Should handle an empty transactions list gracefully', () => {
    const transactions: Transaction[] = [];

    const newTransactionDetail = { id: 'd1' };

    const result = TransactionTableHelper.addNewTransactionDetail({
      transactions,
      transactionId: '1',
      newTransactionDetail: newTransactionDetail as TransactionDetail,
    });

    expect(result).toEqual([]);
  });
});
describe('updateTransactionDetails', () => {
  it('Should update the specified transaction detail in the given transaction', () => {
    const transactions = [
      {
        id: '1',
        details: [
          { id: 'd1', name: 'Detail 1' },
          { id: 'd2', name: 'Detail 2' },
        ],
      },
      {
        id: '2',
        details: [{ id: 'd3', name: 'Detail 3' }],
      },
    ];

    const result = TransactionTableHelper.updateTransactionDetails({
      transactions: transactions as any as Transaction[],
      transactionId: '1',
      transactionDetailTarget: { id: 'd1', name: 'Updated Detail 1' } as any,
    });

    expect(result.transactionsUpdated).toEqual([
      {
        id: '1',
        details: [
          { id: 'd1', name: 'Updated Detail 1' },
          { id: 'd2', name: 'Detail 2' },
        ],
      },
      {
        id: '2',
        details: [{ id: 'd3', name: 'Detail 3' }],
      },
    ]);
    expect(result.updatedRows).toEqual([{ transactionId: '1' }]);
  });

  it.only('Should update the transaction amout with a sum of transaction details amount', () => {
    const transactions = [
      {
        id: '1',
        amount: '150',
        details: [
          { id: 'd1.1', name: 'Detail 1', amount: '100' },
          { id: 'd1.2', name: 'Detail 2', amount: '50' },
        ],
      },
      {
        id: '2',
        details: [{ id: 'd2.1', name: 'Detail 2', amount: '30' }],
      },
    ];

    const transactionDetailTarget = {
      id: 'd1.1',
      name: 'Updated Detail 1',
      amount: '505',
    } as any;

    const result = TransactionTableHelper.updateTransactionDetails({
      transactions: transactions as any as Transaction[],
      transactionId: '1',
      transactionDetailTarget,
    });

    expect(result.transactionsUpdated).toEqual([
      {
        id: '1',
        amount: '555',
        details: [
          { id: 'd1.1', name: 'Updated Detail 1', amount: '505' },
          { id: 'd1.2', name: 'Detail 2', amount: '50' },
        ],
      },
      transactions[1],
    ]);
    expect(result.updatedRows).toEqual([{ transactionId: '1' }]);
  });

  it('Should not modify transactions if the transaction ID is not found', () => {
    const transactions = [
      {
        id: '1',
        details: [{ id: 'd1', name: 'Detail 1' }],
      },
    ];

    const result = TransactionTableHelper.updateTransactionDetails({
      transactions: transactions as any as Transaction[],
      transactionId: '2',
      transactionDetailTarget: { id: 'd1', name: 'Updated Detail 1' } as any,
    });

    expect(result.transactionsUpdated).toEqual(transactions);
    expect(result.updatedRows).toEqual([]);
  });

  it('Should not modify transactions if the transaction detail ID is not found', () => {
    const transactions = [
      {
        id: '1',
        details: [{ id: 'd1', name: 'Detail 1' }],
      },
    ];

    const result = TransactionTableHelper.updateTransactionDetails({
      transactions: transactions as any as Transaction[],
      transactionId: '1',
      transactionDetailTarget: { id: 'd2', name: 'Updated Detail 2' } as any,
    });

    expect(result.transactionsUpdated).toEqual(transactions);
    expect(result.updatedRows).toEqual([]);
  });

  it('Should return a new transactions array and not mutate the original input', () => {
    const transactions = [
      {
        id: '1',
        details: [{ id: 'd1', name: 'Detail 1' }],
      },
    ];

    const result = TransactionTableHelper.updateTransactionDetails({
      transactions: transactions as any as Transaction[],
      transactionId: '1',
      transactionDetailTarget: { id: 'd1', name: 'Updated Detail 1' } as any,
    });

    expect(result.transactionsUpdated).not.toBe(transactions);
    expect(transactions[0].details[0].name).toBe('Detail 1');
  });

  it('Should handle an empty transactions list gracefully', () => {
    const transactions: Transaction[] = [];

    const result = TransactionTableHelper.updateTransactionDetails({
      transactions,
      transactionId: '1',
      transactionDetailTarget: { id: 'd1', name: 'Updated Detail 1' } as any,
    });

    expect(result.transactionsUpdated).toEqual([]);
    expect(result.updatedRows).toEqual([]);
  });
});
describe('updateTransaction', () => {
  it('Should update the specified transaction with the provided updates', () => {
    const transactions = [
      { id: '1', name: 'Transaction 1', status: 'DRAFT' },
      { id: '2', name: 'Transaction 2', status: 'COMPLETED' },
    ];

    const result = TransactionTableHelper.updateTransaction({
      transactions: transactions as any as Transaction[],
      transactionTarget: { id: '1', name: 'Updated Transaction 1' } as any,
    });

    expect(result.updatedTransactions).toEqual([
      { id: '1', name: 'Updated Transaction 1', status: 'DRAFT' },
      { id: '2', name: 'Transaction 2', status: 'COMPLETED' },
    ]);
    expect(result.updatedRows).toEqual([{ transactionId: '1' }]);
  });

  it('Should not modify transactions if the transaction ID is not found', () => {
    const transactions = [
      { id: '1', name: 'Transaction 1', status: 'DRAFT' },
      { id: '2', name: 'Transaction 2', status: 'COMPLETED' },
    ];

    const result = TransactionTableHelper.updateTransaction({
      transactions: transactions as any as Transaction[],
      transactionTarget: { id: '3', name: 'Non-existent Transaction' } as any,
    });

    expect(result.updatedTransactions).toEqual(transactions);
    expect(result.updatedRows).toEqual([]);
  });

  it('Should return a new transactions array and not mutate the original input', () => {
    const transactions = [{ id: '1', name: 'Transaction 1', status: 'DRAFT' }];

    const result = TransactionTableHelper.updateTransaction({
      transactions: transactions as any as Transaction[],
      transactionTarget: { id: '1', name: 'Updated Transaction 1' } as any,
    });

    expect(result.updatedTransactions).not.toBe(transactions);
    expect(transactions[0].name).toBe('Transaction 1');
  });

  it('Should handle an empty transactions list gracefully', () => {
    const transactions: Transaction[] = [];

    const result = TransactionTableHelper.updateTransaction({
      transactions,
      transactionTarget: { id: '1', name: 'Updated Transaction 1' } as any,
    });

    expect(result.updatedTransactions).toEqual([]);
    expect(result.updatedRows).toEqual([]);
  });
});
