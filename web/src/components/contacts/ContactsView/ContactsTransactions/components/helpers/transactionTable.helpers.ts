import { TransactionStatuses } from 'common/globalTypes';
import { uniqueId } from 'lodash';
import BigNumber from 'bignumber.js';

import {
  Transaction,
  TransactionDetail,
  TransactionsUpdates,
} from '../../types';
import { RemovedRows, UpdatedRows } from '../TransactionTable';

const updateTransaction = (input: {
  transactions: Transaction[];
  transactionTarget: Pick<Transaction, 'id'> & Partial<Transaction>;
}) => {
  const { transactions, transactionTarget: updates } = input;

  const updatedRows: UpdatedRows = [];

  const updatedTransactions = transactions.map((transaction) => {
    if (transaction.id !== updates.id) {
      return transaction;
    }

    updatedRows.push({ transactionId: transaction.id });

    return { ...transaction, ...updates };
  });

  return { updatedTransactions, updatedRows };
};

const updateTransactionDetails = (input: {
  transactions: Transaction[];
  transactionId: string;
  transactionDetailTarget: Pick<TransactionDetail, 'id'> &
    Partial<TransactionDetail>;
}) => {
  const {
    transactions,
    transactionId,
    transactionDetailTarget: updates,
  } = input;

  const updatedRows: UpdatedRows = [];

  const transactionsUpdated = transactions.map((transaction) => {
    if (transaction.id === transactionId) {
      let totalTransactionAmount = new BigNumber(0);

      const updatedDetails = transaction.details.map((details) => {
        let updatedDetails;

        if (details.id === updates.id) {
          updatedDetails = { ...details, ...updates };

          updatedRows.push({ transactionId: transaction.id });
        }

        const newDetails = updatedDetails || details;

        totalTransactionAmount = totalTransactionAmount.plus(
          newDetails.amount || 0
        );

        return newDetails;
      });

      return {
        ...transaction,
        amount: totalTransactionAmount.toString(),
        details: updatedDetails,
      };
    }

    return transaction;
  });

  return { transactionsUpdated, updatedRows };
};

const addNewTransactionDetail = (input: {
  transactions: Transaction[];
  transactionId: string;
  newTransactionDetail: TransactionDetail;
}) => {
  const { transactions, transactionId, newTransactionDetail } = input;

  const transactionsUpdated = transactions.map((transaction) => {
    if (transaction.id === transactionId) {
      transaction.details.push(newTransactionDetail);
    }

    return transaction;
  });

  return transactionsUpdated;
};

const getNonSettledTransactions = (
  transactions: Transaction[]
): Transaction[] => {
  const filteredTransactions: Transaction[] = [];

  const isSettledTransaction = (status: TransactionStatuses) => {
    return status === TransactionStatuses.SETTLEMENT;
  };

  for (const transaction of transactions) {
    if (!isSettledTransaction(transaction.status)) {
      filteredTransactions.push({
        ...transaction,
        details: transaction.details.filter(
          (detail) => !isSettledTransaction(detail.status)
        ),
      });
    }
  }

  return filteredTransactions;
};

const getTransactionsUpdates = (input: {
  transactions: Transaction[];
  updatedRows: UpdatedRows;
  removedRows: RemovedRows;
}): TransactionsUpdates => {
  const updatedTransactions: Transaction[] = [];

  for (const updatedRow of input.updatedRows) {
    if (input.removedRows.transactions[updatedRow.transactionId]) {
      continue;
    }

    const transaction = input.transactions.find(
      ({ id }) => id === updatedRow.transactionId
    );

    if (transaction) {
      updatedTransactions.push(transaction);
    }
  }

  const deletedTransactions = Object.keys(input.removedRows.transactions).map(
    (transactionId) => ({ transactionId })
  );

  return {
    deleted: deletedTransactions,
    updated: updatedTransactions,
  };
};

const deleteTransactionById = (
  transactionId: string,
  transactions: Transaction[]
) => {
  let transactionDeleted: Transaction | undefined;

  const transactionsFiltered = transactions.filter((transaction) => {
    if (transaction.id === transactionId) {
      transactionDeleted = { ...transaction };
    }

    return transaction.id !== transactionId;
  });

  return {
    transactions: transactionsFiltered,
    transactionDeleted,
  };
};

const deleteTransactionDetail = (
  target: { transactionId: string; transactionDetailId: string },
  transactions: Transaction[]
) => {
  let transactionUpdated: Pick<Transaction, 'id'> | undefined;

  const updatedTransactions = transactions.map((transaction) => {
    const foundTransaction = target.transactionId === transaction.id;

    if (!foundTransaction) {
      return transaction;
    }

    const detailsFiltered = transaction.details.filter(
      ({ id }) => id !== target.transactionDetailId
    );

    const wasDeleted = detailsFiltered.length < transaction.details.length;

    if (wasDeleted) {
      transactionUpdated = { id: transaction.id };
    }

    return {
      ...transaction,
      details: detailsFiltered,
    };
  });

  return {
    transactions: updatedTransactions,
    transactionUpdated,
  };
};

const buildDraftTransactionValues = (): {
  transaction: Transaction;
  transactionDetail: TransactionDetail;
} => {
  // Temporary id in web client - the final id is defined in API
  const transactionId = uniqueId();
  const transactionDetailsId = uniqueId();

  return {
    transaction: {
      id: transactionId,
      amount: '0',
      date: new Date(),
      status: TransactionStatuses.DRAFT,
      notes: '',
      details: [],
    },
    transactionDetail: {
      id: transactionDetailsId,
      amount: '0',
      date: new Date(),
      status: TransactionStatuses.DRAFT,
      tags: [],
    },
  };
};

export const TransactionTableHelper = {
  addNewTransactionDetail,
  buildDraftTransactionValues,
  deleteTransactionById,
  deleteTransactionDetail,
  getNonSettledTransactions,
  getTransactionsUpdates,
  updateTransaction,
  updateTransactionDetails,
};
