import {
  TableRow,
  Table<PERSON>ell,
  <PERSON>lapse,
  Box,
  Table,
  TableBody,
  Typography,
  Button,
} from '@mui/material';
import React from 'react';
import { TransactionStatuses } from 'common/globalTypes';
import { AccountingTransactionsType } from 'common/constants/accounting_transactions';

import { TransactionTableColumns } from './TransactionTableColumns';
import {
  RowTarged,
  TransactionTableRow,
  TransactionTableRowProps,
} from './TransactionTableRow';
import { TransactionDetail } from '../types';

type Props = {
  children?: never;

  handleAddDetailOnClick: (transactionId: string) => void;
  handleSaveRowUpdates: TransactionTableRowProps['handleSaveRowUpdates'];
  handleRowDeleted: (rowTarged: RowTarged) => void;

  isEditMode: boolean;
  isTrasactionCompReportPaymentType: boolean;
  totalTransactionDetailsNotShown: number;
  transactionDetails: TransactionDetail[];
  transactionId: string;
};

const TransactionDetailTable: React.FC<Props> = (props) => {
  const isEmptyTable = props.transactionDetails.length === 0;

  return (
    <Container key={props.transactionId}>
      {!isEmptyTable && (
        <TransactionTableColumns
          isEditMode={props.isEditMode}
          hiddenColumns={['TOTAL_AMOUNT']}
          highlightBackground
        />
      )}

      <TableBody>
        {props.transactionDetails.map((transactionDetail) => (
          <TransactionTableRow
            key={`${props.transactionId}${transactionDetail.id}`}
            handleSaveRowUpdates={props.handleSaveRowUpdates}
            handleRowDeleted={props.handleRowDeleted}
            editRowEnabled={props.isEditMode}
            transactionId={props.transactionId}
            transactionDetailId={transactionDetail.id}
            amountCellValue={transactionDetail.amount}
            amountCellLabel={'Amount'}
            amountCellDisabled={
              transactionDetail.status === TransactionStatuses.PAID
            }
            dateCellValue={transactionDetail.date}
            expandCellEnabled={false}
            expandCellIsOpen={false}
            notesCellValue={transactionDetail.notes}
            statusCellValue={transactionDetail.status}
            statusCellDisabledEdit={
              props.isTrasactionCompReportPaymentType &&
              transactionDetail.type === AccountingTransactionsType.PAYABLE
            }
            tagsCellValues={transactionDetail.tags}
            referenceCellSavedReport={transactionDetail.savedReport}
            referenceCellCommissionStrId={transactionDetail.commissionStrId}
          />
        ))}

        <FooterRow {...props} />
      </TableBody>
    </Container>
  );
};

export { TransactionDetailTable };

/**
 *
 * Private components for the TransactionDetailTable
 *
 */
const Container: React.FC<{ children: React.ReactNode; key: string }> = (
  props
) => {
  return (
    <TableCell
      key={props.key}
      style={{ paddingBottom: 0, paddingTop: 0 }}
      colSpan={6}
      sx={{
        '& .Transaction-details-cell': {
          minWidth: 120,
        },
      }}
    >
      <Collapse in={true} timeout="auto" unmountOnExit>
        <Box
          sx={{
            width: '100%',
            ml: 2,
            mt: 1,
            mb: 2,
            border: '1px solid rgba(224, 224, 224, 1)',
            borderRadius: '16px',
            overflow: 'hidden',
          }}
        >
          <Table size="small">{props.children}</Table>
        </Box>
      </Collapse>
    </TableCell>
  );
};

type ButtonMouseEvent = React.MouseEvent<HTMLButtonElement, MouseEvent>;

const FooterRow: React.FC<Props> = (props) => {
  const addDetailOnClick = (event: ButtonMouseEvent) => {
    event.stopPropagation();
    event.preventDefault();
    props.handleAddDetailOnClick(props.transactionId);
  };

  return (
    <>
      {props.totalTransactionDetailsNotShown > 0 && (
        <TableRow>
          <TableCell colSpan={6}>
            <Typography variant="body2">
              {`+ ${props.totalTransactionDetailsNotShown} more details not shown.`}
            </Typography>
          </TableCell>
        </TableRow>
      )}

      {props.isEditMode && (
        <TableRow>
          <TableCell colSpan={4}>
            <Button onClick={addDetailOnClick}>Add detail</Button>
          </TableCell>
        </TableRow>
      )}
    </>
  );
};
