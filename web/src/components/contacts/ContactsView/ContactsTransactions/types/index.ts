import { TransactionStatuses } from 'common/globalTypes';
import { AccountingTransactionsType } from 'common/constants/accounting_transactions';

export type Transaction = {
  id: string;
  strId?: string;
  amount: string;
  date?: Date;
  status: TransactionStatuses;
  type?: AccountingTransactionsType;
  notes: string;
  contactId?: string;
  savedReport?: {
    id: string;
    strId: string;
    name: string;
    isCompReportPayment?: boolean;
  };
  details: TransactionDetail[];
};

export type TransactionDetail = {
  id: string;
  strId?: string;
  commissionStrId?: string;
  savedReport?: Transaction['savedReport'];
  type?: AccountingTransactionsType;
  amount: string;
  date: Date;
  status: TransactionStatuses;
  notes?: string;
  tags?: string[];
  statementId?: string;
};

export type Pagination = {
  currentPage: number;
  previousPage?: number;
  nextPage?: number;
  lastPage?: number;
  totalPages: number;
  totalItemsPerPage: number;
  totalItems: number;
};

export type TransactionsUpdates = {
  deleted: Array<{ transactionId: string }>;
  updated: Array<Transaction>;
};

export type TransactionsPagination = Pagination & {
  transactionsPageMap: { [page: number]: Transaction[] };
};

export type TransactionsSaveUpdatesState = {
  onLoading: boolean;
  success: boolean;
  error?: {
    type: 'warning' | 'error';
    message: string;
  };
};

export type OnSubmitResult = {
  totalDeleted: number;
  totalNewItemsAdded: number;
};
