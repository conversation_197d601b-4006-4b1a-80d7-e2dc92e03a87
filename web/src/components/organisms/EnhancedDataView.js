import { Add, Download, FilterAltOffOutlined } from '@mui/icons-material';
import ChevronLeftIcon from '@mui/icons-material/ChevronLeft';
import ChevronRightIcon from '@mui/icons-material/ChevronRight';
import ViewWeekOutlinedIcon from '@mui/icons-material/ViewWeekOutlined';
import {
  Box,
  Button,
  Chip,
  Divider,
  IconButton,
  Link,
  MenuItem,
  Select,
  Tooltip,
  Typography,
  useMediaQuery,
} from '@mui/material';
import { useMutation, useQuery } from '@tanstack/react-query';
import { ResponseAction } from 'common/constants';
import { endpoint } from 'common/constants/table';
import { SystemRoles } from 'common/globalTypes';
import { numberOrDefault } from 'common/helpers';
import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';
import { isEqual } from 'lodash-es';
import log from 'loglevel';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { useLocation, useSearchParams } from 'react-router-dom';

import { axios } from '@/api/interceptor';
import BasicDateRangePicker from '@/common/BasicDateRangePicker';
import DataBulkAdd from '@/components/DataBulkAdd';
import DataForm from '@/components/DataForm';
import LoadingCircle from '@/components/atoms/LoadingCircle';
import BasicDatePicker from '@/components/molecules/BasicDatePicker';
import { EnhancedSelect } from '@/components/molecules/EnhancedSelect';
import EnhancedTable from '@/components/molecules/EnhancedTable';
import MoreDateFilters from '@/components/molecules/MoreDateFilters';
import MoreMenu from '@/components/molecules/MoreMenu';
import MultiSelect from '@/components/molecules/MultiSelect';
import SaveReport from '@/components/molecules/SaveReport';
import SearchBox from '@/components/molecules/SearchBox';
import SearchSettings from '@/components/molecules/SearchSettings';
import SplitButton from '@/components/molecules/SplitButton';
import { LOCAL_STORAGE_KEYS } from '@/constants/account';
import useSnackbar from '@/contexts/useSnackbar';
import { auth } from '@/firebase';
import { useUserInfo } from '@/hooks/useUserInfo';
import API from '@/services/API';
import Formatter from '@/services/Formatter';
import { exportToCsv, formatSearchDateParams } from '@/services/helpers';
import { isNill } from '@/services/tools';
import { useMenuStore } from '@/store';
import useAccountStore from '@/store/accountStore';
import { DocumentPreviewKeys } from '@/types';
import { getControlledPagination } from '@/utils/getControlledPagination';
import { useDefaultSortingFromQuery } from './useDefaultSortingFromQuery';

dayjs.extend(utc);

const internalDefaultSorting = {
  order: 'desc',
  orderBy: 'created_at',
};

/**
 * EnhancedDataView component provides a highly configurable and dynamic data table view
 * with support for filtering, sorting, pagination, bulk actions, and more.
 *
 * @param {Object} props - The properties object.
 * @param {Object} props.dataSpec - Specification for the data table, including fields, labels, and query configurations.
 * @param {boolean} [props.bulkAdd=false] - Enables bulk add functionality.
 * @param {boolean} [props.enableBulkEditCsv=false] - Enables bulk edit functionality.
 * @param {Function} [props.setSelectedData=(a) => {}] - Callback to set selected data rows.
 * @param {Object} [props.options={ mode: 'default' }] - Configuration options for the table view.
 * @param {Function} [props.prefilter=undefined] - Function to prefilter the data.
 * @param {Array} [props.suggested=undefined] - Array of suggested data items.
 * @param {Object|null} [props.filters=null] - Custom filters for the data table.
 * @param {boolean} [props.hideAdd=false] - Hides the add button.
 * @param {boolean} [props.hideExport=false] - Hides the export button.
 * @param {boolean} [props.hideSelectedCount=false] - Hides the selected count indicator.
 * @param {boolean} [props.enableSaves=false] - Enables saving reports functionality.
 * @param {Array} [props.exportOptions=[]] - Options for exporting data.
 * @param {Object} [props.defaultData={}] - Default data for the table.
 * @param {string|null} [props.reportId=null] - Report ID for the data table.
 * @param {boolean} [props.showTotals=false] - Displays totals in the table.
 * @param {boolean} [props.readOnly=false] - Makes the table read-only.
 * @param {string} [props.rowKey=''] - Key to uniquely identify rows.
 * @param {Function} [props.actionsEnabled=() => false] - Function to enable actions for rows.
 * @param {any} [props.actions=[]] - Actions available for rows in the table.
 * @param {Array} [props.outstandingMobileFields=[]] - Fields to highlight in mobile view.
 * @param {boolean} [props.enableMultiSelect=true] - Enables multi-select functionality.
 * @param {boolean} [props.customHeaderActions=false] - Enables custom header actions.
 * @param {boolean} [props.enableEdit=true] - Enables edit functionality for rows.
 * @param {boolean} [props.enableResetFilters=true] - Enables reset filters functionality.
 * @param {boolean} [props.nonSelectableOnMobile=false] - Disables selection on mobile devices.
 * @param {Function} [props.onBulkSync=undefined] - Callback for bulk synchronization.
 * @param {Array} [props.bulkActions=[]] - Bulk actions available for the table.
 * @param {Array} [props.extraActions=[]] - Extra actions available for the table.
 * @param {string} [props.variant=''] - Variant of the table view.
 * @param {Array} [props.extraFormActions=[]] - Extra actions for the form.
 * @param {Function} [props.onQueryKeyChange=(_queryKey) => {}] - Callback for query key changes.
 * @param {Array} [props.notUpdateFields=[]] - Fields that should not be updated.
 * @param {boolean} [props.enableBulkDelete=true] - Enables bulk delete functionality.
 * @param {Object} [props.tableConfig={}] - Configuration for the table.
 * @param {Object} [props.extraBatchEditFields={}] - Extra fields for bulk edit functionality.
 * @returns {JSX.Element} The EnhancedDataView component.
 */
const EnhancedDataView = ({
  dataSpec,
  bulkAdd = false,
  enableBulkEditCsv = false,
  setSelectedData = (a) => {},
  options = { mode: 'default' },
  prefilter = undefined,
  suggested = undefined,
  filters = null,
  hideAdd = false,
  hideExport = false,
  hideSelectedCount = false,
  enableSaves = false,
  exportOptions = [],
  defaultData = {},
  reportId = null,
  showTotals = false,
  readOnly = false,
  rowKey = '',
  actionsEnabled = () => false,
  actions = [],
  outstandingMobileFields = [],
  enableMultiSelect = true,
  customHeaderActions = false,
  enableEdit = true,
  enableResetFilters = true,
  nonSelectableOnMobile = false,
  onBulkSync = undefined,
  bulkActions = [],
  extraActions = [],
  variant = '',
  extraFormActions = [],
  onQueryKeyChange = (_queryKey) => {},
  notUpdateFields = [],
  enableBulkDelete = true,
  tableConfig = {},
}) => {
  const [addBtnLabel, setAddBtnLabel] = useState(null);
  const [dataDescSelect, setDataDescSelect] = useState(null);
  const [hideAddSelect, setHideAddSelect] = useState(hideAdd);
  const { menuOpen } = useMenuStore();
  const isMobile = useMediaQuery('(max-width:600px)');
  const location = useLocation();
  const [searchParams, setSearchParams] = useSearchParams();
  const prevSearchParams = useRef(searchParams.toString());
  const mode = searchParams.get('m') ?? 'list';
  const rowsPerPage = numberOrDefault(searchParams.get('limit'), 50);
  let page = numberOrDefault(searchParams.get('page'), 0);
  const startDate = searchParams.get('start_date') ?? null;
  const endDate = searchParams.get('end_date') ?? null;
  const [newData, setNewData] = useState(defaultData || {});
  const [oldData, setOldData] = useState(defaultData || {});
  const [existingFieldOptions, setExistingFieldOptions] = useState({});
  const [availableFilterValues, setAvailableFilterValues] = useState({});

  const { order: defaultOrder, orderBy: defaultOrderBy } =
    useDefaultSortingFromQuery();
  const [orderBy, setOrderBy] = useState(
    defaultOrderBy || dataSpec.defaultOrderBy || internalDefaultSorting.orderBy
  );
  const [order, setOrder] = useState(
    defaultOrder || dataSpec.defaultSort || internalDefaultSorting.order
  );

  const [filterSuggested, setFilterSuggested] = useState(true);
  const [isDownloading, setIsDownloading] = useState(false);
  const [savingReport, setSavingReport] = useState(false);
  const [initialFilterValues, setInitialFilterValues] = useState();
  const invalidValues = [null, undefined, '', 'Invalid Date'];
  const [fields, setFields] = useState(
    Object.values(dataSpec?.fields)
      .filter((f) => f.label)
      .filter((f) => !f.label?.includes('🔒'))
      .filter((f) => !f.defaultTableHidden || f.type !== 'dynamic-select') // Dynamic-select can't be filtered out or hooks rendering rules will fail
      .map((f) => f.label)
  );
  const [snapshotData, setSnapshotData] = useState({});
  const [getDataUrl, setGetDataUrl] = useState('');
  const { data: accountInfo } = API.getBasicQuery('accounts');
  const { selectedAccount } = useAccountStore();
  const { showSnackbar } = useSnackbar();

  const qcParam = searchParams.get('qc');
  const idParam = searchParams.get('id');
  const includeZeroCommissionParam =
    searchParams.get('incl_zero_commissions') === 'true';

  const reconciliationThreshold = accountInfo?.reconciliation_threshold ?? 1;

  const poster = API.getMutation(dataSpec.table, 'POST');
  const patcher = API.getMutation(dataSpec.table, 'PATCH');

  const API_PREFIX = `${process.env.REACT_APP_API}/api`;
  // TODO: should start using apiEndpoint instead of table, clear meaning
  const bulkEditUrl = `${API_PREFIX}/${dataSpec.apiEndpoint || dataSpec.table}/${dataSpec.bulkEditEndpoint || 'bulk_edit'}`;
  const bulkPatcher = useMutation({
    mutationKey: [bulkEditUrl, selectedAccount?.accountId],
    mutationFn: async ({ data, params }) => {
      return axios.patch(bulkEditUrl, data, {
        params,
        paramsSerializer: (params) => {
          const paramsString = new URLSearchParams(params).toString();
          return paramsString;
        },
      });
    },
  });

  const deleter = API.getMutation(dataSpec.table, 'DELETE');
  const posterBulkAdd = API.getMutation(`${dataSpec.table}/bulk_add`, 'POST');
  const bulkEditCsv = API.getMutation(
    `${dataSpec.table}/bulk-edit-csv`,
    'PATCH'
  );

  const { data: { fintaryAdmin } = {} } = useUserInfo();

  // === handle scroll filter ===

  const refFilterContainer = useRef();
  const refFilterLeftIcon = useRef();
  const refFilterRightIcon = useRef();
  const [isFilterScrollable, setIsFilterScrollable] = useState(false);

  const scrollFilter = (direction) => {
    const distance = 400;
    refFilterContainer.current?.scrollTo({
      left:
        refFilterContainer.current.scrollLeft +
        (direction === 'right' ? distance : -distance),
      behavior: 'smooth',
    });
  };

  useEffect(() => {
    const elFilter = refFilterContainer.current;
    const elFilterLeftIcon = refFilterLeftIcon.current;
    const elFilterRightIcon = refFilterRightIcon.current;

    const onScroll = () => {
      if (
        elFilter === null ||
        elFilterLeftIcon === null ||
        elFilterRightIcon === null
      )
        return;

      const isScrollable = elFilter.scrollWidth > elFilter.clientWidth;

      // Reset
      elFilterLeftIcon.style.display = '';
      elFilterRightIcon.style.display = '';

      if (elFilter.scrollLeft === 0) {
        elFilterLeftIcon.style.display = 'none';
      }
      if (
        Math.ceil(elFilter.scrollLeft) >=
        elFilter.scrollWidth - elFilter.clientWidth
      ) {
        elFilterRightIcon.style.display = 'none';
      }
      setIsFilterScrollable(isScrollable);
    };

    if (elFilter) elFilter.addEventListener('scroll', onScroll);

    const observer = new MutationObserver(() => {
      onScroll();
    });

    observer.observe(elFilter, { subtree: true, childList: true });

    return () => {
      if (elFilter) {
        elFilter.removeEventListener('scroll', onScroll);
        observer.disconnect();
      }
    };
  }, []);

  // === end handle scroll filter ===

  // TODO (frank.santillan): Fix commission_filters. Probably better for us to just move onto the next version of configuring these, so will just comment this out for now.
  // useEffect(() => {
  //   if (accountSettings?.commissions_filters?.length > 0) {
  //     accountSettings.commissions_filters.forEach((_filter) => {
  //       const filter = _filter.filter((item) => item);
  //       if (!filter) {
  //         console.warn('Invalid filter', filter);
  //         return;
  //       }
  //       const filterNameValue = filter.split('::');
  //       setSearchParams((prev) => {
  //         prev.set(filterNameValue[0], filterNameValue[1]);
  //         return prev;
  //       });
  //     });
  //   }
  // }, [accountSettings]);

  useEffect(() => {
    const currentSearchParams = new URLSearchParams(searchParams.toString());
    const oldSearchParams = new URLSearchParams(prevSearchParams.current);

    const inPreview =
      currentSearchParams.get('m') === DocumentPreviewKeys.PREVIEW;
    if (inPreview) {
      return;
    }

    if (currentSearchParams.get('m') !== 'edit') {
      currentSearchParams.delete('page');
      oldSearchParams.delete('page');

      if (currentSearchParams.toString() !== oldSearchParams.toString()) {
        handleChangePage('', 0);
      }
      prevSearchParams.current = currentSearchParams.toString();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [searchParams]);

  // Add cleanup function for form data
  useEffect(() => {
    return () => {
      // Clean up form data when component unmounts
      setNewData({});
      setOldData({});
    };
  }, []);

  const queryKey = useMemo(
    () => [
      selectedAccount?.accountId,
      dataSpec.table,
      page,
      rowsPerPage,
      orderBy,
      order,
      Array.from(searchParams.entries()).toString(),
    ],
    [
      selectedAccount?.accountId,
      dataSpec.table,
      page,
      rowsPerPage,
      orderBy,
      order,
      searchParams,
    ]
  );

  const updateSearchParams = (kvMap) =>
    setSearchParams((prev) => {
      Object.entries(kvMap).forEach(([k, v]) => {
        if ([undefined, null].includes(v)) {
          prev.delete(k);
        } else {
          prev.set(k, v);
        }
      });
      return prev;
    });

  const {
    isLoading,
    isError,
    data: queryData,
    refetch,
    error,
  } = useQuery({
    queryKey,
    queryFn: async () => {
      let queryParams = `?page=${page}&limit=${rowsPerPage}&orderBy=${orderBy}&sort=${order}`;
      if (reportId) {
        queryParams += `&comp_report_id=${reportId}`;
      }

      // Filter out limit, page, orderBy, and sort from prevSearchParams to avoid duplication
      const searchParamsObj = new URLSearchParams(prevSearchParams.current);
      searchParamsObj.delete('limit');
      searchParamsObj.delete('page');
      searchParamsObj.delete('orderBy');
      searchParamsObj.delete('sort');

      let additionalQueryParams = searchParamsObj.toString()
        ? `&${searchParamsObj.toString()}`
        : '';

      if (dataSpec.queryChips && qcParam) {
        const chipQuery = dataSpec.queryChips[qcParam]?.query ?? {};
        Object.entries(chipQuery).forEach(([k, v]) => {
          if (v instanceof Array) {
            v.forEach((e) => {
              additionalQueryParams += `&${k}=${encodeURIComponent(e)}`;
            });
          } else {
            additionalQueryParams += `&${k}=${encodeURIComponent(v)}`;
          }
        });
      }

      const url = `${process.env.REACT_APP_API}/api/${dataSpec.table}${queryParams}${additionalQueryParams}`;
      setGetDataUrl(url);
      const res = await fetch(url, {
        method: 'GET',
        headers: await API.getHeaders(),
      });
      const data = await res.json();
      if (res.status === 401 && data.action === ResponseAction.LOG_OUT) {
        window.dispatchEvent(new Event('sessionExpired'));
        return;
      }
      if (data.success === false) {
        throw new Error(data.message);
      }
      return data;
    },
    enabled:
      !!auth?.currentUser ||
      !!localStorage.getItem(LOCAL_STORAGE_KEYS.ssoToken),
  });

  useEffect(() => {
    onQueryKeyChange(queryKey);
  }, [queryKey, onQueryKeyChange]);

  useEffect(() => {
    if (isError) {
      showSnackbar(
        error?.message || 'Error retrieving data, please try again later',
        'error'
      );
    }
  }, [error, isError, showSnackbar]);

  // UseEffect(() => {
  //   setInitialFilterValues(null);
  // }, [queryKey]);

  const data = useMemo(() => {
    if (Array.isArray(queryData)) {
      return queryData;
    }
    if (Array.isArray(queryData?.data)) {
      return queryData.data;
    }
    return [];
  }, [queryData]);

  const count = useMemo(() => queryData?.count ?? 0, [queryData?.count]);
  const fieldOptions = useMemo(
    () => (filters || queryData?.fieldOptions) ?? {},
    [filters, queryData?.fieldOptions]
  );
  const totals = useMemo(() => queryData?.totals ?? {}, [queryData?.totals]);
  useEffect(() => {
    if (
      mode === 'edit' &&
      idParam &&
      Array.isArray(data) &&
      data?.length === 1
    ) {
      setNewData(data[0]);
      setOldData(JSON.parse(JSON.stringify(data[0])));
    }
  }, [data, mode, idParam]);

  const setFieldsStorage = (newFields) => {
    setFields(newFields);
    if (Array.isArray(newFields) && newFields?.length > 0) {
      localStorage.setItem(`ui${location.pathname}`, newFields.join(','));
    }
  };

  useEffect(() => {
    if (isLoading || isEqual(fieldOptions, existingFieldOptions)) {
      return;
    }
    setExistingFieldOptions(fieldOptions);
    const availableVals = {};
    const filteredVals = {};
    Object.entries(fieldOptions).forEach(([k, v]) => {
      if (k.endsWith('_date_start')) {
        availableVals[k] = {
          label: 'Start date',
          type: 'date',
          field: k.replace('_start', ''),
          value: v,
        };
        filteredVals[k] = v;
      } else if (k.endsWith('_date_end')) {
        availableVals[k] = {
          label: 'End date',
          type: 'date',
          field: k.replace('_end', ''),
          value: v,
        };
        filteredVals[k] = v;
      } else if (Array.isArray(v) && v.length) {
        availableVals[k] = { label: k, type: 'multiSelect', options: v };
        filteredVals[k] = Array.isArray(v) ? v : [];
      } else if (k.startsWith('payment_date')) {
        // Do nothing
      } else {
        console.warn('Unexpected field option', k, v);
      }
    });
    setAvailableFilterValues(availableVals);
    // SetFilteredValues(filteredVals);
    // if (!initialFilterValues) {
    setInitialFilterValues(filteredVals);
    // }
  }, [dataSpec.table, existingFieldOptions, fieldOptions, isLoading]);

  const searchParamsToString = searchParams.toString();

  /**
   * Download CSV
   */
  const downloadCsvFn = useCallback(
    async (options = {}) => {
      const idToken = await auth.currentUser?.getIdToken(true);
      let additionalQueryParams = `&${prevSearchParams.current}`;

      if (dataSpec.queryChips && qcParam) {
        const chipQuery = dataSpec.queryChips[qcParam]?.query ?? {};
        Object.entries(chipQuery).forEach(([k, v]) => {
          if (v instanceof Array) {
            v.forEach((e) => {
              additionalQueryParams += `&${k}=${encodeURIComponent(e)}`;
            });
          } else {
            additionalQueryParams += `&${k}=${encodeURIComponent(v)}`;
          }
        });
      }

      const tempQuery = {};
      Object.entries(availableFilterValues).forEach(([k, v]) => {
        if (
          v.type === 'multiSelect' &&
          searchParams.getAll(k).length > 0 &&
          // V.options.length !== filteredValues[k].length
          v.options.length !== searchParams.getAll(k).length
        ) {
          tempQuery[k] = searchParams.getAll(k);
        }

        if (v.type === 'date' && searchParams.get(k)) {
          tempQuery[k] = new Date(
            encodeURIComponent(
              new Date(searchParams.get(k)).toISOString().substring(0, 10)
            )
          );
        }
      });

      // Special case for ReconciliationView special casing reconciled status
      // TODO: These should be read directly from Reconciliations.js. And the reconciled statuses should be converted to enums.
      if (dataSpec.table === 'reconciliation_data') {
        tempQuery.reconciled =
          dataSpec.queryChips[qcParam ?? 'all'].query.reconciled;
      }

      if (Object.entries(options).length > 0) {
        Object.entries(options).forEach(([k, v]) => {
          tempQuery[k] = v;
        });
      }
      // TODO: Handle dupes
      // if (fieldOptions.payment_date_first && fieldOptions.payment_date_last) {
      //   tempQuery.payment_date_first = new Date(
      //     encodeURIComponent(
      //       new Date(fieldOptions.payment_date_first)
      //         .toISOString()
      //         .substring(0, 10)
      //     )
      //   );
      //   tempQuery.payment_date_last = new Date(
      //     encodeURIComponent(
      //       new Date(fieldOptions.payment_date_last)
      //         .toISOString()
      //         .substring(0, 10)
      //     )
      //   );
      // }

      const searchParamsWithOption = new URLSearchParams(additionalQueryParams);

      Object.entries(options).forEach(([k, v]) => {
        searchParamsWithOption.set(k, v);
      });

      try {
        await exportToCsv(
          {
            orderBy,
            sort: order,
            extraParams: searchParamsWithOption,
            ...tempQuery,
          },
          { idToken, endpoint: dataSpec.table }
        );
      } catch (err) {
        showSnackbar(err?.message || 'Export failed', 'error');
      }
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [
      availableFilterValues,
      fieldOptions,
      orderBy,
      order,
      dataSpec.table,
      searchParamsToString,
      qcParam,
    ]
  );

  const handleDownload = useCallback(
    async (options) => {
      setIsDownloading(true);
      await downloadCsvFn(options);
      setIsDownloading(false);
    },
    [downloadCsvFn]
  );

  const updatedExportOptions = useMemo(() => {
    const dataExportOptions = exportOptions.length
      ? exportOptions
      : [
          {
            id: 'export',
            label: 'Export',
            key: 'export',
          },
        ];

    return [
      ...dataExportOptions,
      {
        id: 'export-relationships',
        label: 'Export with IDs',
        options: { is_export_relationship: true },
      },
    ].map((e) => ({
      ...e,
      onClick: () => handleDownload(e.options),
      key: e?.id || Date.now(),
    }));
  }, [exportOptions, handleDownload]);

  const handleChangePage = async (event, newPage) => {
    setSearchParams((prev) => {
      if (newPage && +newPage > 0) {
        page = newPage;
        prev.set('page', newPage);
      } else {
        prev.delete('page');
      }
      return prev;
    });
  };

  const handleChangeRowsPerPage = (e) => {
    setSearchParams((prev) => {
      prev.delete('page');
      prev.set('limit', e.target.value);
      return prev;
    });
  };

  const dataDesc = useMemo(() => {
    return {
      label: dataSpec.label,
      table: dataSpec.table,
      copyable:
        typeof dataSpec.copyable === 'function'
          ? dataSpec.copyable(newData)
          : (dataSpec.copyable ?? false),
      disableDelete: dataSpec.disableDelete ?? false,
      editable: true,
      fields: Object.entries(dataSpec.fields)
        .filter(([k, v]) => v.enabled)
        .reduce((acc, [k, v]) => [...acc, { ...v, id: k }], []),
    };
  }, [dataSpec, newData]);

  const deleteRows = async (ids) => {
    log.debug('Deleting ids: ', ids);
    await deleter.mutateAsync({ ids }).catch((err) => {
      showSnackbar(err?.message || 'Delete failed', 'error');
    });
    setTimeout(refetch, 300);
  };

  useEffect(() => {
    // Reset states initially
    setAddBtnLabel(null);
    setDataDescSelect(null);
    setHideAddSelect(hideAdd);

    const qc = searchParams.get('qc');
    const mode = searchParams.get('m');

    if (!qc) return;

    const chip = Object.values(dataSpec.queryChips).find(
      (chip) => chip.id === qc
    );
    if (!chip) return;

    const updateDataDescFields = (fields) => {
      const enabledFields = Object.entries(fields)
        .filter(([_, field]) => field.enabled)
        .map(([key, field]) => ({ ...field, id: key }));

      const updatedDataDesc = { ...dataDesc, fields: enabledFields };
      setDataDescSelect(updatedDataDesc);
    };

    // Handle chip configurations
    if (chip.showAddBtn) setHideAddSelect(!chip.showAddBtn);
    if (chip.addBtnLabel) setAddBtnLabel(chip.addBtnLabel);

    // Handle 'add' mode
    if (chip.addFields && mode === 'add') {
      const updatedNewData = Object.entries(chip.addFields)
        .filter(([_, field]) => field.value !== undefined)
        .reduce((acc, [key, field]) => {
          acc[key] =
            typeof field.value === 'function' ? field.value() : field.value;
          return acc;
        }, {});
      setNewData(updatedNewData);
      updateDataDescFields(chip.addFields);
    }

    // Handle 'edit' mode
    if (chip.editFields && mode === 'edit') {
      updateDataDescFields(chip.editFields);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [searchParams, setDataDescSelect, dataSpec?.queryChips, setHideAddSelect]);

  useEffect(() => {
    const savedFields = localStorage
      .getItem(`ui${location.pathname}`)
      ?.split(',');
    if (Array.isArray(savedFields) && savedFields.length > 0) {
      setFields(savedFields);
    } else if (Object.keys(dataSpec?.fields ?? {})?.length) {
      setFields(
        Object.values(dataSpec?.fields)
          .filter((f) => f.label)
          .filter((f) => !f.label?.includes('🔒'))
          .map((f) => f.label)
      );
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [dataSpec?.fields]);

  const headers = useMemo(() => {
    const newHeaders = Object.entries(dataSpec.fields)
      .map(([k, v]) => ({
        ...v,
        id: k,
      }))
      .filter((i) => i.enabled)
      .filter((i) => (options.mode === 'reconciler' ? i.reconciler : true))
      .filter((i) => fields.includes(i.label));

    const contacts = searchParams.getAll('contacts');

    if (contacts.length === 1) {
      const exportOption = exportOptions.find(
        (option) => option.id === 'export-producer-view'
      );
      if (exportOption) {
        exportOption.options.disabled = false;
      }
    } else {
      const exportOption = exportOptions.find(
        (option) => option.id === 'export-producer-view'
      );
      if (exportOption) {
        exportOption.options.disabled = true;
      }
    }

    // TODO: Find a better way of handling this

    // Figure out first and last day of payments, rather than the filters set in this view
    // queryData?.data?.forEach((row) => {
    //   if (row.payments) {
    //     const payments = Object.keys(row.payments);
    //     const firstPayment = payments[0];
    //     const lastPayment = payments[payments.length - 1];
    //     if (firstPayment) {
    //       const firstPaymentDate = dayjsUTC(firstPayment);
    //       if (
    //         !filteredValues?.effective_date_start ||
    //         firstPaymentDate.isBefore(filteredValues?.effective_date_start)
    //       ) {
    //         setFilteredValues({
    //           ...filteredValues,
    //           effective_date_start: firstPaymentDate.toDate(),
    //         });
    //       }
    //     }
    //     if (lastPayment) {
    //       const lastPaymentDate = dayjsUTC(lastPayment);
    //       if (
    //         !filteredValues?.effective_date_end ||
    //         lastPaymentDate.isAfter(filteredValues?.effective_date_end)
    //       ) {
    //         setFilteredValues({
    //           ...filteredValues,
    //           effective_date_end: lastPaymentDate.toDate(),
    //         });
    //       }
    //     }
    //   }
    // });
    // TODO: This is very specific for commissions. Generalize somehow outside of this component.

    if (dataSpec?.fields?.commission_amount_monthly?.enabled) {
      const startEffective = dayjs.utc(
        searchParams.get('effective_date_start')
      );
      const startPayment = dayjs.utc(fieldOptions.payment_date_first);
      const endEffective = dayjs.utc(searchParams.get('effective_date_end'));
      const endPayment = dayjs.utc(fieldOptions.payment_date_last);
      const start = startEffective.isBefore(startPayment)
        ? startEffective.startOf('month')
        : startPayment.startOf('month');
      const end = endEffective.isAfter(endPayment)
        ? endEffective.startOf('month')
        : endPayment.startOf('month');
      let maxMonths = 60; // 5 years max
      for (let i = end; i >= start; i = i.subtract(1, 'month')) {
        newHeaders.push({
          id: 'commission_amount_monthly',
          id2: i.format('MM/DD/YYYY'),
          label: i.format('MMM YYYY'),
          // Formatter: Formatter.currency,
          numeric: true,
          getter: (row) => {
            const month = i.format('MM/DD/YYYY');
            let commissionReceived =
              row.commission_amount_monthly?.[month]?.commission_amount_monthly;
            let commissionExpected = row.commission_expected_monthly?.[month];
            let commissionBalance = row.commission_balance_monthly?.[month];
            // Aggregate child values into parent
            // TODO: Unlink when showing child policies, will ahve to do with normal headers too
            if (row?.children_reconciliation_data?.length > 0) {
              let childrenCommissionReceived;
              let childrenCommissionExpected;
              let childrenCommissionBalance;
              row.children_reconciliation_data.forEach((child) => {
                if (
                  child.commission_amount_monthly?.[month]
                    ?.commission_amount_monthly
                )
                  childrenCommissionReceived =
                    +child.commission_amount_monthly?.[month]
                      ?.commission_amount_monthly +
                    (childrenCommissionReceived ?? 0);
                if (child.commission_expected_monthly?.[month])
                  childrenCommissionExpected =
                    +child.commission_expected_monthly?.[month] +
                    (childrenCommissionExpected ?? 0);
                if (child.commission_balance_monthly?.[month])
                  childrenCommissionBalance =
                    child.commission_balance_monthly?.[month] +
                    (childrenCommissionBalance ?? 0);
              });
              if (childrenCommissionReceived)
                commissionReceived =
                  childrenCommissionReceived + (commissionReceived ?? 0);
              if (childrenCommissionExpected)
                commissionExpected =
                  childrenCommissionExpected + (commissionExpected ?? 0);
              if (childrenCommissionBalance)
                commissionBalance =
                  childrenCommissionBalance + (commissionBalance ?? 0);
            }

            let result = Formatter.currency(commissionReceived);
            let commissionDiff;
            if (!isNill(commissionExpected) || !isNill(commissionReceived)) {
              // const effCommissionReceived = commissionReceived ?? 0;
              // const balance = commissionBalance;
              commissionDiff = (
                <div>
                  Due for this month: {Formatter.currency(commissionExpected)}
                  {commissionBalance < 0 &&
                    Math.abs(commissionBalance) > reconciliationThreshold && (
                      <>
                        <br />
                        <span>
                          Excess: {Formatter.currency(commissionBalance)}
                        </span>
                      </>
                    )}
                </div>
              );
            }
            if (
              commissionBalance < 0 &&
              Math.abs(commissionBalance) > reconciliationThreshold &&
              commissionReceived
            ) {
              result = (
                <Tooltip title={commissionDiff}>
                  <div className="whitespace-nowrap text-black text-right">
                    {Formatter.currency(commissionReceived)}*
                    <br />
                    <Typography variant="caption" className="invisible">
                      Bal: {Formatter.currency(commissionBalance ?? 0)}{' '}
                    </Typography>
                  </div>
                </Tooltip>
              );
            } else if (
              commissionBalance > 0 &&
              Math.abs(commissionBalance) > reconciliationThreshold
            ) {
              result = (
                <Tooltip title={commissionDiff}>
                  <div className="whitespace-nowrap text-black text-right">
                    {Formatter.currency(commissionReceived ?? 0)}
                    <br />
                    <Typography variant="caption" className="text-red-600">
                      Bal: {Formatter.currency(commissionBalance ?? 0)}{' '}
                    </Typography>
                  </div>
                </Tooltip>
              );
            }
            return (
              <div className="whitespace-nowrap text-black text-right">
                {result}
              </div>
            );
          },
        });
        maxMonths -= 1;
        if (maxMonths <= 0) break;
      }
    }
    return newHeaders;
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [
    dataSpec.fields,
    searchParamsToString,
    fieldOptions?.payment_date_first,
    fieldOptions?.payment_date_last,
    options.mode,
    fields,
  ]);

  // TODO (alan.nguyen): This is causing infinite re-renders, PTAL
  // const columns = useMemo(() => headers.map((header) => header.id), [headers]);
  // useUpdateSortingInQuery({
  //   order,
  //   orderBy,
  //   columns,
  // });

  const searchSettings = [];

  // TODO: Move these filters into dataSpec
  if (
    ['report_data', 'reconciliation_data', 'statement_data'].includes(
      dataSpec.table
    )
  ) {
    searchSettings.unshift({
      id: 'incl_dupes',
      type: 'toggle',
      label: 'Show duplicates',
      access: SystemRoles.ADMIN,
    });
    searchSettings.unshift({
      id: 'incl_linked',
      type: 'toggle',
      label: `Show linked ${dataSpec.table === 'report_data' ? 'policies' : 'commissions'}`,
    });
  }
  if (dataSpec.table === 'statement_data') {
    searchSettings.unshift({
      id: 'incl_zero_commissions',
      type: 'toggle',
      label: 'Show payouts of $0',
    });
    searchSettings.unshift({
      id: 'hide_no_payout_calc_commissions',
      type: 'toggle',
      label: 'Hide commissions without payout',
    });
    searchSettings.unshift({
      id: 'hide_payout_calc_commissions',
      type: 'toggle',
      label: 'Hide commissions with payout',
    });
    searchSettings.unshift({
      id: 'show_allocated_commissions',
      type: 'toggle',
      label: 'Show allocated master commissions',
    });
  }

  const dataAll = data;

  let dataFiltered = dataAll;

  const displayBasicRangeDate = (availableFilterValues) => {
    const filterDatesMap = Object.entries(availableFilterValues).filter(
      ([, value]) => value.type === 'date'
    );

    return filterDatesMap
      .filter(([key]) => key.includes('start'))
      .map(([startKey, startValue]) => {
        const endDateKey = startKey.replace('start', 'end');
        const endDateValue = availableFilterValues[endDateKey];

        if (!endDateValue) return null;

        return (
          <BasicDateRangePicker
            key={startKey}
            range={{
              startDate:
                searchParams.get(startKey) ||
                initialFilterValues?.[startKey] ||
                null,
              startDateLabel: startValue.label,
              toolTipStartDate: (
                <Tooltip
                  key={startValue.label}
                  title={
                    ['report_data', 'reconciliation_data'].includes(
                      dataSpec.table
                    )
                      ? 'Effective date'
                      : dataSpec.table === 'statement_data'
                        ? 'Payment date'
                        : ''
                  }
                  placement="top"
                >
                  <div></div>
                </Tooltip>
              ),
              endDate:
                searchParams.get(endDateKey) ||
                initialFilterValues?.[endDateKey] ||
                null,
              endDateLabel: endDateValue.label,
              toolTipEndDate: (
                <Tooltip
                  key={endDateValue.label}
                  title={
                    ['report_data', 'reconciliation_data'].includes(
                      dataSpec.table
                    )
                      ? 'Effective date'
                      : dataSpec.table === 'statement_data'
                        ? 'Payment date'
                        : ''
                  }
                  placement="top"
                >
                  <div></div>
                </Tooltip>
              ),
            }}
            onChange={({ startDate, endDate }) => {
              setSearchParams((prev) => {
                if (startDate === 'Invalid Date') {
                  prev.delete(startKey);
                } else {
                  prev.set(
                    startKey,
                    startDate
                      ? dayjs.isDayjs(startDate)
                        ? startDate.toString()
                        : formatSearchDateParams(startDate)
                      : null
                  );
                }
                if (endDate === 'Invalid Date') {
                  prev.delete(endDateKey);
                } else {
                  prev.set(
                    endDateKey,
                    endDate
                      ? dayjs.isDayjs(endDate)
                        ? endDate.toString()
                        : formatSearchDateParams(endDate)
                      : null
                  );
                }

                return prev;
              });
            }}
          />
        );
      })
      .filter(Boolean);
  };

  // TODO: Reconcile prefilter vs suggested, xor for now
  if (prefilter instanceof Function) {
    dataFiltered = dataAll.filter(prefilter);
  } else if (suggested && filterSuggested) {
    dataFiltered = dataAll.filter((e) => suggested.includes(e.policy_id));
  }

  useEffect(() => {
    if (headers.length > 0 && dataFiltered.length > 0)
      setSnapshotData({
        headers: headers,
      });
  }, [dataFiltered, headers]);

  const getValues = (paramKey) => {
    const paramValues = searchParams.getAll(paramKey) || [];
    // Unselect all
    if (paramValues.length === 1 && paramValues[0] === 'undefined') {
      return [];
    }

    let selected = paramValues.length
      ? paramValues
          .map((val) =>
            availableFilterValues[paramKey]?.options.find(
              (option) => String(option.id) === val
            )
          )
          .filter((item) => !!item)
      : availableFilterValues[paramKey]?.options;

    // Handle unselected_contacts
    if (paramKey === 'contacts') {
      const unselected = searchParams.getAll('unselected_contacts');
      if (Array.isArray(unselected) && unselected.length > 0) {
        selected = selected.filter(
          (item) => !unselected.includes(String(item.id))
        );
      }
    }

    return selected;
  };
  const renderAddButtons = () => {
    if (hideAddSelect) return null;

    const options = [
      {
        id: 'add',
        label: addBtnLabel || 'Add',
        onClick: () => {
          updateSearchParams({ m: 'add', id: null });
        },
      },
    ];

    if (bulkAdd) {
      options.push({
        id: 'bulkAdd',
        label: 'Bulk add from csv',
        onClick: () => updateSearchParams({ m: 'bulkAdd' }),
      });
    }
    if (enableBulkEditCsv) {
      options.push({
        id: 'bulkEditCsv',
        label: 'Bulk edit from csv',
        onClick: () => updateSearchParams({ m: 'bulkEditCsv' }),
      });
    }

    return (
      <SplitButton
        startIcon={<Add />}
        options={options}
        disabled={['add', 'edit'].includes(mode)}
        variant="contained"
      />
    );
  };

  // TODO: UI filter will be refactored soon
  // This is a short term solution to make expected filter order
  const selectFilters = useMemo(() => {
    const DEFAULT_SORT_POSITION = 1000; // If no sort position is defined, we put it at the end
    let selectFilters = Object.values(availableFilterValues)
      .filter((v) => v.type === 'multiSelect')
      .map(({ ...filter }) => {
        const realKey = filter.label;
        filter.key = realKey; // This is actually the key returned by the API
        filter.label = dataSpec.filters?.[realKey]?.label || filter.label; // This is the label shown in the UI
        filter.sortPosition =
          dataSpec.filters?.[realKey]?.sortPosition || DEFAULT_SORT_POSITION;
        return filter;
      });

    if (dataSpec.sortFilterByPosition) {
      selectFilters = selectFilters.sort((a, b) => {
        return a.sortPosition - b.sortPosition;
      });
    } else {
      selectFilters = selectFilters.sort((a, b) => {
        return a.label.localeCompare(b.label);
      });
    }
    return selectFilters;
  }, [availableFilterValues, dataSpec.filters, dataSpec.sortFilterByPosition]);

  const bulkEditAddFields = useMemo(() => {
    const extraFields =
      (mode === 'bulkEditCsv'
        ? dataSpec.extraBulkEditCsvFields
        : dataSpec.bulkAddFields) || [];
    return [
      ...extraFields,
      ...(dataDescSelect || dataDesc).fields
        .flat()
        .filter(
          (field) =>
            [undefined, 'select', 'dynamic-select', 'boolean', 'date'].includes(
              field.type
            ) &&
            field.access !== SystemRoles.ADMIN &&
            !field.readOnly &&
            !field.bulkAddUnsupported
        ),
    ];
  }, [
    mode,
    dataSpec.extraBulkEditCsvFields,
    dataSpec.bulkAddFields,
    dataDescSelect,
    dataDesc,
  ]);

  if (!dataSpec) return null;

  return (
    <Box
      sx={{
        width:
          options.mode === 'reconciler'
            ? 'calc((100vw - 200px)/2 - 120px)'
            : `calc(100vw - ${menuOpen ? '200px' : '0px'})`,
      }}
    >
      {options.mode === 'reconciler' ? (
        <Box>
          <Box
            display="flex"
            justifyContent="space-between"
            alignItems="center"
            sx={{ height: 56 }}
          >
            <Typography variant="h5">{dataSpec.labelSimple}</Typography>
            <SearchBox id={dataSpec.table} />
          </Box>
        </Box>
      ) : (
        <Box sx={{ pt: 2, position: 'relative', zIndex: 3 }}>
          <Box
            display="flex"
            justifyContent="space-between"
            alignItems="center"
            flexWrap={isMobile ? 'wrap' : 'nowrap'}
            sx={{ mb: 1, px: 2 }}
          >
            <Typography variant="h5">
              {!['tabbed'].includes(variant) && dataSpec.label}
            </Typography>
            {/* TODO: Re-enable card view */}
            {/* <IconButton
              onClick={() => setView(view === 'table' ? 'cards' : 'table')}
            >
              {view === 'table' ? <Splitscreen /> : <GridOn />}
            </IconButton> */}
            <Box sx={{ display: 'flex' }}>
              {idParam &&
                (idParam.split(',').length === 1 ? (
                  <Chip
                    label={idParam}
                    sx={{ mr: 1 }}
                    onDelete={() => {
                      setOldData({});
                      setNewData({});
                      updateSearchParams({ id: null, m: null });
                    }}
                    color="primary"
                  />
                ) : (
                  <Chip
                    label={`${idParam.split(',')[0]} and ${idParam.split(',').length - 1} others`}
                    sx={{ mr: 1 }}
                    onDelete={() => {
                      updateSearchParams({ id: null, m: null });
                    }}
                    color="primary"
                  />
                ))}
              <SearchBox id={dataSpec.table} />
              {searchSettings?.length > 0 && (
                <SearchSettings settings={searchSettings} />
              )}
            </Box>
          </Box>
          <Box
            sx={{
              display: 'flex',
              alignItems: 'center',
              flexDirection: isMobile ? 'column' : 'row',
            }}
          >
            {/* === left filters === */}
            <Box
              sx={{
                display: 'flex',
                flexGrow: 1,
                overflow: 'hidden',
                position: 'relative',
                alignItems: 'center',
                alignSelf: 'stretch',
                '&:hover': {
                  '& .navigate-icon': {
                    display: isFilterScrollable ? 'inline-flex' : 'none',
                  },
                },
              }}
            >
              {/* === navigate icon === */}
              <IconButton
                ref={refFilterLeftIcon}
                className="navigate-icon"
                onClick={() => {
                  scrollFilter('left');
                }}
                sx={{
                  display: 'none',
                  position: 'absolute',
                  left: 8,
                  zIndex: 3,
                  background: '#e3e3e3',
                  boxShadow: '-6px 0px 7px -3px rgba(0, 0, 0, 0.1)',
                  '&:hover': {
                    background: '#e3e3e3',
                  },
                }}
              >
                <ChevronLeftIcon />
              </IconButton>
              <Box
                ref={refFilterContainer}
                className="hiddenScrollbar"
                sx={{
                  flexGrow: 1,
                  display: 'flex',
                  alignItems: 'center',
                  position: 'relative',
                  alignSelf: 'stretch',
                  px: 2,
                  overflow: 'auto',
                  '&:hover': {
                    '& .navigate-icon': {
                      display: isFilterScrollable ? 'inline-flex' : 'none',
                    },
                  },
                }}
              >
                {/* === main filters === */}
                {dataSpec?.queryChips &&
                  Object.keys(dataSpec?.queryChips).length > 0 && (
                    <Box
                      sx={{
                        whiteSpace: 'wrap',
                        display: 'flex',
                        width: isMobile ? '100%' : 'auto',
                        justifyContent: 'start',
                        alignItems: 'center',
                        mb: isMobile ? 1.5 : 0,
                      }}
                    >
                      {Object.entries(dataSpec?.queryChips ?? {})
                        .filter(([k, v]) => !v.more)
                        .filter(
                          ([k, v]) =>
                            v.access !== SystemRoles.ADMIN || fintaryAdmin
                        )
                        .map(([k, chip]) => (
                          <Chip
                            label={`${chip.label}${chip.access === SystemRoles.ADMIN ? ' 🔒' : ''}`}
                            key={chip.id}
                            onClick={() => {
                              setSearchParams((prev) => {
                                if (chip.id === 'all') {
                                  prev.delete('qc');
                                } else {
                                  prev.set('qc', chip.id);
                                }
                                prev.delete('m');
                                return prev;
                              });
                            }}
                            sx={{ mr: 0.5, cursor: 'pointer' }}
                            color={
                              searchParams.get('qc') === chip.id ||
                              (!searchParams.get('qc') && chip.id === 'all')
                                ? 'primary'
                                : 'default'
                            }
                            variant={
                              searchParams.get('qc') === chip.id ||
                              (!searchParams.get('qc') && chip.id === 'all')
                                ? 'filled'
                                : 'outlined'
                            }
                          />
                        ))}
                      {Object.values(dataSpec?.queryChips ?? {}).filter(
                        (chip) => chip.more
                      ).length > 0 && (
                        <MoreMenu
                          actions={Object.values(dataSpec?.queryChips ?? {})
                            .filter((chip) => chip.more)
                            ?.map((chip) => ({
                              label: chip.label,
                              onClick: () => {
                                setSearchParams((prev) => {
                                  if (chip.id === 'all') {
                                    prev.delete('qc');
                                  } else {
                                    prev.set('qc', chip.id);
                                  }
                                  return prev;
                                });
                              },
                            }))}
                          data={null}
                          setActionLoading={() => {}}
                          sx={{ mr: 1 }}
                        />
                      )}
                    </Box>
                  )}
                <Box
                  sx={{
                    mt: 1,
                    mr: 1,
                    mb: isMobile ? 0 : 1,
                    display: 'flex',
                    flexWrap: isMobile ? 'wrap' : 'nowrap',
                    alignItems: 'center',
                  }}
                >
                  {displayBasicRangeDate(availableFilterValues)}
                  {Array.isArray(dataSpec?.dateFilters) &&
                    dataSpec?.dateFilters.length > 0 &&
                    Object.keys(availableFilterValues).length > 0 && (
                      <MoreDateFilters
                        title="Additional date filters"
                        filters={dataSpec?.dateFilters ?? []}
                        values={searchParams}
                        onSetValue={(k, e) => {
                          setSearchParams((prev) => {
                            if (invalidValues.includes(e)) prev.delete(k);
                            else prev.set(k, e);
                            return prev;
                          });
                        }}
                      />
                    )}
                </Box>
                <Box
                  sx={{
                    mt: isMobile ? 1 : 0,
                    display: 'flex',
                    flexWrap: isMobile ? 'wrap' : 'nowrap',
                    alignItems: 'center',
                    flexShrink: 0,
                    gap: 1,
                  }}
                >
                  {selectFilters.map((v) => {
                    const k = v.key;
                    const selectedValue = getValues(k);
                    const selectedValue2 = searchParams.getAll(k).length
                      ? searchParams
                          .getAll(k)
                          .filter((k) => k && k !== 'undefined')
                      : availableFilterValues[k]?.options;

                    // For handling objects as options the option must have an 'id' and a 'name' property
                    return availableFilterValues[k]?.options.every(
                      (option) => typeof option === 'object'
                    ) ? (
                      <EnhancedSelect
                        key={k}
                        enableActiveColor={
                          selectedValue.length > 0 &&
                          selectedValue.length <
                            availableFilterValues[k]?.options.length
                        }
                        sx={{ minWidth: 130, width: 'fit-content' }}
                        label={dataSpec.filters[k]?.label || k}
                        enableSearch
                        multiple
                        options={availableFilterValues[k]?.options}
                        value={selectedValue}
                        listContainerSx={dataSpec.filters[k]?.listContainerSx}
                        onChange={(values) => {
                          const ids = values.map((item) => item.id);
                          const options = availableFilterValues[k]?.options;

                          // Select all
                          if (!values.length) {
                            setSearchParams((prev) => {
                              prev.set(k, 'undefined');
                              return prev;
                            });
                          } else {
                            setSearchParams((prev) => {
                              prev.delete(k);
                              // If select all values, we remove params on the url instead
                              const deselectedValues = options.filter(
                                (o) => !values.find((v) => v.id === o.id)
                              );
                              if (ids.length !== options.length) {
                                if (
                                  k === 'contacts' &&
                                  ids.length > 50 &&
                                  deselectedValues.length > 0
                                ) {
                                  // Clear all contacts=
                                  prev.delete(k);
                                  deselectedValues.forEach((v) => {
                                    prev.append('unselected_contacts', v.id);
                                  });
                                } else {
                                  ids.forEach((v) => {
                                    prev.append(k, v);
                                  });
                                }
                              }
                              return prev;
                            });
                          }
                        }}
                      />
                    ) : (
                      <EnhancedSelect
                        key={k}
                        sx={{ minWidth: 130, width: 'fit-content' }}
                        label={dataSpec.filters[k]?.label || k}
                        enableSearch
                        multiple
                        enableActiveColor={
                          selectedValue2.length > 0 &&
                          selectedValue2.length <
                            availableFilterValues[k]?.options.length
                        }
                        options={availableFilterValues[k]?.options}
                        listContainerSx={dataSpec.filters[k]?.listContainerSx}
                        value={selectedValue2}
                        onChange={(values) => {
                          const options = availableFilterValues[k]?.options;

                          if (!values.length) {
                            setSearchParams((prev) => {
                              prev.set(k, 'undefined');
                              return prev;
                            });
                          } else {
                            setSearchParams((prev) => {
                              prev.delete(k);
                              // If select all values, we remove params on the url instead
                              if (values.length !== options.length) {
                                // Get the deselected values
                                const deselectedValues = options.filter(
                                  (o) => !values.find((v) => v.id === o.id)
                                );
                                if (
                                  k === 'contacts' &&
                                  deselectedValues.length > 0
                                ) {
                                  // Clear all contacts=
                                  prev.delete(k);
                                  deselectedValues.forEach((v) => {
                                    prev.append('unselected_contacts', v.id);
                                  });
                                } else {
                                  values.forEach((v) => {
                                    prev.append(k, v);
                                  });
                                }
                              }
                              return prev;
                            });
                          }
                        }}
                      />
                    );
                  })}
                  {Object.keys(availableFilterValues).length > 0 &&
                    enableResetFilters &&
                    searchParams.size > 0 && (
                      <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        <Tooltip title="Clear filters">
                          <IconButton
                            onClick={() => {
                              setSearchParams({});
                            }}
                            disabled={['add', 'edit'].includes(mode)}
                            sx={{ mr: 1 }}
                          >
                            <FilterAltOffOutlined />
                          </IconButton>
                        </Tooltip>
                      </Box>
                    )}
                </Box>

                {/* === navigate icon === */}
              </Box>
              <IconButton
                className="navigate-icon"
                onClick={() => {
                  scrollFilter('right');
                }}
                ref={refFilterRightIcon}
                sx={{
                  display: 'none',
                  position: 'absolute',
                  right: 8,
                  zIndex: 3,
                  background: '#e3e3e3',
                  boxShadow: '-6px 0px 7px -3px rgba(0, 0, 0, 0.1)',
                  '&:hover': {
                    background: '#e3e3e3',
                  },
                }}
              >
                <ChevronRightIcon />
              </IconButton>
            </Box>

            {/* === right sticky actions ==== */}
            <Box
              sx={{
                boxShadow: isFilterScrollable
                  ? '-6px 4px 7px -3px rgba(0, 0, 0, 0.1)'
                  : 'none',
                pr: 2,
                py: 1,
                display: 'flex',
                flexShrink: 0,
                flexWrap: isMobile ? 'wrap' : 'nowrap',
                gap: 1,
                alignItems: 'center',
                background: 'white',
                zIndex: 2,
              }}
            >
              <EnhancedSelect
                label="Fields"
                multiple
                sortLabel={false}
                options={Object.values(dataSpec?.fields)
                  .filter((f) => f.label)
                  .map((f) => f.label)}
                value={fields}
                onChange={(values) => setFieldsStorage(values)}
                sx={{ mx: 0.5, maxWidth: 140 }}
                popoverProps={{
                  anchorOrigin: {
                    vertical: 'bottom',
                    horizontal: 'right',
                  },
                  transformOrigin: {
                    vertical: 'top',
                    horizontal: 'right',
                  },
                }}
                renderField={({ onClick }) => {
                  return (
                    <Tooltip title="Show/hide fields">
                      <IconButton color="primary" onClick={onClick}>
                        <ViewWeekOutlinedIcon />
                      </IconButton>
                    </Tooltip>
                  );
                }}
              />
              {extraActions.map((action) => (
                <Box key={action.label}>
                  {action.type === 'button' && (
                    <Button
                      key={action.label}
                      onClick={action.onClick}
                      sx={{ mr: 1 }}
                      startIcon={action.icon}
                      variant={action.variant || 'outlined'}
                      disabled={action.disabled || false}
                      disableElevation
                    >
                      {action.label}
                    </Button>
                  )}
                  {action.type === 'select' && (
                    <Select
                      key={action.label}
                      value={action.value}
                      onChange={action.onChange}
                      sx={{ mr: 1 }}
                    >
                      {action.options.map((option) => (
                        <MenuItem key={option.value} value={option.value}>
                          {option.label}
                        </MenuItem>
                      ))}
                    </Select>
                  )}
                  {action.type === 'date' && (
                    <BasicDatePicker
                      key={action.label}
                      label={action.label}
                      value={action.value}
                      setValue={action.onChange}
                      sx={{ mr: 1 }}
                    />
                  )}
                  {action.type === 'dateRange' && (
                    <BasicDateRangePicker
                      key={action.label}
                      range={{
                        startDate,
                        endDate,
                        startDateLabel: action.label,
                        endDateLabel: action.label,
                      }}
                      onChange={(range) => {
                        setSearchParams((prev) => {
                          const { startDate, endDate } = range;
                          if (startDate) {
                            prev.set(
                              'start_date',
                              dayjs.isDayjs(startDate)
                                ? startDate.toString()
                                : formatSearchDateParams(startDate)
                            );
                          } else {
                            prev.delete('start_date');
                          }
                          if (endDate) {
                            prev.set(
                              'end_date',
                              dayjs.isDayjs(endDate)
                                ? endDate.toString()
                                : formatSearchDateParams(endDate)
                            );
                          } else {
                            prev.delete('end_date');
                          }
                          return prev;
                        });
                      }}
                    />
                  )}
                  {action.type === 'multiSelect' && (
                    <MultiSelect
                      key={action.label}
                      label={action.label}
                      values={action.options}
                      selectedValues={action.value}
                      setSelectedValues={action.onChange}
                      sx={{ mr: 1 }}
                    />
                  )}
                  {action.element && action.element}
                </Box>
              ))}
              {enableSaves && (
                <SaveReport
                  reportId={reportId}
                  queryData={snapshotData}
                  getDataUrl={getDataUrl}
                  table={dataSpec.table}
                  setSavingReport={setSavingReport}
                />
              )}
              {renderAddButtons()}
              {!hideExport && (
                <SplitButton
                  startIcon={<Download />}
                  useLoadingBtn
                  loading={isDownloading}
                  options={updatedExportOptions}
                />
              )}
            </Box>
          </Box>
        </Box>
      )}
      {suggested?.length > 0 && (
        <Box sx={{ textAlign: 'center' }}>
          {filterSuggested ? (
            <Typography variant="body2">
              ✨ Showing suggested matches (
              <Link
                onClick={() => setFilterSuggested(false)}
                sx={{ cursor: 'pointer' }}
              >
                see all
              </Link>
              )
            </Typography>
          ) : (
            <Typography variant="body2">
              Showing all matches (
              <Link
                onClick={() => setFilterSuggested(true)}
                sx={{ cursor: 'pointer' }}
              >
                show suggested
              </Link>
              )
            </Typography>
          )}
        </Box>
      )}

      {!isLoading &&
        ((mode === 'bulkAdd' && bulkAdd) ||
          (mode === 'bulkEditCsv' && enableBulkEditCsv)) && (
          <Box
            sx={{
              overflowY: 'scroll',
              height: 'calc(100vh - 168px)',
              display: 'flex',
              justifyContent: 'center',
              p: 2,
            }}
          >
            <DataBulkAdd
              fields={bulkEditAddFields}
              btnLabel={mode === 'bulkAdd' ? 'Bulk add' : 'Bulk edit'}
              isUpdate={mode === 'bulkEditCsv'}
              onCancel={() => {
                updateSearchParams({ m: null });
              }}
              onSave={async (jsonEntities) => {
                let res = null;
                if (mode === 'bulkAdd') {
                  res = await posterBulkAdd.mutateAsync(jsonEntities);
                } else {
                  res = await bulkEditCsv.mutateAsync(jsonEntities);
                }

                if (res?.total) {
                  showSnackbar(`Added ${res.total} records`, 'success');
                  updateSearchParams({ m: null });
                  setTimeout(refetch, 200);
                } else {
                  showSnackbar('Error bulk adding data', 'error');
                }
              }}
            />
          </Box>
        )}

      {!isLoading && ['add', 'edit'].includes(mode) && (
        <Box
          sx={{
            overflowY: 'scroll',
            height: 'calc(100vh - 168px)',
            display: 'flex',
            justifyContent: 'center',
            p: 2,
          }}
        >
          <DataForm
            dataDesc={dataDescSelect || dataDesc}
            fields={Object.values((dataDescSelect || dataDesc).fields).filter(
              (field) => (field.condition ? field.condition(newData) : true)
            )}
            useNewTable={dataSpec.useNewTable}
            dynamicSelectsConfig={dataSpec.dynamicSelectsConfig}
            newData={newData}
            readOnly={readOnly}
            oldData={oldData}
            setNewData={setNewData}
            onCancel={() => {
              setNewData({});
              updateSearchParams({ m: null, id: null });
            }}
            onSave={async () => {
              // In general, don't send read-only fields. But some are necessary for identifying records to update.
              // For general forms, id and str_id, and for Views and fields and Agent settings, key, role is needed.
              const normalizedNewData = Object.fromEntries(
                Object.entries(newData)
                  .filter(
                    ([key]) =>
                      ['id', 'str_id', 'role', 'key'].includes(key) ||
                      (typeof dataDesc.fields.find((field) => field.id === key)
                        ?.readOnly === 'function'
                        ? !dataDesc.fields
                            .find((field) => field.id === key)
                            ?.readOnly(newData)
                        : !dataDesc.fields.find((field) => field.id === key)
                            ?.readOnly)
                  )
                  .map(([key, value]) => [
                    key,
                    dataDesc.fields.find((field) => field.id === key)
                      ?.normalizer
                      ? dataDesc.fields
                          .find((field) => field.id === key)
                          .normalizer(value, [])
                      : value,
                  ])
              );
              if (includeZeroCommissionParam)
                normalizedNewData.incl_zero_commissions =
                  includeZeroCommissionParam;

              let res;

              if (notUpdateFields.length > 0) {
                notUpdateFields.forEach((field) => {
                  delete normalizedNewData[field];
                });
              }

              try {
                if (
                  normalizedNewData.id &&
                  dataDesc.table !== endpoint.viewsAndFields &&
                  dataDesc.table !== endpoint.agents
                ) {
                  if (reportId) normalizedNewData.comp_report_id = reportId;
                  res = await patcher.mutateAsync(normalizedNewData);
                } else {
                  res = await poster.mutateAsync(normalizedNewData);
                }
                setNewData({});
                updateSearchParams({ m: null, id: null });
              } catch (e) {
                console.error('Error encountered while saving data');
                return {
                  error: e.message || 'Error encountered while saving data',
                };
              } finally {
                setTimeout(refetch, 300);
              }
              return res;
            }}
            onDelete={async () => {
              await deleter.mutate({ ids: [newData.id] });

              setNewData({});
              updateSearchParams({ m: null, id: null });
              setTimeout(refetch, 300);
            }}
            validateData={dataDesc.validateData}
            extraActions={extraFormActions}
            currentData={newData}
          />
        </Box>
      )}

      <Divider />
      {(isLoading || savingReport) && (
        <Box sx={{ textAlign: 'center', mt: 6 }}>
          <LoadingCircle />
        </Box>
      )}
      {!isLoading &&
        !['edit', 'bulkAdd', 'add'].includes(mode) &&
        dataFiltered.length > 0 &&
        headers.length > 0 && (
          <EnhancedTable
            dense
            bulkEditFields={dataSpec.bulkEditFields}
            dynamicQueryKeys={dataSpec.dynamicQueryKeys}
            dynamicSelectsConfig={dataSpec.dynamicSelectsConfig}
            useNewTable={dataSpec.useNewTable}
            headers={headers}
            stickyColumns={dataSpec.stickyColumns}
            rows={dataFiltered}
            readOnly={readOnly}
            actions={actions}
            outstandingFieldsInMobileView={
              outstandingMobileFields?.length > 0
                ? outstandingMobileFields
                : (dataSpec.outstandingFieldsInMobileView ?? [])
            }
            actionsEnabled={actionsEnabled}
            customHeaderActions={customHeaderActions}
            enableBulkDelete={enableBulkDelete}
            onDelete={enableMultiSelect ? deleteRows : false}
            nonSelectableOnMobile={nonSelectableOnMobile}
            rowKey={rowKey}
            onBulkSync={onBulkSync}
            bulkActions={bulkActions}
            onEdit={
              enableEdit
                ? options.mode !== 'reconciler'
                  ? (row) => {
                      updateSearchParams({
                        m: 'edit',
                        id: row.str_id,
                      });
                      setNewData(row);
                      setOldData(JSON.parse(JSON.stringify(row)));
                    }
                  : undefined
                : false
            }
            onBulkEdit={async (selected, updateData) => {
              const { clearDataFields, ...rest } = updateData;
              bulkPatcher.mutate(
                {
                  data: {
                    ids: selected,
                    ...rest,
                  },
                  params: {
                    clearDataFields,
                  },
                },
                {
                  onError: (error) => {
                    showSnackbar(
                      error.message ||
                        'Something wrong happened, please try again!',
                      'error'
                    );
                  },
                  onSuccess: () => {
                    showSnackbar('Bulk edit successful', 'success');
                    refetch();
                  },
                }
              );
            }}
            // TODO: Should be controlled selection...hack for now
            setSelectedData={setSelectedData}
            stickyHeader
            paginated={tableConfig?.paginated ?? true}
            controlledOrdering={{
              order,
              orderBy,
              setOrder: (e) => {
                handleChangePage(e, 0);
                setOrder(e);
              },
              setOrderBy: (newOrderBy) => {
                if (!newOrderBy) {
                  console.warn(
                    'Invalid orderBy value. Falling back to default.'
                  );
                  newOrderBy = orderBy;
                }
                handleChangePage(null, 0); // Reset to the first page
                setOrderBy(newOrderBy);
              },
            }}
            // If controlledPagination is not defined, use the default pagination
            // controlledPagination null means no pagination
            controlledPagination={getControlledPagination(
              tableConfig?.controlledPagination,
              {
                count, // +(filterVals.showDupes ? dataCounts.countDupes : 0),
                page,
                onPageChange: handleChangePage,
                rowsPerPage,
                onRowsPerPageChange: handleChangeRowsPerPage,
              }
            )}
            options={{ hideSelectedCount, radio: options.radio }}
            showTotals={showTotals}
            totals={totals}
            refetch={refetch}
          />
        )}
      {!isLoading && dataFiltered.length === 0 && (
        <Box sx={{ textAlign: 'center', mt: 6, width: '100%' }}>
          {isError ? (
            <Box>
              <Typography variant="h5">
                Error retrieving data, please try again later
              </Typography>
              <Button
                variant="outlined"
                onClick={(_e) => refetch()}
                sx={{ mt: 5 }}
              >
                Retry
              </Button>
            </Box>
          ) : (
            <Typography variant="h5">No results</Typography>
          )}
        </Box>
      )}
    </Box>
  );
};

export default EnhancedDataView;
