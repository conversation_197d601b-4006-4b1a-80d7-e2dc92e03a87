import CommonFormatter from 'common/Formatter';

import DataView from '@/components/DataView';
import LazyLoadDynamicSelect from '@/components/molecules/LazyLoadDynamicSelect';
import CompProfilesAdd from '@/components/schedules/CompProfilesView/CompProfilesAdd';
import API from '@/services/API';
import { useAccountStore, useRoleStore } from '@/store';
import { Roles } from '@/types';

const CompProfileSetsView = () => {
  const { userRole } = useRoleStore();
  const { selectedAccount } = useAccountStore();
  const { data: accountSettings } = API.getBasicQuery(`accounts/settings`);

  const viewSettings = accountSettings?.pages_settings?.carriers_schedules;
  const viewOnly = viewSettings?.read_only ?? false;

  const dataDesc = {
    label: 'Compensation profile sets',
    table: 'schedules/comp-profile-sets',
    editable: userRole === Roles.ACCOUNT_ADMIN,
    fields: [
      { id: 'name', label: 'Name' },
      { id: 'notes', label: 'Notes' },
      {
        id: 'commission_profiles',
        label: 'Comp profiles',
        disableSort: true,
        table: 'schedules/comp-profiles',
        field: 'id',
        multiple: true,
        formatter: (val, collectionVals = []) => {
          if (val === '') return '';
          if (Array.isArray(collectionVals) && collectionVals.length > 0) {
            const record: any = collectionVals?.filter(
              (datum: any) => datum.id === val
            )?.[0];
            return record?.name ?? '';
          }
          return val;
        },
        optionValuer: (option) => option?.id,
        optionFormatter: (option) => option?.name,
        type: 'dynamic-select',
        tableFormatter: (field, row, dynamicSelects, header) => (
          <LazyLoadDynamicSelect
            data={(field ?? []).map((o) => o?.id)}
            header={header}
            formatter={(val) => val?.name}
            link="/schedules/comp-profiles"
          />
        ),
      },
      {
        id: 'contacts_agent_commission_schedule_profiles_sets',
        label: 'Agents',
        disableSort: true,
        type: 'custom',
        table: 'contacts',
        tableFormatter: (field, row, dynamicSelects, header) => {
          return (
            <LazyLoadDynamicSelect
              data={(field ?? []).map((o) => o?.contact?.id)}
              header={header}
              formatter={(o, idx) =>
                `${CommonFormatter.contact(o, { account_id: selectedAccount?.accountId })}${field[idx]?.hierarchy_processing === 'downlines' ? ' (and downlines)' : ''}`
              }
              link="/contacts"
            />
          );
        },
        optionFormatter: (option) => `${option.email}`,
        optionValuer: (option) => option?.id,
        render: (field, row, setter, dynamicSelects) => (
          <CompProfilesAdd
            key="contacts_agent_commission_schedule_profiles_sets"
            table="contacts_agent_commission_schedule_profiles_sets"
            data={row}
            setter={setter}
            field={field}
            dynamicSelects={dynamicSelects}
            readOnly={userRole !== Roles.ACCOUNT_ADMIN}
          />
        ),
      },
      { id: 'divider', type: 'divider' },
      {
        id: 'created_at',
        label: 'Created at',
        condition: (data) => !!data.created_at,
        formatter: CommonFormatter.dateTime,
        readOnly: true,
        visible: ['form'],
      },
      {
        id: 'updated_at',
        label: 'Updated at',
        condition: (data) => !!data.updated_at,
        formatter: CommonFormatter.dateTime,
        readOnly: true,
        visible: ['form'],
      },
    ],
  };

  if (viewSettings?.page_label) {
    dataDesc.label = viewSettings?.page_label;
  }

  return (
    <DataView
      dataDesc={dataDesc}
      hideExport
      viewOnly={viewOnly}
      readOnly={userRole !== Roles.ACCOUNT_ADMIN}
      // TODO: add this new view to fields and views settings
    />
  );
};

export default CompProfileSetsView;
