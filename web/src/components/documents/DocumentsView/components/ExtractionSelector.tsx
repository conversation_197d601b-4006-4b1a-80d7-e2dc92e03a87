import React from 'react';
import { Box, Select, MenuItem, Chip } from '@mui/material';
import { ExtractionGroup } from 'common/globalTypes';

interface ExtractionOption {
  value: string;
  label: string | React.ReactNode;
  type?: ExtractionGroup;
  disabled?: boolean;
  sx?: object;
}

interface ExtractionSelectorProps {
  options: ExtractionOption[];
  selectedMethods: string[];
  onSelectionChange: (methods: string[]) => void;
  onItemClick: (value: string) => void;
}

const ExtractionSelector: React.FC<ExtractionSelectorProps> = ({
  options,
  selectedMethods,
  onSelectionChange,
  onItemClick,
}) => {
  const renderValue = (selected: string[]) => {
    if (!Array.isArray(selected) || selected.length === 0) return '';

    if (selected[0].includes('::')) {
      const method = selected[0].split('::')[0];
      const option = options.find((opt) => opt.value === method);
      const aiOption = options.find(
        (opt) => opt.value === method && opt.type === ExtractionGroup.AI
      );
      const displayName = option?.label || aiOption?.label || method;
      return displayName;
    }

    const aiMethods = selected.filter((method) => {
      const option = options.find((opt) => opt.value === method);
      return option?.type === ExtractionGroup.AI;
    });

    const toolMethods = selected.filter((method) => {
      const option = options.find((opt) => opt.value === method);
      return option?.type === ExtractionGroup.TOOL;
    });

    if (aiMethods.length > 0) {
      return (
        <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
          {aiMethods.map((method) => {
            const option = options.find((opt) => opt.value === method);
            return (
              <Chip
                key={method}
                label={option?.label}
                size="small"
                variant="outlined"
              />
            );
          })}
        </Box>
      );
    }

    if (toolMethods.length > 0) {
      return (
        <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
          {toolMethods.map((method) => {
            const option = options.find((opt) => opt.value === method);
            return (
              <Chip
                key={method}
                label={option?.label}
                size="small"
                variant="outlined"
              />
            );
          })}
        </Box>
      );
    }

    const option = options.find((opt) => opt.value === selected[0]);
    return option?.label || '';
  };

  const handleSelectionChange = (event: any) => {
    const value = event.target.value;

    if (Array.isArray(value)) {
      const newValue = value.find((v: string) => !selectedMethods.includes(v));
      const removedValue = selectedMethods.find((v) => !value.includes(v));

      if (newValue) {
        onItemClick(newValue);
      } else if (removedValue) {
        const option = options.find((opt) => opt.value === removedValue);
        if (
          option?.type === ExtractionGroup.AI ||
          option?.type === ExtractionGroup.TOOL
        ) {
          const newMethods = selectedMethods.filter(
            (method) => method !== removedValue
          );
          onSelectionChange(newMethods);
        }
      }
    } else {
      onItemClick(value);
    }
  };

  return (
    <Select
      fullWidth
      multiple
      value={selectedMethods}
      onChange={handleSelectionChange}
      renderValue={renderValue}
      sx={{ mt: 1 }}
    >
      {options.map((item) => {
        if (item.disabled) {
          return (
            <MenuItem
              key={item.value}
              value={item.value}
              disabled={item.disabled}
              sx={{
                ...item.sx,
                py: 0.5,
                backgroundColor: '#fafafa',
                '&.Mui-disabled': {
                  opacity: 1,
                },
              }}
            >
              <Box sx={{ width: '100%' }}>{item.label}</Box>
            </MenuItem>
          );
        }

        const isSelected = selectedMethods.includes(item.value);
        return (
          <MenuItem
            key={item.value}
            value={item.value}
            onClick={() => onItemClick(item.value)}
            sx={{
              py: 0.8,
              backgroundColor: isSelected ? '#f5f5f5' : 'transparent',
              '&:hover': {
                backgroundColor: isSelected ? '#eeeeee' : '#f9f9f9',
              },
            }}
          >
            <Box sx={{ display: 'flex', alignItems: 'center', width: '100%' }}>
              <Box sx={{ flex: 1 }}>
                {typeof item.label === 'string' ? item.label : item.label}
              </Box>
              {isSelected && (
                <Box sx={{ ml: 1, color: '#666', fontSize: '14px' }}>●</Box>
              )}
            </Box>
          </MenuItem>
        );
      })}
    </Select>
  );
};

export default ExtractionSelector;
