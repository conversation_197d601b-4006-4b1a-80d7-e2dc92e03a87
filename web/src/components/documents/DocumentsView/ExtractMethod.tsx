import React, { useEffect, useMemo, useState } from 'react';
import {
  <PERSON>,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Typography,
} from '@mui/material';
import {
  DocumentPreviewTypes,
  ExtractionGroup,
  METHOD_LABELS,
  DEFAULT_METHODS,
  PDF_OPTIONS,
  HTML_OPTIONS,
} from 'common/globalTypes';

import { modelOptions } from '@/constants/prompt';
import ExtractionSelector from './components/ExtractionSelector';

interface ExtractionOption {
  value: string;
  label: string | React.ReactNode;
  type?: ExtractionGroup;
  disabled?: boolean;
  sx?: object;
}

interface UploadedRow {
  file_type?: string;
  extractions?: ExtractionItem[];
  [key: string]: any;
}

interface ExtractionItem {
  id: number;
  str_id: string;
  method: string;
  status: string;
  result?: string;
  created_at: string;
}

interface ExtractMethodProps {
  showExtract: boolean;
  onClose: () => void;
  onConfirm: (method: string) => void;
  uploadedRow: UploadedRow;
}

const createSectionDivider = (title: string): ExtractionOption => ({
  label: (
    <Typography
      variant="body2"
      sx={{ color: '#666', fontWeight: 500, textAlign: 'center' }}
    >
      {title}
    </Typography>
  ),
  value: `${title.toLowerCase().replace(/\s+/g, '_')}_divider`,
  disabled: true,
});

const getExtractionOptions = (fileType: string): ExtractionOption[] => {
  const toolOptions =
    fileType === DocumentPreviewTypes.HTML ? HTML_OPTIONS : PDF_OPTIONS;

  return [
    ...toolOptions.map((option) => ({
      ...option,
      type: ExtractionGroup.TOOL,
    })),

    createSectionDivider('Create new AI extraction'),
    ...modelOptions.map((option) => ({
      ...option,
      type: ExtractionGroup.AI,
    })),
  ];
};

const createPreviousExtractions = (
  extractions: ExtractionItem[]
): ExtractionOption[] => {
  const prevExtractions = extractions
    .filter((item) => {
      if (item.method === DEFAULT_METHODS.pdf) {
        return item.result;
      }
      return true;
    })
    .sort((a, b) => +new Date(b.created_at) - +new Date(a.created_at))
    .map((e): ExtractionOption => {
      let displayName = METHOD_LABELS[e.method];
      if (!displayName) {
        const aiOption = modelOptions.find((opt) => opt.value === e.method);
        displayName = aiOption ? aiOption.label : e.method;
      }

      return {
        label: (
          <Box
            sx={{
              display: 'flex',
              justifyContent: 'space-between',
              width: '100%',
            }}
          >
            <Box>{displayName}</Box>
            <Box sx={{ color: '#999', fontSize: '0.875rem' }}>
              {new Date(e.created_at).toLocaleString()}
            </Box>
          </Box>
        ),
        value: `${e.method}::${e.str_id}::${e.id}`,
        type: ExtractionGroup.EXISTING,
      };
    });

  if (prevExtractions.length > 0) {
    return [
      createSectionDivider('Use existing extraction'),
      ...prevExtractions,
      createSectionDivider('Create new extraction'),
    ];
  }

  return [];
};

const ExtractMethod: React.FC<ExtractMethodProps> = ({
  showExtract,
  onClose,
  onConfirm,
  uploadedRow,
}) => {
  const [selectedMethods, setSelectedMethods] = useState<string[]>([]);

  const extractionOptions = useMemo(
    () => getExtractionOptions(uploadedRow.file_type ?? ''),
    [uploadedRow.file_type]
  );

  const allOptions = useMemo(() => {
    const previousExtractions = createPreviousExtractions(
      uploadedRow.extractions || []
    );
    return [...previousExtractions, ...extractionOptions];
  }, [uploadedRow.extractions, extractionOptions]);

  useEffect(() => {
    if (uploadedRow.file_type === DocumentPreviewTypes.HTML) {
      setSelectedMethods([DEFAULT_METHODS.html]);
    } else {
      setSelectedMethods([DEFAULT_METHODS.pdf]);
    }
  }, [uploadedRow.file_type]);

  useEffect(() => {
    if ((uploadedRow.extractions ?? []).length > 0) {
      const extractions = uploadedRow.extractions ?? [];
      const mostRecent =
        extractions.find((item) => item.status === 'extracted') ||
        extractions[0];
      setSelectedMethods([
        `${mostRecent.method}::${mostRecent.str_id}::${mostRecent.id}`,
      ]);
    }
  }, [uploadedRow.extractions]);

  const handleItemClick = (value: string) => {
    if (value.includes('::')) {
      setSelectedMethods([value]);
      return;
    }

    const clickedOption = extractionOptions.find((opt) => opt.value === value);
    if (!clickedOption) return;

    if (clickedOption.type === ExtractionGroup.AI) {
      const aiMethods = selectedMethods.filter((method) => {
        const option = extractionOptions.find((opt) => opt.value === method);
        return option?.type === ExtractionGroup.AI;
      });

      if (aiMethods.includes(value)) {
        setSelectedMethods(
          selectedMethods.filter((method) => method !== value)
        );
      } else {
        const newAIMethods = [...aiMethods, value];
        setSelectedMethods(newAIMethods);
      }
    } else if (clickedOption.type === ExtractionGroup.TOOL) {
      const toolMethods = selectedMethods.filter((method) => {
        const option = extractionOptions.find((opt) => opt.value === method);
        return option?.type === ExtractionGroup.TOOL;
      });

      if (toolMethods.includes(value)) {
        setSelectedMethods(
          selectedMethods.filter((method) => method !== value)
        );
      } else {
        const newtoolMethods = [...toolMethods, value];
        setSelectedMethods(newtoolMethods);
      }
    }
  };

  const handleSubmit = () => {
    const aiMethods = selectedMethods.filter((method) => {
      const option = extractionOptions.find((opt) => opt.value === method);
      return option?.type === ExtractionGroup.AI;
    });

    const toolMethods = selectedMethods.filter((method) => {
      const option = extractionOptions.find((opt) => opt.value === method);
      return option?.type === ExtractionGroup.TOOL;
    });

    if (aiMethods.length > 0) {
      onConfirm(`ai_methods::${aiMethods.join(',')}`);
    } else if (toolMethods.length > 0) {
      onConfirm(`tool_methods::${toolMethods.join(',')}`);
    } else if (selectedMethods.length > 0) {
      onConfirm(selectedMethods[0]);
    }
  };

  const getButtonText = (): string => {
    const aiMethods = selectedMethods.filter((method) => {
      const option = extractionOptions.find((opt) => opt.value === method);
      return option?.type === ExtractionGroup.AI;
    });

    const toolMethods = selectedMethods.filter((method) => {
      const option = extractionOptions.find((opt) => opt.value === method);
      return option?.type === ExtractionGroup.TOOL;
    });

    if (aiMethods.length > 0) {
      return `Extract with ${aiMethods.length} AI${aiMethods.length > 1 ? 's' : ''}`;
    }

    if (toolMethods.length > 0) {
      return `Extract with ${toolMethods.length} Tool${toolMethods.length > 1 ? 's' : ''}`;
    }

    if (selectedMethods.length > 0 && selectedMethods[0].includes('::')) {
      return 'View data';
    }

    return 'Extract data';
  };

  return (
    <Dialog open={showExtract} onClose={onClose} fullWidth maxWidth="sm">
      <DialogTitle sx={{ pb: 1 }}>Document data extraction method</DialogTitle>
      <DialogContent sx={{ pt: 1 }}>
        <ExtractionSelector
          options={allOptions}
          selectedMethods={selectedMethods}
          onSelectionChange={setSelectedMethods}
          onItemClick={handleItemClick}
        />
      </DialogContent>
      <DialogActions sx={{ pt: 1 }}>
        <Button onClick={onClose}>Cancel</Button>
        <Button
          onClick={handleSubmit}
          disabled={selectedMethods.length === 0}
          variant="contained"
        >
          {getButtonText()}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default ExtractMethod;
