import {
  Add,
  ComputerOutlined,
  DeleteOutline,
  DownloadOutlined,
  EmailOutlined,
  LaunchOutlined,
  PlayArrow,
  SyncOutlined,
  WebhookOutlined,
} from '@mui/icons-material';
import { Alert, Box, Button, Chip, IconButton, Tooltip } from '@mui/material';
import { useQueryClient } from '@tanstack/react-query';
import CommonFormatter from 'common/Formatter';
import { DocumentTypeLabels, DocumentTypes } from 'common/constants/documents';
import {
  DocumentStatuses,
  DocumentStatusesLabels,
  ProcessMethod,
  UploadSource,
  UploadSourceLabels,
} from 'common/globalTypes';
import { numberOrDefault } from 'common/helpers';
import dayjs from 'dayjs';
import { isEqual } from 'lodash-es';
import { useContext, useState } from 'react';
import { Link } from 'react-router-dom';

import { BasicDialog, FileDialogPreview } from '@/common';
import { PDF_HTML_IMG_TYPES } from '@/common/preview/model';
import { useFilters } from '@/components/documents/DocumentsView';
import UploadOverrideFile from '@/components/documents/DocumentsView/DocumentOverrideFile';
import ExtractMethod from '@/components/documents/DocumentsView/ExtractMethod';
import UpdateProcessData from '@/components/documents/DocumentsView/UpdateProcessData';
import EnhancedDataView from '@/components/organisms/EnhancedDataView';
import { LoadingContext } from '@/contexts/LoadingContext';
import useDownloadStorageFile from '@/contexts/useDownloadStorageFile';
import usePreviewParams from '@/contexts/usePreviewParams';
import useSnackbar from '@/contexts/useSnackbar';
import API from '@/services/API';
import Formatter from '@/services/Formatter';
import { useAccountStore } from '@/store';
import { useSetOriginFile } from '@/store/excelStore';
import { DocumentPreviewKeys } from '@/types';

export const SyncStatusList = [
  {
    label: 'Synced',
    id: 'synced',
  },
  {
    label: 'Not synced',
    id: 'not_synced',
  },
];

const UPLOAD_SOURCE_CONFIG = {
  [UploadSource.EMAIL]: {
    icon: <EmailOutlined sx={{ fontSize: 15, color: '#aaaaaa' }} />,
    text: UploadSourceLabels[UploadSource.EMAIL],
  },
  [UploadSource.API]: {
    icon: <WebhookOutlined sx={{ fontSize: 15, color: '#aaaaaa' }} />,
    text: UploadSourceLabels[UploadSource.API],
  },
  [UploadSource.WEB]: {
    icon: <ComputerOutlined sx={{ fontSize: 15, color: '#aaaaaa' }} />,
    text: UploadSourceLabels[UploadSource.WEB],
  },
};

const DocumentsView = ({ variant = '' }) => {
  const { selectedAccount } = useAccountStore();
  const queryClient = useQueryClient();
  const enableAccountId = ['W4kSrayZvmh26pGfYVrGE', 'fFF86XAy2Cu97xxra8lgA'];
  const isRiskTag =
    enableAccountId.includes(selectedAccount?.accountId || '') ||
    selectedAccount?.accountName?.toLowerCase().includes('risktag');

  const [sync, setSync] = useState({ documentId: '', show: false, count: 0 });

  const [open, setOpen] = useState(false);
  const [showUploadModal, setShowUploadModal] = useState(false);
  const [showExtract, setShowExtract] = useState(false);

  const [rowData, setRowData] = useState({});
  const { setLoadingConfig } = useContext(LoadingContext);
  const { downloadFile } = useDownloadStorageFile();

  const setUploadedFile = useSetOriginFile();
  const { showSnackbar } = useSnackbar();

  const documentsDelete = API.getMutation('documents', 'DELETE');
  const syncStatement = API.getMutation(
    'data_processing/sync/benefit-point/statements',
    'POST'
  );

  const { filters, filtersData } = useFilters({ isAdmin: true });

  const { showPreview, setShowPreview, previewId, setPreviewPath } =
    usePreviewParams();

  const filePathFormatter = (filename, row) => {
    const hasOverrideFile = !!row.override_filename;

    return filename ? (
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'column',
        }}
      >
        <Box
          sx={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
            width: '100%',
          }}
        >
          <Tooltip title={filename} enterNextDelay={1000}>
            <Button
              style={{
                maxWidth: 400,
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                textAlign: 'left',
                whiteSpace: 'nowrap',
                padding: '4px 8px',
                minWidth: 'unset',
              }}
              onClick={async () => {
                setPreviewPath(row.str_id, DocumentPreviewKeys.ORIGINAL);
                setShowPreview(true);
              }}
            >
              <span
                style={{
                  overflow: 'hidden',
                  textOverflow: 'ellipsis',
                  textAlign: 'left',
                }}
              >
                {filename}
              </span>
              {row.upload_source && (
                <Tooltip
                  title={UPLOAD_SOURCE_CONFIG[row.upload_source]?.text || ''}
                >
                  <Box
                    sx={{
                      display: 'inline-flex',
                      ml: 0.5,
                      alignItems: 'center',
                      opacity: 0.7,
                      '&:hover': {
                        opacity: 1,
                      },
                    }}
                  >
                    {UPLOAD_SOURCE_CONFIG[row.upload_source]?.icon || null}
                  </Box>
                </Tooltip>
              )}
            </Button>
          </Tooltip>

          {!hasOverrideFile && (
            <Tooltip title="Add override file" placement="right">
              <IconButton
                size="small"
                onClick={() => {
                  setRowData(row);
                  setShowUploadModal(true);
                }}
              >
                <Add sx={{ height: 18 }} />
              </IconButton>
            </Tooltip>
          )}
        </Box>

        {hasOverrideFile && (
          <Box sx={{ mt: 0.5 }}>
            <Tooltip title={row.override_filename} enterNextDelay={1000}>
              <Button
                style={{
                  overflow: 'hidden',
                  textOverflow: 'ellipsis',
                  textAlign: 'left',
                  whiteSpace: 'nowrap',
                  padding: '4px 8px',
                  minWidth: 'unset',
                }}
                onClick={async () => {
                  setPreviewPath(row.str_id, DocumentPreviewKeys.OVERRIDE);
                  setShowPreview(true);
                }}
              >
                <span
                  style={{
                    overflow: 'hidden',
                    textOverflow: 'ellipsis',
                    textAlign: 'left',
                  }}
                >
                  Override: {row.override_filename}
                </span>
              </Button>
            </Tooltip>
          </Box>
        )}
      </Box>
    ) : (
      '--'
    );
  };

  const deleteData = async (row) => {
    try {
      setLoadingConfig({
        loading: true,
        message: 'Deleting...',
      });
      await documentsDelete.mutateAsync({ id: row.id });
      queryClient.invalidateQueries();
      setLoadingConfig({
        loading: false,
        message: '',
      });
      showSnackbar(`Deleted ${row.filename}`, 'success');
    } catch (error: any) {
      setLoadingConfig({
        loading: false,
        message: '',
      });
      showSnackbar(error.message || error, 'error');
    }
  };

  const extractData = async (row) => {
    try {
      setLoadingConfig({
        loading: true,
        message: 'Loading...',
      });
      const file = await downloadFile({
        file_preview_type: row.override_file_path ? 'override' : 'original',
        endpoint_str_id: row.str_id,
        endpoint: 'documents',
      });
      if (!file) {
        setLoadingConfig({
          loading: false,
          message: '',
        });
        throw new Error('Failed to download file');
      }
      setRowData({
        ...row,
      });
      setUploadedFile(file);

      setLoadingConfig({
        loading: false,
        message: '',
      });
      if (PDF_HTML_IMG_TYPES.includes(file.type)) {
        setShowExtract(true);
      } else {
        setOpen(true);
      }
    } catch (error: any) {
      setLoadingConfig({
        loading: false,
        message: '',
      });

      const tip = error.message || error;
      showSnackbar(tip, 'error');
    }
  };

  const dataDesc = {
    label: 'Documents',
    table: 'admin/documents',
    filters,
    fields: {
      account: {
        label: 'Account',
        enabled: true,
        formatter: Formatter.getLinkChipFormatter(
          'name',
          'str_id',
          '/admin/accounts?id='
        ),
      },
      filename: {
        label: 'File',
        enabled: true,
        copyable: true,
        formatter: filePathFormatter,
      },
      type: {
        label: 'Type',
        enabled: true,
        formatter: (v: DocumentTypes) => DocumentTypeLabels[v],
      },
      companies: {
        label: 'Company',
        enabled: true,
        formatter: Formatter.getLinkChipFormatter(
          'company_name',
          'str_id',
          '/admin/companies?id='
        ),
      },
      imports_count: {
        label: 'Imports',
        enabled: true,
        formatter: (count, row) => (
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <Box sx={{ flex: 1, px: 2 }}>
              <span>{count || 0}</span>
            </Box>
            <Box>
              {count > 0 && (
                <IconButton
                  component={Link}
                  to={`/imports?q=${row.str_id}`}
                  target="_blank"
                  sx={{
                    opacity: 0.5,
                    '&:hover': { opacity: 1 },
                    color: '#2196f3',
                  }}
                >
                  <LaunchOutlined />
                </IconButton>
              )}
            </Box>
          </Box>
        ),
      },
      statement_data: {
        label: 'Records',
        disableSort: true,
        enabled: true,
        formatter: (val, row) => {
          const groupedCountInfoStrList: string[] = [];
          const groupedCommissionInfoStrList: string[] = [];
          if (val.groupedCountInfo) {
            Object.entries(val.groupedCountInfo).forEach(([key, value]) => {
              if (key !== 'NO_STATUS') {
                groupedCountInfoStrList.push(`${key}: ${value}`);
              }
            });
          }
          if (val.groupedCommissionInfo) {
            Object.entries(val.groupedCommissionInfo).forEach(
              ([key, value]) => {
                if (key !== 'NO_STATUS') {
                  groupedCommissionInfoStrList.push(
                    `${key}: ${Formatter.currency(value)}`
                  );
                }
              }
            );
          }
          return (
            <>
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <Box sx={{ flex: 1 }}>
                  {!!val.total_count && (
                    <Box
                      sx={{
                        whiteSpace: 'nowrap',
                      }}
                    >
                      <span>{+val.total_count}</span>
                      {groupedCountInfoStrList.length > 0 && (
                        <span
                          style={{
                            color: '#444',
                            fontSize: 13,
                          }}
                        >
                          ({groupedCountInfoStrList.toString()})
                        </span>
                      )}
                    </Box>
                  )}
                  {!!val?.total_commission && (
                    <Box
                      sx={{
                        whiteSpace: 'nowrap',
                      }}
                    >
                      <span>{Formatter.currency(val.total_commission)}</span>
                      {groupedCommissionInfoStrList.length > 0 && (
                        <span
                          style={{
                            color: '#444',
                            fontSize: 13,
                          }}
                        >
                          ({groupedCommissionInfoStrList.toString()})
                        </span>
                      )}
                    </Box>
                  )}
                  {!val?.total_commission && !val.total_count && '0'}
                </Box>
                <Box>
                  {(val.total_commission > 0 || val.total_count > 0) && (
                    <>
                      <IconButton
                        component={Link}
                        to={`/${row.type === 'statement' ? 'commissions' : 'policies'}?q=${row.str_id}`}
                        target="_blank"
                        sx={{
                          opacity: 0.5,
                          '&:hover': { opacity: 1 },
                          color: '#2196f3',
                        }}
                      >
                        <LaunchOutlined />
                      </IconButton>
                      {isRiskTag && (
                        <span>
                          {row?._count?.statement_data > 0 && (
                            <Tooltip
                              title={`${row._count.statement_data} statement entries will be synced to BenefitPoint`}
                              placement="top"
                            >
                              <IconButton
                                onClick={() =>
                                  setSync({
                                    documentId: row.str_id,
                                    show: true,
                                    count: row?._count?.statement_data || 0,
                                  })
                                }
                                size="small"
                                sx={{
                                  opacity: 0.5,
                                  '&:hover': { opacity: 1 },
                                  color: '#2196f3',
                                }}
                              >
                                <SyncOutlined />
                              </IconButton>
                            </Tooltip>
                          )}
                          <IconButton
                            // OnClick={() => exportData(row)}
                            size="small"
                            sx={{
                              opacity: 0.5,
                              '&:hover': { opacity: 1 },
                              color: '#2196f3',
                            }}
                          >
                            <DownloadOutlined />
                          </IconButton>
                        </span>
                      )}
                    </>
                  )}
                </Box>
              </Box>
            </>
          );
        },
      },
      statement_amount: {
        label: 'Commission totals',
        enabled: true,
        disableSort: true,
        formatter: (v, row) => {
          const statementData = row.statement_data;
          const statementAmount = numberOrDefault(row.statement_amount, null, {
            toFixed: 2,
          });
          const statementTotalAmount = numberOrDefault(
            row.imports?.[0]?.statement_total_amount,
            null,
            { toFixed: 2 }
          );
          const totalCommissionAmount = numberOrDefault(
            statementData.total_commission,
            null,
            { toFixed: 2 }
          );

          if (row.status !== DocumentStatuses.PROCESSED) {
            return (
              <Box
                sx={{
                  whiteSpace: 'nowrap',
                  display: 'flex',
                  flexDirection: 'column',
                }}
              >
                {statementAmount && (
                  <span>{`Statement amount: ${Formatter.currency(statementAmount)}`}</span>
                )}
                {statementTotalAmount && (
                  <span>{`Statement total amount: ${Formatter.currency(statementTotalAmount)}`}</span>
                )}
              </Box>
            );
          }

          const statementAndCmsTotalIsEqual = isEqual(
            statementAmount,
            totalCommissionAmount
          );
          const statementTotalAndCmsTotalIsEqual = isEqual(
            totalCommissionAmount,
            statementTotalAmount
          );

          let matchesNode = <></>;

          if (
            statementAndCmsTotalIsEqual &&
            statementTotalAndCmsTotalIsEqual &&
            statementAmount !== null
          ) {
            const tip =
              'Statement amount, statement total amount, and commission records all match';
            matchesNode = (
              <Tooltip title={tip} placement="right">
                <span>{`✅ ${Formatter.currency(statementAmount)}`}</span>
              </Tooltip>
            );
          } else if (statementAndCmsTotalIsEqual && totalCommissionAmount) {
            const tip = (
              <span>
                Statement amount and commissions match
                <br />
                (Statement total amount not available)
              </span>
            );
            matchesNode = (
              <Tooltip title={tip} placement="right">
                <span>{`✅ ${Formatter.currency(statementAmount)}`}</span>
              </Tooltip>
            );
          } else if (
            statementTotalAndCmsTotalIsEqual &&
            totalCommissionAmount
          ) {
            const tip =
              'Statement total amount and commission records match (Statement amount not available)';
            matchesNode = (
              <Tooltip title={tip} placement="right">
                <span>{`✅ ${Formatter.currency(statementTotalAmount)}`}</span>
              </Tooltip>
            );
          } else if (
            !statementAmount &&
            !statementTotalAmount &&
            !totalCommissionAmount
          ) {
            matchesNode = <span>No amounts available</span>;
          } else if (
            totalCommissionAmount &&
            !statementTotalAmount &&
            !statementAmount
          ) {
            matchesNode = (
              <Tooltip
                title={
                  <span>
                    Validation not available.
                    <br />
                    Statement amount and/or statement total amount required.
                  </span>
                }
                placement="right"
              >
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <Box sx={{ fontSize: 12 }}>ℹ️</Box>
                  <Box
                    sx={{
                      flex: 1,
                      display: 'flex',
                      flexDirection: 'column',
                    }}
                  >
                    <span>
                      {`Commissions: ${Formatter.currency(totalCommissionAmount)}`}
                    </span>
                  </Box>
                </Box>
              </Tooltip>
            );
          } else {
            matchesNode = (
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <Box sx={{ fontSize: 12 }}>❌</Box>
                <Box
                  sx={{
                    flex: 1,
                    display: 'flex',
                    flexDirection: 'column',
                  }}
                >
                  {totalCommissionAmount ? (
                    <span>{`Commissions: ${Formatter.currency(totalCommissionAmount)}`}</span>
                  ) : (
                    <span>No commission data</span>
                  )}
                  {statementAmount ? (
                    <span>{`Statement amount: ${Formatter.currency(statementAmount)}`}</span>
                  ) : (
                    <span>No statement amount</span>
                  )}
                  {statementTotalAmount ? (
                    <span>{`Statement total amount: ${Formatter.currency(statementTotalAmount)}`}</span>
                  ) : (
                    <span>No statement total amount</span>
                  )}
                </Box>
              </Box>
            );
          }
          return <Box sx={{ whiteSpace: 'nowrap' }}>{matchesNode}</Box>;
        },
      },
      bank_total_amount: {
        label: 'Bank totals',
        enabled: true,
        disableSort: true,
        formatter: (v, row) => {
          const bankTotalAmount = numberOrDefault(row.bank_total_amount, null, {
            toFixed: 2,
          });

          return (
            <Box
              sx={{
                whiteSpace: 'nowrap',
                display: 'flex',
                flexDirection: 'column',
              }}
            >
              {bankTotalAmount && (
                <span>{`Bank total: ${Formatter.currency(bankTotalAmount)}`}</span>
              )}
            </Box>
          );
        },
      },
      created_by: {
        label: 'Imported total',
        enabled: true,
        disableSort: true,
        formatter: (v, row) => {
          const list = row.imports;
          if (list && list.length) {
            const target = list[0];
            return Formatter.currency(target?.summed_total_amount);
          }
        },
      },
      status: {
        label: 'Status',
        enabled: true,
        formatter: (val, row) => {
          let statusChip;

          if (
            val === DocumentStatuses.PROCESSED &&
            row.process_method === ProcessMethod.AUTO
          ) {
            statusChip = Formatter.statusChip('Processed✨', {
              mapping: {
                'Processed✨': 'green',
              },
            });
          } else {
            statusChip = Formatter.statusChip(DocumentStatusesLabels[val], {
              mapping: {
                'Pending upload': 'yellow',
                New: 'yellow',
                Processing: 'blue',
                'Pending review': 'deepBlue',
                Processed: 'green',
              },
            });
          }

          if (row.processing_notes) {
            return (
              <Tooltip
                title={
                  <Box>
                    <Box sx={{ whiteSpace: 'pre-wrap', maxWidth: 400 }}>
                      {row.processing_notes}
                    </Box>
                  </Box>
                }
                arrow
                placement="top"
                enterDelay={500}
              >
                <Box sx={{ display: 'inline-block' }}>{statusChip}</Box>
              </Tooltip>
            );
          }

          return statusChip;
        },
      },
      method: {
        label: 'Method',
        enabled: true,
        formatter: (val: string, row) =>
          val ? (
            <Chip
              label={val}
              component={Link}
              to={`/documents/profiles?id=${row.profile_str_id}`}
            />
          ) : null,
      },
      notes: {
        label: 'Notes',
        enabled: true,
      },
      sync_id: {
        label: 'Synced',
        enabled: true,
        type: 'boolean',
        options: SyncStatusList,
      },
      imported_at: {
        label: 'Imported at',
        enabled: true,

        formatter: (s: string, row) => {
          if (!s) {
            return '';
          }
          const uploadToImportTime = dayjs(s).diff(
            dayjs(row.created_at),
            'milliseconds'
          );
          const uploadedInRes = `${CommonFormatter.duration(Math.abs(uploadToImportTime), { truncate: 'seconds' })}`;
          return `${Formatter.date(s, {
            format: 'MM/DD/YYYY hh:mmA',
          })} (${uploadedInRes})`;
        },
      },
      created_at: {
        label: 'Uploaded at',
        enabled: true,
        formatter: Formatter.dateTime,
        readOnly: true,
      },
    },
    queryChips: {
      all: {
        id: 'all',
        label: 'All',
        query: {},
      },
      pending_upload: {
        id: DocumentStatuses.PENDING_UPLOAD,
        label: DocumentStatusesLabels[DocumentStatuses.PENDING_UPLOAD],
        query: {
          status: DocumentStatuses.PENDING_UPLOAD,
        },
      },
      new: {
        id: DocumentStatuses.NEW,
        label: DocumentStatusesLabels[DocumentStatuses.NEW],
        query: {
          status: DocumentStatuses.NEW,
        },
      },
      processing: {
        id: DocumentStatuses.PROCESSING,
        label: DocumentStatusesLabels[DocumentStatuses.PROCESSING],
        query: {
          status: DocumentStatuses.PROCESSING,
        },
      },
      pending_review: {
        id: DocumentStatuses.PENDING_REVIEW,
        label: DocumentStatusesLabels[DocumentStatuses.PENDING_REVIEW],
        query: {
          status: DocumentStatuses.PENDING_REVIEW,
        },
      },
      processed: {
        id: DocumentStatuses.PROCESSED,
        label: DocumentStatusesLabels[DocumentStatuses.PROCESSED],
        query: {
          status: DocumentStatuses.PROCESSED,
        },
      },
    },
    actions: [
      {
        id: 'process',
        label: 'Process',
        type: 'iconButton',
        icon: <PlayArrow />,
        onClick: (row) => extractData(row),
      },
      {
        id: 'delete',
        label: 'Delete',
        type: 'iconButton',
        icon: <DeleteOutline />,
        enabled: (row) => row.status === DocumentStatuses.NEW,
        onClick: (row) => deleteData(row),
      },
    ],
  };

  const onConfirmMethod = (method) => {
    setRowData((prev) => ({ ...prev, method }));
    setShowExtract(false);
    setOpen(true);
  };

  const extraActions = [
    {
      type: 'dateRange',
      label: 'Uploaded at',
      value: {
        startDate: null,
        endDate: null,
      },
    },
  ];

  const syncToBenefit = async (data) => {
    try {
      setLoadingConfig({
        loading: true,
        message: 'Syncing...',
      });
      const ret = await syncStatement.mutateAsync(data);
      if (ret.success === false || ret.message) {
        const tip = <Alert severity="error">{ret.message}</Alert>;
        showSnackbar(tip);
      } else {
        showSnackbar(
          <Alert severity="success">
            Sync succeessfully, synced statmentID: {ret.statementId}
          </Alert>
        );
      }
    } catch (error: any) {
      const tip = <Alert severity="error">{error.message || error}</Alert>;
      showSnackbar(tip);
    } finally {
      setLoadingConfig({
        loading: false,
        message: '',
      });
    }
  };

  return (
    <>
      <EnhancedDataView
        dataSpec={dataDesc}
        hideAdd
        hideSelectedCount
        enableMultiSelect={false}
        enableEdit={false}
        extraActions={extraActions}
        variant={variant}
        filters={filtersData as any}
        actionsEnabled={() => true}
        actions={dataDesc.actions as any}
        hideExport
      />
      {open && (
        <UpdateProcessData
          open={open}
          rowData={rowData}
          setRowData={setRowData}
          handleClose={(arg) => {
            queryClient.invalidateQueries();
            setOpen(arg);
          }}
        />
      )}
      {showUploadModal && (
        <UploadOverrideFile
          open={showUploadModal}
          setOpen={setShowUploadModal}
          uploadedRow={rowData}
        />
      )}
      {showExtract && (
        <ExtractMethod
          showExtract={showExtract}
          onClose={() => setShowExtract(false)}
          onConfirm={onConfirmMethod}
          uploadedRow={rowData}
        />
      )}

      {showPreview && (
        <FileDialogPreview
          showPreview={showPreview}
          setShowPreview={setShowPreview}
          fileId={previewId}
          isAdmin={true}
        />
      )}

      <BasicDialog
        open={sync.show}
        title="Sync data"
        bodyComponent={
          <Alert severity="warning">
            Are you sure you want to sync {sync.count} statement entries to
            BenefitPoint?
          </Alert>
        }
        onClose={(isOk) => {
          if (isOk) {
            syncToBenefit({ documentId: sync.documentId });
          }

          setSync({ ...sync, show: false });
        }}
        positiveLabel="Sync"
      />
    </>
  );
};

export default DocumentsView;
