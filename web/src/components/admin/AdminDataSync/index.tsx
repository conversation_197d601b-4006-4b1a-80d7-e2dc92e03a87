import React, { useEffect, useState } from 'react';
import {
  Button,
  CircularProgress,
  TextField,
  Typography,
  Alert,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
} from '@mui/material';

import { useDataSync } from '@/hooks/useDataSync';
import { useUserInfo } from '@/hooks/useUserInfo';

const AdminSyncDataView: React.FC = () => {
  const [isEditing, setIsEditing] = useState<boolean>(false);
  const [isSaving, setIsSaving] = useState<boolean>(false);
  const { data } = useUserInfo();
  const {
    isGoogleAuthenticated,
    googleLoginUrl,
    error,
    initializeGoogleAuth,
    handleSave,
    verifiedUser,
  } = useDataSync(data);

  console.log('verifiedUser', verifiedUser);

  const [googleDriveFolder, setGoogleDriveFolder] = useState<string>(
    verifiedUser?.googleDriveFolder
  );

  useEffect(() => {
    initializeGoogleAuth();
  }, [initializeGoogleAuth]);

  const handleSaveForm = async () => {
    setIsSaving(true);
    try {
      await handleSave(googleDriveFolder);
    } catch (err) {
      console.error('Error saving folder:', err);
    } finally {
      setIsSaving(false);
      setIsEditing(false);
    }
  };

  useEffect(() => {
    setGoogleDriveFolder(verifiedUser?.googleDriveFolder);
  }, [verifiedUser?.googleDriveFolder]);

  const handleEdit = () => {
    setIsEditing(true);
  };

  return (
    <div style={{ padding: '16px', maxWidth: '800px', margin: '0 auto' }}>
      <Typography variant="h5" gutterBottom style={{ textAlign: 'center' }}>
        Admin - Connect Google sync
      </Typography>

      {error && (
        <Alert severity="error" style={{ marginBottom: '16px' }}>
          {error}
        </Alert>
      )}

      {!isGoogleAuthenticated ? (
        <div style={{ textAlign: 'center', marginTop: '16px' }}>
          <Typography variant="body1" gutterBottom>
            Please log in with Google to access Gmail and Drive.
          </Typography>
          {googleLoginUrl ? (
            <Button variant="contained" color="primary" href={googleLoginUrl}>
              Login with Google
            </Button>
          ) : (
            <CircularProgress />
          )}
        </div>
      ) : (
        <TableContainer component={Paper} style={{ marginTop: '16px' }}>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>
                  <strong>Field</strong>
                </TableCell>
                <TableCell>
                  <strong>Value</strong>
                </TableCell>
                <TableCell>
                  <strong>Actions</strong>
                </TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              <TableRow>
                <TableCell>User Email</TableCell>
                <TableCell>{verifiedUser?.email || 'N/A'}</TableCell>
                <TableCell />
              </TableRow>
              <TableRow>
                <TableCell>Google Drive Folder</TableCell>
                <TableCell>
                  {isEditing ? (
                    <TextField
                      fullWidth
                      variant="outlined"
                      value={googleDriveFolder}
                      onChange={(e) => setGoogleDriveFolder(e.target.value)}
                      placeholder="Enter Google Drive folder"
                    />
                  ) : (
                    googleDriveFolder || 'Not set'
                  )}
                </TableCell>
                <TableCell>
                  {isEditing ? (
                    <Button
                      variant="contained"
                      color="primary"
                      onClick={handleSaveForm}
                      disabled={isSaving || !googleDriveFolder}
                      style={{ whiteSpace: 'nowrap' }}
                    >
                      {isSaving ? 'Saving...' : 'Save'}
                    </Button>
                  ) : (
                    <Button
                      variant="outlined"
                      color="primary"
                      onClick={handleEdit}
                      style={{ whiteSpace: 'nowrap' }}
                    >
                      Edit
                    </Button>
                  )}
                </TableCell>
              </TableRow>
            </TableBody>
          </Table>
        </TableContainer>
      )}
    </div>
  );
};

export default AdminSyncDataView;
