import { Box, IconButton, Tooltip, Typography } from '@mui/material';
import { Link } from 'react-router-dom';
import { useContext } from 'react';
import { VerticalSplit, Launch } from '@mui/icons-material';

import { ComparisonContext } from '@/contexts/ReconciliationConfirmProvider';
import ExpandableData from '@/components/molecules/ExpandableData';
import DataPreviewTable from '@/components/molecules/DataPreviewTable';
import DataTransformation from '@/services/DataTransformation';
import { getStatementIcon, PolicyIdColumn } from './PolicyIdColumn';

const Normalizer = DataTransformation;

export const ChildrenDataTableFormatter = ({ vals, row, title }: any) => {
  const ctx = useContext(ComparisonContext);
  return (
    <ExpandableData
      key="children_data"
      header={{ label: title ?? 'Grouped commissions' }}
      formatter={(val: any) => (
        <Box
          sx={{
            display: 'flex',
            alignItems: 'center',
            flexDirection: 'row',
            gap: 2,
            p: 2,
            borderRadius: 2,
            boxShadow: 1,
            bgcolor: 'background.paper',
            minWidth: 0,
            width: '100%',
            border: (theme) => `1px solid ${theme.palette.divider}`,
          }}
        >
          <Box sx={{ flex: 1, minWidth: 0, overflow: 'hidden' }}>
            <Box
              sx={{
                display: 'flex',
                alignItems: 'center',
                alignContent: 'center',
                whiteSpace: 'nowrap',
                overflow: 'hidden',
                mb: 0.5,
              }}
              title={val.str_id}
            >
              {getStatementIcon(val, { ml: 0 })}
              <Typography
                variant="subtitle2"
                sx={{
                  fontWeight: 500,
                  overflow: 'hidden',
                  textOverflow: 'ellipsis',
                  minWidth: 0,
                }}
              >
                {val.str_id}
              </Typography>
            </Box>
            <Tooltip title="Commission amount">
              <Typography
                variant="body2"
                color="text.secondary"
                sx={{ fontWeight: 400, mb: 0.25 }}
              >
                {Normalizer.formatCurrency(val.commission_amount)}
              </Typography>
            </Tooltip>
            <Tooltip title="Payment date">
              <Typography
                variant="body2"
                color="text.secondary"
                sx={{ fontWeight: 400 }}
              >
                {Normalizer.formatDate(val.payment_date)}
              </Typography>
            </Tooltip>
          </Box>
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
            <IconButton
              onClick={() => {
                ctx.setData({
                  statements: vals,
                  policy: row.report,
                  defaultOpenStatement: val.str_id,
                });
                ctx.setShow(true);
              }}
              size="small"
              aria-label="Compare"
            >
              <VerticalSplit fontSize="inherit" />
            </IconButton>
            <IconButton
              component={Link}
              to={`/commissions?id=${val?.str_id}`}
              target="_blank"
              size="small"
              aria-label="Open commission"
            >
              <Launch fontSize="inherit" />
            </IconButton>
          </Box>
        </Box>
      )}
      data={vals}
    />
  );
};

export const ChildrenDataRenderer = ({
  row,
  collectionVals,
  field = 'children_data',
  accountId,
  title,
}: any) => {
  return row && Array.isArray(row[field]) ? (
    <DataPreviewTable
      label={title ?? 'Grouped commissions'}
      data={row[field]}
      fields={[
        {
          label: 'Commission ID',
          key: 'str_id',
          formatter: (val: any) => (
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <Typography
                variant="body2"
                component="span"
                sx={{ whiteSpace: 'nowrap' }}
              >
                {val ? `${val.substring(0, 10)}...` : ''}
              </Typography>
              <IconButton
                component={Link}
                to={`/commissions?id=${val}`}
                target="_blank"
                size="small"
              >
                <Launch fontSize="inherit" />
              </IconButton>
            </Box>
          ),
          copyable: true,
        },
        {
          label: 'Policy ID',
          key: 'policy_id',
          formatter: (val: any, _collectionVals: any, data: any) => {
            return (
              <PolicyIdColumn val={val} row={data} accountId={accountId} />
            );
          },
        },
        {
          label: 'Commission amount',
          key: 'commission_amount',
          formatter: Normalizer.formatCurrency,
        },
        {
          label: 'Period date',
          key: 'period_date',
          formatter: Normalizer.formatDate,
        },
        {
          label: 'Payment date',
          key: 'payment_date',
          formatter: Normalizer.formatDate,
        },
        {
          label: 'Product type',
          key: 'product_type',
        },
      ]}
      collectionVals={collectionVals}
    />
  ) : null;
};
