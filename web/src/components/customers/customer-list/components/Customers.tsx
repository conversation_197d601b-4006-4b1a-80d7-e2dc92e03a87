import { GroupAdd, Launch } from '@mui/icons-material';
import { Box, IconButton, TextField, Typography } from '@mui/material';
import { getCustomerFieldConfig } from 'common/field-config/customer';
import { mergeConfigs } from 'common/field-config/document';
import { useMemo, useState } from 'react';
import { Link } from 'react-router-dom';

import { SyncEndAdornment } from '@/common/SyncEndAdornment';
import { SyncedEntity } from '@/common/SyncedEntity';
import { SyncWithPolicies } from '@/components/customers/sync-with-policies';
import DataPreviewTable from '@/components/molecules/DataPreviewTable';
import useEnhancedTableToolbarStore from '@/components/molecules/EnhancedTableToolbar/store';
import EnhancedDataView from '@/components/organisms/EnhancedDataView';
import { useSyncedFieldsNew } from '@/contexts/useSyncedFields';
import Formatter from '@/services/Formatter';
import { formatDate } from '@/utils/datetime';

export const Customers = () => {
  const isBulkEdit = useEnhancedTableToolbarStore((state) => state.isBulkEdit);
  const [openCreateCustomers, setOpenCreateCustomers] = useState(false);
  const { workerSyncedFields, isSyncedField } = useSyncedFieldsNew();

  const dataDesc = useMemo(() => {
    const config = getCustomerFieldConfig();

    const fields = {
      type: {
        formatter: (v: string) => (
          <Box sx={{ textTransform: 'capitalize' }}>{v}</Box>
        ),
      },
      first_name: {},
      middle_name: {},
      last_name: {},
      nickname: {},
      dob: {
        formatter: formatDate,
      },
      gender: {
        formatter: (v: string) => (
          <Box sx={{ textTransform: 'capitalize' }}>{v}</Box>
        ),
      },
      company_name: {},
      website: {},
      email: {},
      phone: {},
      address: {
        formatter: (v) => {
          if (!v) return '';
          const values = [v.street, v.city, v.geo_state, v.zipcode].filter(
            (v) => !!v
          );
          return <Box>{values.join(', ')}</Box>;
        },
        render: (_, newData, setNewData) => {
          const {
            street = '',
            city = '',
            geo_state = '',
            zipcode = '',
          } = newData.address || {};

          const onChange = (v: string, field: string) => {
            setNewData({
              ...newData,
              address: {
                ...(newData.address || {}),
                [field]: v,
              },
            });
          };
          return (
            <Box sx={{ display: 'flex', gap: 1 }}>
              <TextField
                value={street}
                onChange={(v) => onChange(v.target.value, 'street')}
                label="Street"
              />
              <TextField
                value={city}
                onChange={(v) => onChange(v.target.value, 'city')}
                label="City"
              />
              <TextField
                value={geo_state}
                onChange={(v) => onChange(v.target.value, 'geo_state')}
                label="State"
              />
              <TextField
                value={zipcode}
                onChange={(v) => onChange(v.target.value, 'zipcode')}
                label="Zip code"
              />
            </Box>
          );
        },
      },
      group_id: {},
      start_date: {
        formatter: formatDate,
      },
      end_date: {
        formatter: formatDate,
      },
      report_data: {
        tableFormatter: (field) => field.length || '',
        render: (field, row) => (
          <DataPreviewTable
            label="Policies"
            data={row.report_data}
            fields={[
              {
                label: 'ID',
                key: 'str_id',
                formatter: (val) => (
                  <Box sx={{ whiteSpace: 'nowrap' }}>
                    <Typography variant="body2" component="span">
                      {`${val.substring(0, 10)}...`}
                    </Typography>
                    <IconButton
                      component={Link}
                      to={`/policies?id=${val}`}
                      target="_blank"
                    >
                      <Launch />
                    </IconButton>
                  </Box>
                ),
              },
              { label: 'Policy number', key: 'policy_id' },
              { label: 'Master company', key: 'writing_carrier_name' },
              { label: 'Product type', key: 'product_type' },
              { label: 'Product name', key: 'product_name' },
              { label: 'Policy status', key: 'policy_status' },
              {
                label: 'Effective date',
                key: 'effective_date',
                formatter: Formatter.date,
              },
              { label: 'Policy term (months)', key: 'policy_term_months' },
            ]}
            opts={{ expanded: true }}
          />
        ),
      },
    };

    const renderConfig = {
      fields: fields,
    };

    const mergedConfig = mergeConfigs(config, renderConfig);

    return mergedConfig;
  }, []);

  const extraActions = [
    {
      type: 'button',
      label: 'Sync with policies',
      onClick: () => setOpenCreateCustomers(true),
      icon: <GroupAdd />,
    },
  ];
  if (!isBulkEdit) {
    for (const key in dataDesc.fields) {
      const field = dataDesc.fields[key];
      const fieldId = key;
      field.readOnly =
        field.readOnly ||
        ((data) => {
          const syncedFields =
            workerSyncedFields?.[data?.sync_worker]?.customers;
          if ((syncedFields || []).includes(fieldId)) {
            return isSyncedField(data, syncedFields, fieldId, data.config);
          }
          return false;
        });
      field.endAdornment = (data, field, setNewData) => (
        <SyncEndAdornment
          syncedFields={workerSyncedFields?.[data?.sync_worker]?.customers}
          syncId={data?.sync_id}
          fieldId={fieldId}
          data={data}
          fieldType={field.type}
          onChange={(newOverrideFields) => {
            setNewData({
              ...data,
              config: {
                ...(data.config || {}),
                overrideFields: newOverrideFields,
              },
            });
          }}
        />
      );
    }
  }

  const actions = [
    {
      type: 'icon',
      label: 'Sync',
      icon: <SyncedEntity isSynced={true} disabled={true} />,
      enabled: (row) => !!row.sync_id,
      onClick: () => {},
    } as never,
  ];

  return (
    <>
      <EnhancedDataView
        extraActions={extraActions as any}
        bulkAdd
        dataSpec={dataDesc}
        actions={actions}
        actionsEnabled={
          ((row) => {
            return !!row.sync_id;
          }) as any
        }
      />
      {openCreateCustomers && (
        <SyncWithPolicies
          open={openCreateCustomers}
          onClose={() => setOpenCreateCustomers(false)}
        />
      )}
    </>
  );
};
