import { Add, Download, ExpandLess, ExpandMore } from '@mui/icons-material';
import {
  Box,
  Button,
  Chip,
  IconButton,
  Typography,
  useMediaQuery,
} from '@mui/material';
import { SystemRoles } from 'common/globalTypes';
import { numberOrDefault } from 'common/helpers';
import { isEqual } from 'lodash-es';
import PropTypes from 'prop-types';
import {
  useCallback,
  useContext,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';
import { useSearchParams } from 'react-router-dom';

import DataBulkAdd from '@/components/DataBulkAdd';
import DataBulkAddCompGrids from '@/components/DataBulkAdd/DataBulkAddCompGrids';
import DataForm from '@/components/DataForm';
import LoadingCircle from '@/components/atoms/LoadingCircle';
import { EnhancedSelect } from '@/components/molecules/EnhancedSelect';
import EnhancedTable from '@/components/molecules/EnhancedTable';
import SearchBox from '@/components/molecules/SearchBox';
import SplitButton from '@/components/molecules/SplitButton';
import { LoadingContext } from '@/contexts/LoadingContext';
import useSnackbar from '@/contexts/useSnackbar';
import { auth } from '@/firebase';
import API from '@/services/API';
import { exportToCsv } from '@/services/helpers';
import { useRoleStore } from '@/store';
import { Roles } from '@/types';
import { useFilterParams } from './hooks/useFilterParams';

/**
 * DataView Component
 *
 * A versatile component for displaying, editing, and managing data in various modes such as list, form, and bulk add.
 * It supports pagination, filtering, exporting, and custom actions.
 *
 * @param {Object} props - Component properties.
 * @param {Object} props.dataDesc - Description of the data structure, including fields, filters and actions.
 * @param {boolean} [props.formModeOnly=false] - If true, displays the form mode only.
 * @param {boolean} [props.viewOnly=false] - If true, disables editing and other actions.
 * @param {boolean} [props.hideExport=false] - If true, hides the export functionality.
 * @param {number} [props.refresh] - Triggers a refresh when changed (typically start with 0 and increment to refresh, but can be any value change).
 * @param {React.ReactNode} [props.extraActions=null] - Additional actions to display in the header.
 * @param {Function} [props.dataCallback=null] - Callback function triggered after data is saved or updated.
 * @param {Object} [props.sx={}] - Custom styles for the component.
 * @param {boolean} [props.enableAutoRefetch=true] - If true, enables automatic refetching of data.
 * @param {boolean} [props.embed=false] - If true, enables embedded mode.
 * @param {boolean} [props.readOnly=false] - If true, disables editing functionality.
 * @param {Array<Object>} [props.exportOptions=[]] - Options for exporting data.
 * @param {boolean} [props.customExport=false] - If true, enables custom export functionality.
 * @param {Function} [props.customExportCallback=() => {}] - Callback function for custom export logic.
 * @param {Function} [props.handleCustomExportOptions=null] - Function to handle custom export options.
 * @param {string} [props.rowKey=''] - Key used to uniquely identify rows.
 * @param {Function} [props.setSelectedData=(a) => {}] - Function to set selected data.
 * @param {Function|null} [props.onDelete=null] - Callback function triggered on delete action.
 * @param {Function} [props.onBulkEdit=() => {}] - Callback function triggered on bulk edit action.
 * @param {boolean} [props.customHeaderActions=false] - If true, enables custom header actions.
 * @param {boolean} [props.enablePagination=false] - If true, enables pagination functionality.
 * @param {Array<string>} [props.customQueryParams=[]] - Custom query parameters for API requests.
 * @param {number} [props.headingOffset=98] - Offset for the heading in pixels.
 * @param {string} [props.variant=''] - Variant of the component.
 * @param {boolean} [props.saveEditableFieldsOnly=false] - If true, saves only editable fields.
 *
 * @returns {JSX.Element} The rendered DataView component.
 */
const DataView = ({
  dataDesc,
  formModeOnly = false,
  viewOnly = false,
  hideExport,
  refresh,
  extraActions,
  dataCallback,
  sx = {},
  enableAutoRefetch = true,
  embed = false,
  readOnly = false,
  exportOptions = [],
  // Use them to trigger a callback in the partent component with any custom export logic
  customExport = false,
  customExportCallback = () => {},
  handleCustomExportOptions,
  rowKey = '',
  setSelectedData = (a) => {},
  onDelete = null,
  onBulkEdit = () => {},
  customHeaderActions = false,
  enablePagination = false,
  customQueryParams = [],
  headingOffset = 98,
  variant = '',
  saveEditableFieldsOnly = false,
}) => {
  const { showSnackbar } = useSnackbar();

  const { filterParams } = useFilterParams({
    filters: dataDesc.filters,
  });

  const isMobile = useMediaQuery('(max-width:600px)');
  const [searchParams, setSearchParams] = useSearchParams({});
  const mode = searchParams.get('m') ?? 'list';
  const embedMode = searchParams.get('em') ?? 'list';
  const [newData, setNewData] = useState({});
  const [oldData, setOldData] = useState({});

  // Can put editingData and originData in the same state
  const [editingData, setEditingData] = useState({});
  const [originData, setOriginData] = useState({});

  const [isDownloading, setIsDownloading] = useState(false);
  const [qcExpanded, setQcExpanded] = useState(false);
  const [selectedExportOptions, setSelectedExportOptions] = useState({});
  const { userRole } = useRoleStore();

  const orderBy = searchParams.get('order_by') ?? 'created_at';
  const order = searchParams.get('order') ?? 'desc';
  const page = numberOrDefault(searchParams.get('page'), 0);
  const rowsPerPage = numberOrDefault(searchParams.get('limit'), 50);

  // const [additionalQueryParams, setParams] = useState({})

  const handleOnSave = async () => {
    let dbData = null;
    const dataDescFields = dataDesc.fields.flat();
    const filteredNewData = saveEditableFieldsOnly
      ? Object.fromEntries(
          Object.entries(newData).filter(([key]) =>
            dataDescFields.some((field) => field.id === key || key === 'id')
          )
        )
      : newData;

    const normalizedNewData = Object.fromEntries(
      Object.entries(filteredNewData).map(([key, value]) => [
        key,
        dataDescFields.find((field) => field.id === key)?.normalizer
          ? dataDescFields.find((field) => field.id === key).normalizer(value)
          : value,
      ])
    );

    if (normalizedNewData.id) {
      dbData = await patcher.mutateAsync(normalizedNewData);
    } else {
      dbData = await poster.mutateAsync(normalizedNewData);
    }

    if (dbData.error) {
      showSnackbar(dbData.error, 'error');
      return;
    }

    if (dataCallback) await dataCallback(dbData);
    setNewData({});
    setEditingData({});
    updateSearchParams(embed ? { em: null } : { m: null, id: null });

    if (embed) {
      setEmbededId(null);
    }

    setTimeout(refetch, 200);
    return dbData;
  };

  let additionalQueryParams = filterParams;
  const idParam = searchParams.get('id');

  const [embededId, setEmbededId] = useState(null);

  Array.from(searchParams.entries()).forEach(([key, value]) => {
    additionalQueryParams += `&${key}=${value}`;
  });

  if (enablePagination) {
    if (!additionalQueryParams.includes('page'))
      additionalQueryParams += `&page=${page}`;
    if (!additionalQueryParams.includes('limit'))
      additionalQueryParams += `&limit=${rowsPerPage}`;
    additionalQueryParams += `&orderBy=${orderBy}&sort=${order}`;
  }

  if (dataDesc.queryChips && searchParams.get('qc')) {
    const chipQuery =
      dataDesc.queryChips[searchParams.get('qc') || '']?.query ?? {};
    Object.entries(chipQuery).forEach(([k, v]) => {
      if (v instanceof Array) {
        v.forEach((e) => {
          additionalQueryParams += `&${k}=${encodeURIComponent(e)}`;
        });
      } else {
        additionalQueryParams += `&${k}=${encodeURIComponent(v)}`;
      }
    });
  }
  customQueryParams.forEach((param) => {
    if (searchParams.get(param))
      additionalQueryParams += `&${param}=${searchParams.get(param)}`;
  });

  const searchQuery = searchParams.get('q');
  const refPrevSearchQuery = useRef(searchQuery);

  useEffect(() => {
    if (searchQuery !== refPrevSearchQuery.current) {
      refPrevSearchQuery.current = searchQuery;
      setSearchParams((prev) => {
        prev.delete('page');
        return prev;
      });
    }
  }, [searchQuery, setSearchParams]);

  useEffect(() => {
    const params = new URLSearchParams(additionalQueryParams);

    params.set('page', page?.toString() || '');
    params.set('limit', rowsPerPage?.toString() || '');

    /*
     * Lint disabled due to uncertain impact on adding missed references.
     *
     * WARN SINCE:  March/2024
     * MISSED REFs: 'additionalQueryParams'
     */
    // eslint-disable-next-line react-hooks/exhaustive-deps
    additionalQueryParams = `?${params.toString()}`;
  }, [page, rowsPerPage]);

  let { isLoading, data, refetch } = API.getBasicQuery(
    dataDesc.table,
    additionalQueryParams,
    enableAutoRefetch
  );

  let count = 0;
  if (enablePagination) {
    count = data?.count;
    data = data?.data;
  }

  useEffect(() => {
    if (!isEqual(newData, editingData) && isEqual(newData, originData)) {
      setNewData({ ...newData, ...editingData });
    }
    /*
     * Lint disabled due to uncertain impact on adding missed references.
     *
     * WARN SINCE:  Jul/2024
     * MISSED REFs: 'editingData' and 'newData'
     */
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [originData]);

  const handleChangePage = async (event, newPage) => {
    setSearchParams((prev) => {
      if (newPage && +newPage > 0) {
        prev.set('page', newPage);
      } else {
        prev.delete('page');
      }
      return prev;
    });
  };

  const handleChangeRowsPerPage = (event) => {
    setSearchParams((prev) => {
      prev.delete('page');
      prev.set('limit', event.target.value);
      return prev;
    });
  };

  const { setLoadingConfig } = useContext(LoadingContext);

  const updateSearchParams = (kvMap) =>
    setSearchParams((prev) => {
      Object.entries(kvMap).forEach(([k, v]) => {
        if ([undefined, null, ''].includes(v)) {
          prev.delete(k);
        } else {
          prev.set(k, v);
        }
      });
      return prev;
    });

  useEffect(() => {
    let _data;
    let _count;

    if (enablePagination) {
      _data = data?.data;
      _count = data?.count;
    } else {
      _data = data;
    }

    if (formModeOnly) {
      const defaultData =
        _data ||
        dataDesc.fields
          .filter((field) => field.default !== undefined)
          .reduce((acc, cur) => ({ ...acc, [cur.id]: cur.default }), {});
      setNewData(defaultData);
      setOldData(JSON.parse(JSON.stringify(defaultData)));
    }

    if (_count !== undefined) {
      /*
       * Lint disabled due to uncertain impact on adding missed references.
       *
       * WARN SINCE:  Marc/2024
       * MISSED REFs: '.current' property
       */
      // eslint-disable-next-line react-hooks/exhaustive-deps
      count = _count;
    }
  }, [data, dataDesc.fields, formModeOnly, enablePagination]);

  useEffect(() => {
    if (
      mode === 'edit' &&
      idParam &&
      Array.isArray(data) &&
      data.length === 1
    ) {
      setNewData(data[0]);
      setOriginData(data[0]);
      setOldData(JSON.parse(JSON.stringify(data[0])));
    }
  }, [data, mode, idParam]);

  useEffect(() => {
    if (refresh) {
      setTimeout(refetch, 100);
    }
  }, [refresh, refetch]);

  const poster = API.getMutation(dataDesc.table, 'POST');

  const apiPath = useMemo(() => {
    let url = dataDesc.table;
    if (url.includes('?')) {
      url = url.split('?')[0];
    }
    return url;
  }, [dataDesc.table]);
  const posterBulk = API.getMutation(`${apiPath}/bulk_add`, 'POST');
  const patcher = API.getMutation(dataDesc.table, 'PATCH');
  const deleter = API.getMutation(dataDesc.table, 'DELETE');

  /**
   * Download CSV
   */
  const downloadCsvFn = useCallback(async () => {
    const idToken = (await auth.currentUser?.getIdToken(true)) || '';

    const searchParamsWithOption = new URLSearchParams(additionalQueryParams);
    if (selectedExportOptions) {
      Object.entries(selectedExportOptions).forEach(([k, v]) => {
        searchParamsWithOption.set(k, v);
      });
    }
    await exportToCsv(
      {
        extraParams: searchParamsWithOption,
      },
      {
        idToken,
        endpoint: dataDesc.table,
        exportOptions: selectedExportOptions,
      }
    );
  }, [dataDesc.table, selectedExportOptions, additionalQueryParams]);

  useEffect(() => {
    const doDownload = async () => {
      try {
        await downloadCsvFn();
      } catch (e) {
        console.error(e);
        showSnackbar('Error exporting data', 'error');
      } finally {
        setIsDownloading(false);
        setLoadingConfig({
          loading: false,
        });
      }
    };
    if (isDownloading) {
      doDownload();
    }
  }, [downloadCsvFn, isDownloading, setLoadingConfig, showSnackbar]);

  let queryFields = dataDesc.fields
    .flat()
    .filter((field) => field.queryable && field.type !== 'boolean')
    .map((field) => field.id);
  queryFields =
    queryFields.length > 0
      ? queryFields
      : dataDesc.fields
          .filter((field) => field.type !== 'boolean')
          .map((field) => field.id);

  const dataFiltered = Array.isArray(data)
    ? data?.filter((datum) => {
        let query = searchParams.get('q');
        if (enablePagination) query = null;
        if (!query) return true;
        return Object.entries(datum)
          .filter(([k, v]) => queryFields.includes(k))
          .some(([_key, value]) =>
            (typeof value === 'object'
              ? JSON.stringify(value ?? {})
              : (value ?? '').toString()
            )
              .toLowerCase()
              .includes(query.trim().toLowerCase())
          );
      })
    : data;
  const DataFormer = (
    <DataForm
      dataDesc={dataDesc}
      fields={dataDesc.fields.filter((field) =>
        field.condition ? field.condition(newData) : true
      )}
      newData={newData}
      oldData={oldData}
      setNewData={(data) => {
        setNewData(data);
        setEditingData(data);
      }}
      onCancel={() => {
        setNewData({});
        setEditingData({});
        updateSearchParams(embed ? { em: null } : { m: null, id: null });
        if (embed) {
          setEmbededId(null);
        }
      }}
      onSave={handleOnSave}
      onDelete={async () => {
        try {
          await deleter.mutateAsync({
            ids: ['id' in newData ? newData.id : ''],
            strId: 'str_id' in newData ? newData.str_id : '',
          });
          setNewData({});
          setEditingData({});
          updateSearchParams(embed ? { em: null } : { m: null, id: null });
          if (embed) {
            setEmbededId(null);
          }
        } catch (e) {
          console.error(e);
          showSnackbar(
            `Error deleting data: ${typeof e === 'object' ? (e && 'message' in e ? e?.message : '') : ''}`,
            'error'
          );
        } finally {
          setTimeout(refetch, 200);
        }
      }}
      validateData={dataDesc.validateData}
      formModeOnly={formModeOnly}
      embed={embed}
      readOnly={readOnly}
    />
  );

  const DataBulkAdder = (
    <DataBulkAdd
      fields={dataDesc.fields
        .flat()
        .filter(
          (field) =>
            [undefined, 'select', 'dynamic-select', 'boolean', 'date'].includes(
              field.type
            ) &&
            field.access !== SystemRoles.ADMIN &&
            !field.readOnly &&
            !field.bulkAddUnsupported
        )}
      btnLabel=""
      onCancel={() => {
        updateSearchParams(embed ? { em: null } : { m: null });
      }}
      onSave={async (jsonEntities) => {
        const res = await posterBulk.mutateAsync(jsonEntities);
        if (res.statusText === 'ok') {
          showSnackbar(
            `Added ${res?.stats?.current_length} records`,
            'success'
          );
          updateSearchParams(embed ? { em: null } : { m: null });
          setTimeout(refetch, 200);
        } else {
          showSnackbar('Error bulk adding data', 'error');
        }
      }}
    />
  );
  const DataBulkAdderCompGrids = (
    <DataBulkAddCompGrids
      fields={dataDesc.fields
        .flat()
        .filter(
          (field) =>
            [undefined, 'select', 'dynamic-select', 'boolean', 'date'].includes(
              field.type
            ) &&
            field.access !== SystemRoles.ADMIN &&
            !field.readOnly &&
            !field.bulkAddUnsupported
        )}
      onCancel={() => {
        updateSearchParams(embed ? { em: null } : { m: null });
      }}
      onSave={async (jsonEntities) => {
        const res = await posterBulk.mutateAsync(jsonEntities);
        if (res.statusText === 'ok') {
          showSnackbar(
            `Added ${res?.stats?.current_length} records`,
            'success'
          );
          updateSearchParams(embed ? { em: null } : { m: null });
          setTimeout(refetch, 200);
        } else {
          showSnackbar(
            `Error bulk adding records ${res.error ? ` (${res.error}})` : ''}`,
            'error'
          );
        }
      }}
    />
  );

  const onExport = useCallback(
    (options) => {
      setSelectedExportOptions(options);
      setIsDownloading(true);
      setLoadingConfig({
        loading: true,
        message: 'Exporting...',
      });
    },
    [setLoadingConfig, setSelectedExportOptions]
  );

  useEffect(() => {
    if (customExport && handleCustomExportOptions) {
      handleCustomExportOptions(onExport);
    }
  }, [handleCustomExportOptions, customExport, onExport]);

  const getValues = (paramKey, options) => {
    const paramValues = searchParams.getAll(paramKey) || [];
    // Unselect all
    if (paramValues.length === 1 && paramValues[0] === 'undefined') {
      return [];
    }

    return paramValues.length
      ? paramValues
          .map((val) => options.find((option) => String(option.id) === val))
          .filter((item) => !!item)
      : options;
  };

  const updatedExportOptions = useMemo(() => {
    const dataExportOptions = exportOptions.length
      ? exportOptions
      : [
          {
            id: 'export',
            label: 'Export',
            key: 'export',
          },
        ];

    return [
      ...dataExportOptions,
      {
        id: 'export-relationships',
        label: 'Export with IDs',
        options: { is_export_relationship: true },
      },
    ].map((e) => ({
      ...e,
      onClick: () => onExport(e.options),
      key: e?.id || Date.now(),
    }));
    /*
     * Lint disabled due to uncertain impact on adding missed references.
     *
     * WARN SINCE:  March/2024
     * MISSED REFs: 'onExport'
     */
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [exportOptions]);

  return (
    <Box
      sx={{
        ...{
          width: formModeOnly
            ? 'inherit'
            : `calc(100vw - ${isMobile ? '0px' : '200px'})`,
          overflowY: embed ? 'auto' : 'scroll',
        },
        ...sx,
      }}
    >
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'column',
          pt: embed ? 0 : 2,
          px: 2,
          pb: embed ? 0 : 1,
        }}
      >
        <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
          <Typography variant="h5">
            {!['tabbed'].includes(variant) && dataDesc.label}
          </Typography>
          {mode !== 'add' && !formModeOnly && (
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              {(idParam || embededId) && (
                <Chip
                  label={!embed ? idParam : embededId}
                  sx={{ mr: 1 }}
                  onDelete={() => {
                    setNewData({});
                    updateSearchParams(
                      embed ? { em: null } : { m: null, id: null }
                    );
                    if (embed) {
                      setEmbededId(null);
                    }
                  }}
                  // variant="outlined"
                  color="primary"
                />
              )}
              <SearchBox id={dataDesc.table} />
            </Box>
          )}
        </Box>
        {!['add', 'bulkAdd'].includes(mode) && !formModeOnly && (
          <Box className="mt-2 flex justify-between items-start">
            <Box
              id="filter-chips"
              sx={{ display: 'flex', flexDirection: 'row', gap: 1 }}
            >
              {Object.keys(dataDesc?.filterConfigs ?? {})?.length > 0
                ? Object.entries(dataDesc?.filterConfigs ?? {}).map(
                    ([filterId, filterConfig]) => {
                      const options = Object.values(filterConfig.options ?? {});
                      return (
                        <EnhancedSelect
                          sx={{ minWidth: 130, width: 'fit-content' }}
                          key={`filter-${filterId}`}
                          label={filterConfig.label}
                          enableSearch
                          multiple
                          options={options}
                          value={getValues(filterId, options)}
                          onChange={(values) => {
                            const ids = values.map((item) => item.id);
                            // Select all
                            if (!values.length) {
                              setSearchParams((prev) => {
                                prev.set(filterId, 'undefined');
                                return prev;
                              });
                            } else {
                              setSearchParams((prev) => {
                                prev.delete(filterId);
                                // If select all values, we remove params on the url instead
                                if (ids.length !== options.length) {
                                  ids.forEach((v) => {
                                    prev.append(filterId, v);
                                  });
                                }
                                return prev;
                              });
                            }
                          }}
                        />
                      );
                    }
                  )
                : null}

              {Object.values(dataDesc.queryChips ?? {})
                ?.filter((val, i) => qcExpanded || i < 10)
                .sort((a, b) =>
                  ['all', 0, '0'].includes(a.id) || a.label === 'All'
                    ? -1
                    : ['all', 0, '0'].includes(b.id) || b.label === 'All'
                      ? 1
                      : (a.label ?? '').localeCompare(b.label ?? '')
                )
                .map((chip) => (
                  <Chip
                    key={chip.id}
                    label={chip.label}
                    onClick={() => {
                      // Restart pagination when changing query chips
                      setSearchParams((prev) => {
                        if (['all', 0, '0'].includes(chip.id)) {
                          prev.delete('qc');
                        } else {
                          prev.set('qc', chip.id);
                        }
                        return prev;
                      });
                    }}
                    sx={{
                      mr: 0.5,
                      my: 0.25,
                      cursor: 'pointer',
                    }}
                    color={
                      searchParams.get('qc') === chip.id ||
                      (!searchParams.get('qc') &&
                        ['all', 0, '0'].includes(chip.id))
                        ? 'primary'
                        : 'default'
                    }
                    variant={
                      searchParams.get('qc') === chip.id ||
                      (!searchParams.get('qc') &&
                        ['all', 0, '0'].includes(chip.id))
                        ? 'filled'
                        : 'outlined'
                    }
                  />
                ))}
              {dataDesc.queryChipsType !== 'select' &&
                Object.values(dataDesc.queryChips ?? {})?.length > 10 && (
                  <IconButton onClick={() => setQcExpanded(!qcExpanded)}>
                    {qcExpanded ? (
                      <ExpandLess sx={{ width: 16, height: 16 }} />
                    ) : (
                      <ExpandMore sx={{ width: 16, height: 16 }} />
                    )}
                  </IconButton>
                )}
              {dataDesc.filters?.map((filter) => {
                const options = filter.options.sort((a, b) =>
                  a.label === 'All'
                    ? -1
                    : b.label === 'All'
                      ? 1
                      : (a.label ?? '').localeCompare(b.label ?? '')
                );
                return (
                  <EnhancedSelect
                    enableSelectAllSearchResult
                    sortLabel={false}
                    key={filter.apiParamKey}
                    label={filter.label}
                    options={options}
                    value={{
                      id: searchParams.get(filter.apiParamKey),
                      label:
                        options.find(
                          (option) =>
                            option.value ===
                            searchParams.get(filter.apiParamKey)
                        )?.label ?? 'All',
                    }}
                    onChange={(value) => {
                      setSearchParams((prev) => {
                        if (['all', 0, '0'].includes(String(value.id))) {
                          prev.delete(filter.apiParamKey);
                        } else {
                          prev.set(filter.apiParamKey, String(value.id));
                        }
                        return prev;
                      });
                    }}
                  />
                );
              })}
            </Box>
            <Box
              sx={{
                display: 'flex',
                flexDirection: 'row',
                alignItems: 'center',
              }}
            >
              <Box
                sx={{
                  display: 'flex',
                  alignItems: 'center',
                }}
              >
                {extraActions ? <Box sx={{ ml: 1 }}>{extraActions}</Box> : null}
                {!(viewOnly || formModeOnly) && (
                  <SplitButton
                    startIcon={<Add />}
                    options={
                      dataDesc.bulkAdd &&
                      userRole &&
                      [Roles.ACCOUNT_ADMIN, Roles.DATA_SPECIALIST].includes(
                        userRole
                      )
                        ? [
                            {
                              id: 'add',
                              label: 'Add',
                              onClick: () => {
                                setNewData({});
                                updateSearchParams({ m: 'add' });
                              },
                            },
                            {
                              id: 'bulkAdd',
                              label: 'Bulk add',
                              onClick: () =>
                                updateSearchParams({ m: 'bulkAdd' }),
                            },
                          ]
                        : [
                            {
                              id: 'add',
                              label: 'Add',
                              onClick: () => {
                                setNewData({});
                                updateSearchParams({ m: 'add' });
                              },
                            },
                          ]
                    }
                    disabled={['add', 'edit'].includes(mode)}
                    variant="contained"
                  />
                )}
              </Box>
              {!hideExport && (
                <Box sx={{ ml: 1 }}>
                  {customExport ? (
                    <>
                      {/* Used to trigger a callback in the partent component with any custom export logic */}
                      <Button
                        onClick={() => customExportCallback()}
                        variant="outlined"
                      >
                        Export
                      </Button>
                    </>
                  ) : (
                    <SplitButton
                      startIcon={<Download />}
                      options={updatedExportOptions}
                      disabled={isDownloading}
                    />
                  )}
                </Box>
              )}
            </Box>
          </Box>
        )}
      </Box>
      <Box>
        {(embed
          ? ['add', 'embed-edit'].includes(embedMode)
          : ['add', 'edit'].includes(mode)) || formModeOnly ? (
          <Box
            sx={{
              overflowY: embed ? 'auto' : 'scroll',
              height: embed
                ? 'inherit'
                : `calc(100vh - ${(headingOffset ?? 0) + 74}px)`,
              display: 'flex',
              justifyContent: 'center',
              py: embed ? 0 : 2,
              px: 2,
            }}
          >
            {DataFormer}
          </Box>
        ) : mode === 'bulkAdd' ? (
          <Box
            sx={{
              overflowY: 'scroll',
              height: embed ? 'inherit' : 'calc(100vh - 168px)',
              display: 'flex',
              justifyContent: 'center',
              px: 2,
              pb: 2,
            }}
          >
            {dataDesc.label !== 'Agent commission schedule profiles'
              ? DataBulkAdder
              : DataBulkAdderCompGrids}
          </Box>
        ) : isLoading ? (
          <LoadingCircle />
        ) : (
          <>
            <EnhancedTable
              dense
              headers={dataDesc.fields}
              rows={dataFiltered}
              rowKey={rowKey}
              onEdit={
                dataDesc.editable
                  ? (row) => {
                      updateSearchParams(
                        embed
                          ? { em: 'embed-edit' }
                          : { m: 'edit', id: row.str_id }
                      );
                      if (embed) {
                        setEmbededId(row.str_id);
                      }
                      setNewData(row);
                      setOldData(JSON.parse(JSON.stringify(row)));
                    }
                  : null
              }
              actionsEnabled={() =>
                Array.isArray(dataDesc.actions) && dataDesc.actions.length > 0
              }
              actions={dataDesc.actions}
              refresh={refresh}
              refetch={refetch}
              setSelectedData={setSelectedData}
              onDelete={onDelete}
              onBulkEdit={onBulkEdit}
              customHeaderActions={customHeaderActions}
              paginated={enablePagination}
              headingOffset={headingOffset}
              controlledOrdering={{
                order,
                orderBy,
                setOrder: (e) => {
                  setSearchParams((prev) => {
                    prev.delete('page');
                    prev.set('order', e);
                    return prev;
                  });
                },
                setOrderBy: (e) => {
                  setSearchParams((prev) => {
                    prev.delete('page');
                    prev.set('order_by', e);
                    return prev;
                  });
                },
              }}
              {...(enablePagination
                ? {
                    controlledPagination: {
                      count,
                      page,
                      onPageChange: handleChangePage,
                      rowsPerPage,
                      onRowsPerPageChange: handleChangeRowsPerPage,
                    },
                  }
                : {})}
            />
          </>
        )}
      </Box>
    </Box>
  );
};

DataView.propTypes = {
  dataDesc: PropTypes.object,
  formModeOnly: PropTypes.bool,
  viewOnly: PropTypes.bool,
  hideExport: PropTypes.bool,
  refresh: PropTypes.number,
  extraActions: PropTypes.node,
  dataCallback: PropTypes.func,
  onDelete: PropTypes.oneOfType([PropTypes.func, PropTypes.oneOf([null])]),
  handleCustomExportOptions: PropTypes.func,
};

export default DataView;
