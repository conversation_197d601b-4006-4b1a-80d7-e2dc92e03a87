import { FunctionComponent, useState } from 'react';
import {
  Box,
  Button,
  Divider,
  Link,
  Paper,
  TextField,
  Typography,
} from '@mui/material';
import GoogleIcon from '@mui/icons-material/Google';
import { z } from 'zod';
import { useNavigate } from 'react-router-dom';

import LandingImg from '@/illustrations/landing_tp.png';
import authentication from '@/services/authentication';

type InputEvent = React.ChangeEvent<HTMLInputElement>;
type FormEvent = React.FormEvent<HTMLFormElement>;

const emailSchema = z.object({
  emailAddress: z
    .string()
    .email({ message: 'Please enter a valid email address.' }),
});

const signInSchema = z.object({
  emailAddress: z
    .string()
    .email({ message: 'Please enter a valid email address.' }),
  password: z.string().min(1, { message: 'Password is required.' }),
});

export const SignIn: FunctionComponent = () => {
  const [isPerformingAction, setIsPerformingAction] = useState(false);
  const [emailAddress, setEmailAddress] = useState('');
  const [password, setPassword] = useState('');
  const [errorMessage, setErrorMessage] = useState<string>('');
  const [successMessage, setSuccessMessage] = useState<string>('');
  const navigate = useNavigate();

  const updateSuccessMessage = (message: string) => {
    setErrorMessage('');
    setSuccessMessage(message);
  };

  const updateErrorMessage = (error: unknown | unknown[]) => {
    const possibleError = Array.isArray(error) ? error[0] : error;
    setSuccessMessage('');

    if (possibleError instanceof Error) {
      setErrorMessage(possibleError.message.replace('Firebase: ', ''));
      return;
    }
    if (typeof possibleError === 'string') {
      setErrorMessage(possibleError);
      return;
    }
    setErrorMessage('An error has occurred.');
  };

  const resetMessageState = () => {
    setErrorMessage('');
    setSuccessMessage('');
  };

  const signIn = async () => {
    const result = signInSchema.safeParse({ emailAddress, password });
    if (!result.success) {
      const firstError = Object.values(
        result.error.flatten().fieldErrors
      )[0]?.[0];
      updateErrorMessage(firstError || 'Invalid input.');
      return;
    }

    setIsPerformingAction(true);
    resetMessageState();
    try {
      const user = await authentication.signIn(emailAddress, password);
      updateSuccessMessage(`Signed in as ${user.email}`);
      navigate('/');
    } catch (err: unknown) {
      updateErrorMessage(err);
    } finally {
      setIsPerformingAction(false);
    }
  };

  const sendSignInLinkToEmail = async () => {
    const result = emailSchema.safeParse({ emailAddress });
    if (!result.success) {
      const firstError = result.error.flatten().fieldErrors.emailAddress?.[0];
      updateErrorMessage(firstError || 'Invalid email address.');
      return;
    }

    setIsPerformingAction(true);
    resetMessageState();

    try {
      await authentication.signInLinkToEmail(emailAddress);
      setSuccessMessage(`Sent sign-in email to ${emailAddress}`);
    } catch (err: unknown) {
      updateErrorMessage(err);
    } finally {
      setIsPerformingAction(false);
    }
  };

  const signInWithGoogle = async () => {
    setIsPerformingAction(true);
    resetMessageState();

    try {
      const user = await authentication.signInWithAuthProvider({
        id: 'google.com',
        name: 'Google',
      });
      setSuccessMessage(`Signed in as ${user.email}`);
      navigate('/');
    } catch (err: unknown) {
      updateErrorMessage(err);
    } finally {
      setIsPerformingAction(false);
    }
  };

  const handlePasswordChange = (event: InputEvent) => {
    setPassword(event.target.value);
  };

  const handleEmailChange = (event: InputEvent) => {
    setEmailAddress(event.target.value);
  };

  const resetPassword = async () => {
    resetMessageState();
    const result = emailSchema.safeParse({ emailAddress });
    if (!result.success) {
      const firstError = result.error.flatten().fieldErrors.emailAddress?.[0];
      updateErrorMessage(firstError || 'Invalid email address.');
      return;
    }
    setIsPerformingAction(true);
    try {
      await authentication.resetPassword(emailAddress);
      setSuccessMessage(`Sent password reset e-mail to ${emailAddress}`);
    } catch (err: unknown) {
      updateErrorMessage(err);
    } finally {
      setIsPerformingAction(false);
    }
  };

  const hasPassword = password.trim().length > 0;
  const primaryAction = hasPassword ? signIn : sendSignInLinkToEmail;
  const primaryActionText = hasPassword ? 'Sign In' : 'Send sign-in link';

  const handleSubmit = async (event: FormEvent) => {
    event.preventDefault();
    await primaryAction();
  };

  return (
    <Box
      sx={{
        minHeight: '100vh',
        backgroundColor: 'grey.100',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        padding: { xs: 2, sm: 3 },
      }}
    >
      <Paper
        elevation={3}
        sx={{
          maxWidth: '1280px',
          m: { xs: 0, sm: '2.5rem' },
          bgcolor: 'background.paper',
          boxShadow: 1,
          borderRadius: { xs: 0, sm: '0.5rem' },
          display: 'flex',
          flex: 1,
        }}
      >
        <Box
          sx={{
            width: { xs: '100%', lg: '40%' },
            p: { xs: 3, sm: 6 },
            display: 'flex',
            flexDirection: 'column',
            justifyContent: 'center',
          }}
        >
          <Box sx={{ textAlign: 'center', mb: 4 }}>
            <Box
              sx={{
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                gap: 2,
              }}
            >
              <Box
                component="img"
                src="/logo192.png"
                alt="Fintary Logo"
                sx={{ width: 64, height: 64 }}
              />
              <Typography variant="h4" component="h1" fontWeight="600">
                Fintary
              </Typography>
            </Box>
            <Typography
              variant="subtitle1"
              color="text.secondary"
              sx={{ mt: 1 }}
            >
              One place for all your financial operations
            </Typography>
          </Box>

          <Button
            variant="outlined"
            fullWidth
            startIcon={<GoogleIcon />}
            sx={{
              py: 1.5,
              textTransform: 'none',
              fontWeight: 'bold',
              color: 'text.primary',
              borderColor: 'grey.300',
              ':hover': {
                borderColor: 'grey.400',
                backgroundColor: 'grey.50',
              },
            }}
            onClick={signInWithGoogle}
            disabled={isPerformingAction}
          >
            Sign in with Google
          </Button>

          <Divider sx={{ my: 3 }}>
            <Typography variant="body2" color="text.secondary">
              Or sign in with e-mail
            </Typography>
          </Divider>

          <Box component="form" noValidate onSubmit={handleSubmit}>
            <TextField
              margin="normal"
              required
              fullWidth
              id="emailAddress"
              label="E-mail Address"
              name="emailAddress"
              autoComplete="email"
              autoFocus
              value={emailAddress}
              onChange={handleEmailChange}
              disabled={isPerformingAction}
            />
            <TextField
              margin="normal"
              fullWidth
              name="password"
              label="Password"
              type="password"
              id="password"
              autoComplete="current-password"
              value={password}
              onChange={handlePasswordChange}
              disabled={isPerformingAction}
            />
            <Box sx={{ textAlign: 'right', mt: 1 }}>
              <Link
                component="button"
                type="button"
                variant="body2"
                onClick={resetPassword}
                disabled={isPerformingAction || !emailAddress}
              >
                Forgot password?
              </Link>
            </Box>
            <Button
              type="submit"
              fullWidth
              variant="contained"
              sx={{ mt: 3, mb: 2, py: 1.5 }}
              disabled={isPerformingAction || !emailAddress}
            >
              {primaryActionText}
            </Button>
            {errorMessage && (
              <Typography color="error" align="center" sx={{ mt: 2 }}>
                {errorMessage}
              </Typography>
            )}
            {successMessage && (
              <Typography color="primary.main" align="center" sx={{ mt: 2 }}>
                {successMessage}
              </Typography>
            )}
          </Box>
        </Box>
        <Box
          sx={{
            display: { xs: 'none', lg: 'block' },
            width: '60%',
            position: 'relative',
          }}
        >
          <Box
            component="img"
            src={LandingImg}
            alt="Financial operations illustration"
            sx={{
              position: 'absolute',
              top: 0,
              left: 0,
              width: '100%',
              height: '100%',
              objectFit: 'cover',
              borderTopRightRadius: { sm: '0.5rem' },
              borderBottomRightRadius: { sm: '0.5rem' },
            }}
          />
        </Box>
      </Paper>
    </Box>
  );
};
