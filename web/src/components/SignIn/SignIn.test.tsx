import { render, fireEvent, waitFor, screen } from '@testing-library/react';
import { ThemeProvider } from '@mui/material';
import { createTheme } from '@mui/material/styles';
import { MemoryRouter } from 'react-router-dom';

import authentication from '@/services/authentication';
import { SignIn } from './SignIn';

jest.mock('@/services/authentication');

const mockedNavigate = jest.fn();
jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useNavigate: () => mockedNavigate,
}));

describe('SignIn', () => {
  const theme = createTheme({});

  beforeEach(() => {
    jest.clearAllMocks();
  });

  const renderComponent = () =>
    render(
      <ThemeProvider theme={theme}>
        <MemoryRouter>
          <SignIn />
        </MemoryRouter>
      </ThemeProvider>
    );

  it('Given the SignIn component is rendered, then it should display the email, password, Google sign-in, and send sign-in link fields', () => {
    renderComponent();
    expect(screen.getByLabelText(/e-mail address/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/password/i)).toBeInTheDocument();
    expect(
      screen.getByRole('button', { name: /sign in with google/i })
    ).toBeInTheDocument();
    expect(
      screen.getByRole('button', { name: /send sign-in link/i })
    ).toBeInTheDocument();
  });

  it('Given an invalid email is entered, when the send sign-in link button is clicked, then the primary action button should be disabled and signInLinkToEmail should not be called', () => {
    renderComponent();
    const emailInput = screen.getByLabelText(/e-mail address/i);
    const primaryButton = screen.getByRole('button', {
      name: /send sign-in link/i,
    });

    fireEvent.change(emailInput, { target: { value: 'invalid-email' } });
    fireEvent.click(primaryButton);

    expect(authentication.signInLinkToEmail).not.toHaveBeenCalled();
  });

  it('Given email and password are provided, when the sign-in button is clicked, then signIn should be called and the user should be navigated to the home page', async () => {
    (authentication.signIn as jest.Mock).mockResolvedValue({
      email: '<EMAIL>',
    });
    renderComponent();

    fireEvent.change(screen.getByLabelText(/e-mail address/i), {
      target: { value: '<EMAIL>' },
    });
    fireEvent.change(screen.getByLabelText(/password/i), {
      target: { value: 'password123' },
    });

    fireEvent.click(screen.getByRole('button', { name: /^sign in$/i }));

    await waitFor(() => {
      expect(authentication.signIn).toHaveBeenCalledWith(
        '<EMAIL>',
        'password123'
      );
    });

    await waitFor(() => {
      expect(mockedNavigate).toHaveBeenCalledWith('/');
    });
  });

  it('Given only an email is provided, when the send sign-in link button is clicked, then sendSignInLinkToEmail should be called', async () => {
    renderComponent();

    fireEvent.change(screen.getByLabelText(/e-mail address/i), {
      target: { value: '<EMAIL>' },
    });

    fireEvent.click(screen.getByRole('button', { name: /send sign-in link/i }));

    await waitFor(() => {
      expect(authentication.signInLinkToEmail).toHaveBeenCalledWith(
        '<EMAIL>'
      );
    });
  });

  it('Given the Google sign-in button is clicked, when signInWithAuthProvider is called, then the user should be navigated to the home page', async () => {
    (authentication.signInWithAuthProvider as jest.Mock).mockResolvedValue({
      email: '<EMAIL>',
    });
    renderComponent();

    fireEvent.click(
      screen.getByRole('button', { name: /sign in with google/i })
    );

    await waitFor(() => {
      expect(authentication.signInWithAuthProvider).toHaveBeenCalledWith({
        id: 'google.com',
        name: 'Google',
      });
    });

    await waitFor(() => {
      expect(mockedNavigate).toHaveBeenCalledWith('/');
    });
  });

  it('Given an email is provided, when the forgot password link is clicked, then resetPassword should be called', async () => {
    renderComponent();

    fireEvent.change(screen.getByLabelText(/e-mail address/i), {
      target: { value: '<EMAIL>' },
    });

    fireEvent.click(screen.getByRole('button', { name: /forgot password/i }));

    await waitFor(() => {
      expect(authentication.resetPassword).toHaveBeenCalledWith(
        '<EMAIL>'
      );
    });
  });
});
