import {
  FieldMatchertDateOperatorOptionsLabels,
  FieldMatchertUnitOptions,
  FieldMatchertUnitOptionsLabels,
  FiltersOperators,
} from 'common/globalTypes';
import { Box, Checkbox, TextField } from '@mui/material';
import { useEffect, useState } from 'react';
import { FieldMatchertDateOperatorOptions } from 'common/globalTypes';

import { EnhancedSelect } from '@/components/molecules/EnhancedSelect';
import { DataTypes } from '@/types';

const isNumeric = (type: DataTypes) =>
  [
    DataTypes.NUMBER,
    DataTypes.CURRENCY,
    DataTypes.INTEGER,
    DataTypes.PERCENTAGE,
    DataTypes.DECIMAL,
  ].includes(type);

export const isString = (type: DataTypes) => [DataTypes.TEXT].includes(type);

const isDate = (type: DataTypes) => {
  return [DataTypes.DATE].includes(type);
};

const isJson = (type: DataTypes) => {
  return [DataTypes.JSON].includes(type);
};

const isArray = (type: DataTypes) => {
  return [DataTypes.ARRAY].includes(type);
};

const isNotWritingAgent = (label) => {
  const labels = ['Agents'];
  return labels.includes(label);
};
const isWritingAgent = (label) => {
  const labels = ['Agents'];
  return labels.includes(label);
};

export const fieldMatcherOptions = [
  {
    id: FiltersOperators.EQNUM,
    label: 'Equals',
    shortLabel: '=',
    validate: [isNumeric],
  },
  {
    id: FiltersOperators.NEQNUM,
    label: 'Not equals',
    shortLabel: '!=',
    validate: [isNumeric],
  },
  {
    id: FiltersOperators.EQ,
    label: 'Equals',
    shortLabel: '=',
    validate: [isString, isArray],
  },
  {
    id: FiltersOperators.NEQ,
    label: 'Not equals',
    shortLabel: '!=',
    validate: [isString, isArray],
  },
  {
    id: FiltersOperators.GT,
    label: 'Greater than',
    shortLabel: '>',
    validate: [isNumeric],
  },
  {
    id: FiltersOperators.LT,
    label: 'Less than',
    shortLabel: '<',
    validate: [isNumeric],
  },
  {
    id: FiltersOperators.GTE,
    label: 'Greater than or equals',
    shortLabel: '>=',
    validate: [isNumeric],
  },
  {
    id: FiltersOperators.LTE,
    label: 'Less than or equals',
    shortLabel: '<=',
    validate: [isNumeric],
  },
  {
    id: FiltersOperators.CONTAINS,
    label: 'Contains',
    validate: [isString, isArray],
  },
  {
    id: FiltersOperators.NCONTAINS,
    label: 'Not contains',
    validate: [isString, isArray],
  },
  {
    id: FiltersOperators.CONTAINEDIN,
    label: 'Contained in',
    validate: [isJson, isString],
  },
  {
    id: FiltersOperators.NCONTAINEDIN,
    label: 'Not contained in',
    validate: [isJson, isString],
  },
  {
    id: FiltersOperators.STARTSWITH,
    label: 'Starts with',
    validate: [isString],
  },
  { id: FiltersOperators.ENDSWITH, label: 'Ends with', validate: [isString] },
  { id: FiltersOperators.BEFORE, label: 'Before', validate: [isDate] },
  {
    id: FiltersOperators.BEFORE_EQUALS,
    label: 'Before or equals',
    validate: [isDate],
  },
  { id: FiltersOperators.AFTER, label: 'After', validate: [isDate] },
  {
    id: FiltersOperators.AFTER_EQUALS,
    label: 'After or equals',
    validate: [isDate],
  },
  { id: FiltersOperators.IS_EMPTY, label: 'Is empty' },
  {
    id: FiltersOperators.NOT_WRITING_AGENT,
    label: 'Not writing agent',
    validate: [isNotWritingAgent],
  },
  {
    id: FiltersOperators.IS_WRITING_AGENT,
    label: 'Is writing agent',
    validate: [isWritingAgent],
  },
  // TODO: We need to deprecate withinOneYear and atLeastOneYear operators
  {
    id: FiltersOperators.WITHIN_ONE_YEAR,
    label: 'Within one year',
    validate: [isDate],
  },
  {
    id: FiltersOperators.AT_LEAST_ONE_YEAR,
    label: 'At least one year',
    validate: [isDate],
  },
  {
    id: FiltersOperators.WITHIN,
    label: 'Within',
    validate: [isDate],
  },
  {
    id: FiltersOperators.CUSTOM,
    label: 'Unset date range',
    validate: [isDate],
  },
  {
    id: FiltersOperators.IS_NOT_EMPTY,
    label: 'Is not empty',
    validate: [isArray, isString],
  },
];

const getFilteredFieldMatcherOptions = (selectedField: any) => {
  return fieldMatcherOptions.filter((op) => {
    if (
      op.validate &&
      selectedField &&
      !op.validate.some(
        (validateFn) =>
          validateFn(selectedField?.fieldMatcherType ?? selectedField?.type) ||
          validateFn(selectedField?.label)
      )
    ) {
      return false;
    }
    return true;
  });
};

const FieldMatcherOperatorSelector = ({
  fieldMatcher,
  getSelectedField,
  onOperatorChange,
  selectedDateOperatorOption,
  setSelectedDateOperatorOption,
}: {
  fieldMatcher: any;
  getSelectedField: (fieldId: any) => any;
  onOperatorChange: (updatedKey: string, item: any) => void;
  selectedDateOperatorOption: string;
  setSelectedDateOperatorOption: (option: string) => void;
}) => {
  const [showDateOptions, setShowDateOptions] = useState(false);
  const [timeValue, setTimeValue] = useState('');
  const [timeUnit, setTimeUnit] = useState(FieldMatchertUnitOptions.Empty);

  const selectedField = getSelectedField(fieldMatcher?.field);

  useEffect(() => {
    if (
      fieldMatcher?.number !== null ||
      fieldMatcher?.unit !== FieldMatchertUnitOptions.Empty
    ) {
      setShowDateOptions(true);
      setTimeValue(fieldMatcher?.number ?? null);
      setTimeUnit(fieldMatcher?.unit ?? FieldMatchertUnitOptions.Empty);
    }
  }, [fieldMatcher]);

  const handleDateOptionsChange = (isChecked: boolean) => {
    setShowDateOptions(isChecked);
    if (!isChecked) {
      setTimeValue('');
      setTimeUnit(FieldMatchertUnitOptions.Empty);
      setSelectedDateOperatorOption(
        FieldMatchertDateOperatorOptions.FromFixedDate
      );
      onOperatorChange('number', null);
      onOperatorChange('unit', FieldMatchertUnitOptions.Empty);
      onOperatorChange('from', FieldMatchertDateOperatorOptions.FromFixedDate);
    }
  };

  return (
    <>
      <EnhancedSelect
        enableSearch
        label="Operator"
        options={getFilteredFieldMatcherOptions(selectedField)}
        labelKey="label"
        value={fieldMatcherOptions.find((item) => item.id === fieldMatcher.op)}
        onChange={(item) => onOperatorChange('op', item.id)}
        sx={{ marginRight: 1 }}
      />

      {fieldMatcher?.op === FiltersOperators.WITHIN ||
      fieldMatcher?.op === FiltersOperators.AFTER ||
      fieldMatcher?.op === FiltersOperators.BEFORE ||
      fieldMatcher?.op === FiltersOperators.AFTER_EQUALS ||
      fieldMatcher?.op === FiltersOperators.BEFORE_EQUALS ? (
        <>
          <Box display="flex" alignItems="center" sx={{ mr: 1 }}>
            <Checkbox
              checked={showDateOptions}
              onChange={(e) => handleDateOptionsChange(e.target.checked)}
            />
            <span>More options</span>
          </Box>

          {showDateOptions && (
            <Box display="flex" alignItems="center" sx={{ gap: 1, mr: 2 }}>
              <TextField
                label="Number"
                type="number"
                value={timeValue}
                onChange={(e) => {
                  const value = e.target.value;
                  setTimeValue(value);
                  onOperatorChange('number', value);
                }}
                sx={{ width: '100px' }}
              />
              <EnhancedSelect
                label="Unit"
                options={[
                  {
                    label:
                      FieldMatchertUnitOptionsLabels[
                        FieldMatchertUnitOptions.Days
                      ],
                    value: FieldMatchertUnitOptions.Days,
                  },
                  {
                    label:
                      FieldMatchertUnitOptionsLabels[
                        FieldMatchertUnitOptions.Months
                      ],
                    value: FieldMatchertUnitOptions.Months,
                  },
                  {
                    label:
                      FieldMatchertUnitOptionsLabels[
                        FieldMatchertUnitOptions.Years
                      ],
                    value: FieldMatchertUnitOptions.Years,
                  },
                ]}
                labelKey="label"
                valueKey="value"
                value={{
                  label: FieldMatchertUnitOptionsLabels[timeUnit],
                  value: timeUnit,
                }}
                onChange={(selected) => {
                  setTimeUnit(selected.value);
                  onOperatorChange('unit', selected.value);
                }}
                sx={{ width: '100px' }}
              />
              <EnhancedSelect
                label="From"
                options={[
                  {
                    label:
                      FieldMatchertDateOperatorOptionsLabels[
                        FieldMatchertDateOperatorOptions.FromFixedDate
                      ],
                    value: FieldMatchertDateOperatorOptions.FromFixedDate,
                  },
                  {
                    label:
                      FieldMatchertDateOperatorOptionsLabels[
                        FieldMatchertDateOperatorOptions.FromDateField
                      ],
                    value: FieldMatchertDateOperatorOptions.FromDateField,
                  },
                ]}
                labelKey="label"
                valueKey="value"
                value={{
                  label:
                    FieldMatchertDateOperatorOptionsLabels[
                      selectedDateOperatorOption
                    ],
                  value: selectedDateOperatorOption,
                }}
                onChange={(selected) => {
                  setSelectedDateOperatorOption(selected.value);
                  onOperatorChange('from', selected.value);
                }}
                sx={{ width: '100px' }}
              />
            </Box>
          )}
        </>
      ) : null}
    </>
  );
};

export default FieldMatcherOperatorSelector;
