import { useMemo } from 'react';
import { Box, Button } from '@mui/material';

import { Option, BaseSelectProps } from '../types';

export const SelectSearchResultToggle = ({
  onChange,
  value,
  multiple,
  filteredList,
  searchKeyword,
  enableSelectAllSearchResult = true,
}: Partial<BaseSelectProps> & { filteredList: Option[] }) => {
  const selectedValues = useMemo(() => {
    return Array.isArray(value)
      ? value.reduce((prev, cur) => {
          prev[cur.value] = true;
          return prev;
        }, {})
      : { [value?.value || '']: true };
  }, [value]);

  const showSelectSearchResult =
    Boolean(filteredList.length) &&
    multiple &&
    Boolean(searchKeyword) &&
    enableSelectAllSearchResult;

  const isAllSearchResultSelected = useMemo(() => {
    if (!showSelectSearchResult) return '';
    const isAllSearchResultSelected = filteredList.every(
      (item) => selectedValues[item.value]
    );
    return isAllSearchResultSelected;
  }, [filteredList, selectedValues, showSelectSearchResult]);

  const selectSearchResultText = isAllSearchResultSelected
    ? 'Unselect all search results'
    : 'Select all search results';

  const toggleSelectSearchResult = () => {
    const filterListValues = new Set(filteredList.map((item) => item.value));
    if (isAllSearchResultSelected) {
      const updatedValues = (value as Option[])?.filter(
        (v) => !filterListValues.has(v.value)
      );
      onChange?.(updatedValues);
    } else {
      const newItems = filteredList.filter(
        (item) => !selectedValues[item.value]
      );
      onChange?.([...newItems, ...((value as Option[]) || [])]);
    }
  };

  return showSelectSearchResult ? (
    <Box sx={{ display: 'flex', ml: 1 }}>
      <Button onClick={toggleSelectSearchResult}>
        {selectSearchResultText}
      </Button>
    </Box>
  ) : null;
};
