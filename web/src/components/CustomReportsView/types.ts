export interface CustomReport {
  id: number;
  str_id: string;
  name: string;
  description?: string;
  report_type: number;
  created_at: string;
  updated_at: string;
  isOwner?: boolean;
}

export interface ReportType {
  id: number;
  name: string;
  description?: string;
}

export interface UploadReportModalProps {
  open: boolean;
  handleClose: () => void;
}

export interface UploadFile extends File {
  preview?: string;
}
