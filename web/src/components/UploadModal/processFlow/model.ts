interface IDocumentCompanies {
  id: number;
  str_id: string;
  company_name: string;
  account_id: string;
  alias_list: any[];
}

interface IDocumentExtractions {
  id: number;
  str_id: string;
  created_at: string;
  method: string;
  status: string;
  output_format: any;
}

interface IDocumentUserModel {
  first_name: string;
  last_name: string;
  id: number;
  str_id: string;
}

export interface IDocumentModel {
  id: number;
  str_id: string;
  created_at: string;
  created_by: string;
  filename: string;
  file_path: string;
  mapping: any;
  method: string;
  override_file_path: string;
  override_filename: string;
  override_mapping: any;
  processor: string;
  prompt: string;
  status: string;
  tag: string;
  type: string;
  file_type: string;
  company_str_id: string | null;
  profile_str_id: string;
  companies: IDocumentCompanies | null;
  extractions: IDocumentExtractions[];
  created_by_user: IDocumentUserModel;
  updated_by_user: any;
  company_id: number;
  bank_total_amount: string;
  statement_amount: string;
}

// ProcessFormModel

export interface ProcessFormModel {
  mapping: string | number | null;
  newMappingCarrier: IDocumentCompanies | number | null;
  newMappingName: string;
  processor: string;
  method: string;
  fileName: string;
  fileType: string;
  selectedSheet?: string;
  prompt?: string;
  promptText?: string;
  createMapping?: boolean;
}

// ICompanyModel
export interface ICompanyModel {
  id: number;
  str_id: string;
  account_id: string;
  uid: string;
  state: string;
  access: string;
  created_at: string;
  created_by: any;
  created_proxied_by: any;
  updated_at: string;
  updated_by: any;
  updated_proxied_by: any;
  address: any;
  alias_list: any[];
  company_id: any;
  company_name: string;
  email: any;
  group_id: any;
  sync_id: any;
  log: any;
  notes: any;
  phone: any;
  type: any;
  website: any;
}

export interface IMappingModel {
  id: number;
  str_id: string;
  account_id: string;
  uid: string;
  state: string;
  access: string;
  created_at: string;
  created_by: any;
  created_proxied_by: any;
  updated_at: string;
  updated_by: any;
  updated_proxied_by: any;
  carrier: ICompanyModel | null;
  carrier_id: any;
  mapping: {
    [key: string]: number;
  };
  name: string;
  type: string;
  modify_status: string;
}

interface IUsersProcessorsUserModel {
  first_name: string;
  last_name: string;
  id: number;
  str_id: string;
}

interface IProcessCompaniesModel {
  id: number;
  str_id: string;
  company_name: string;
  alias_list: any[];
}

export interface IProcessorModel {
  access: string;
  company_id: string | null;
  created_at: string;
  created_by: string;
  document_str_id: string;
  extractionsid: any;
  file_type: string;
  id: number;
  str_id: string;
  inner_name: string;
  method: string;
  name: string;
  notes: string;
  processor_status: string;
  reviewed_at: any;
  reviewed_by: any;
  reviewer_id: any;
  reviewer_str_id: string;
  state: string;
  status: string;
  owner: string;
  type: string;
  updated_at: string;
  updated_by: string;
  created_proxied_by: string;
  suggest_for: string;
  profile_str_id: string;
  extractions: any;
  users_processors_created_byTousers: Partial<IUsersProcessorsUserModel> | null;
  users_processors_updated_byTousers: Partial<IUsersProcessorsUserModel> | null;
  users_processors_reviewed_byTousers: Partial<IUsersProcessorsUserModel> | null;
  companies: IProcessCompaniesModel;
}

export interface ProcessorSelectItem {
  value: string;
  label: string;
  company_name?: string;
  updated_at?: string;
  [key: string]: any;
}

export interface DocumentProfileModel {
  id: number;
  str_id: string;
  uid: string;
  account_id: string;
  state: string;
  created_at: string;
  created_by: string;
  created_proxied_by: string;
  updated_at: string;
  updated_by: string;
  updated_proxied_by: string;
  description: any;
  carrier_name: string;
  paying_entity: string;
  file_link: string[];
  field_mapping: string;
  owner: string;
  status: string;
  priority: number;
  notes: string;
  processor_str_ids: string[];
  mappings_str_ids: any[];
  document_str_ids: string[];
  create_type: string;
}

export interface LoadingConfig {
  loading: boolean;
  message?: string;
  allowClose?: boolean;
}

export interface UseExtractionHandlersProps {
  rowData: IDocumentModel;
  processForm?: import('./process').ProcessFormModel;
  setFileData: (data: import('./process').FileDataModel) => void;
  setExtraction: (data: string) => void;
  setExtractionMethod: (method: string) => void;
  setLoadingConfig: (config: LoadingConfig) => void;
  loadingConfig: LoadingConfig;
}

export interface UseFileToDataReturn {
  setFileData: (data: import('./process').FileDataModel) => void;
  fileData?: import('./process').FileDataModel;
  file: File | null;
  setFile: (file: File | null) => void;
}
