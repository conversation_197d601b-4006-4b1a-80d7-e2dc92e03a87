import { useContext, useEffect, useState } from 'react';
import { captureException } from '@sentry/react';
import { useOriginalFile, useOverrideFile } from 'store/excelStore';
import { safeJsonParse } from 'common/helpers/parse';
import {} from 'common/constants/prompt';

import Spreadsheet from '@/services/Spreadsheet';
import { LoadingContext } from '@/contexts/LoadingContext';
import API from '@/services/API';
import { XLS_CSV_TYPES } from '@/common/preview/model';
import { TestResultParser } from '@/utils/TestResultParser';
import useExtraction from '@/contexts/useExtraction';
import {
  AIModelE,
  FileDataModel,
  ProcessFormModel,
  FileDataTypeE,
  ProcessMethodE,
} from '../process';

const useFileToData = (rowData: any, processForm?: ProcessFormModel) => {
  const originalFile = useOriginalFile();
  const overrideFile = useOverrideFile();

  const [file, setFile] = useState<File | null>(null);
  const [fileData, setFileData] = useState<FileDataModel>();
  const [curExtractionId, setCurExtractionId] = useState<number>();
  const [lastPromptText, setLastPromptText] = useState<string>('');
  const { setExtraction, setExtractionMethod } = useExtraction();
  const { setLoadingConfig, loadingConfig } = useContext(LoadingContext);

  const documentAIPoster = API.getMutation(
    'documents/googleDocumentAI',
    'POST'
  );
  const documentsExtractDataPoster = API.getMutation(
    'documents/extractData',
    'POST'
  );
  const adobeExtractDataPoster = API.getMutation(
    'documents/adobeExtract',
    'POST'
  );
  const nanonetsDataPoster = API.getMutation('documents/nanonets', 'POST');
  const htmlExtractPoster = API.getMutation('documents/htmlExtract', 'POST');

  const vertexPoster = API.getMutation('gpt/parser', 'POST');
  const { data: curExtractionData, isLoading: curExtractionLoading } =
    API.getBasicQuery(`extractions/${curExtractionId}`, '', !!curExtractionId);

  const extractDataByAIService = async (method: string) => {
    try {
      if (curExtractionData) {
        const outputData = safeJsonParse(curExtractionData.output);

        setFileData({
          type: FileDataTypeE.MultiAI,
          data: {
            [method]: outputData,
          },
          defaultMethod: method,
          availableMethods: [method],
          error: '',
        });
      }
    } catch (error) {
      console.error('Error extracting AI data:', error);
      setFileData({
        type: FileDataTypeE.MultiError,
        error: error instanceof Error ? error.message : String(error),
        availableMethods: [method],
        data: null,
      });
    }
  };

  useEffect(() => {
    setFile(overrideFile || originalFile);
  }, [overrideFile, originalFile]);

  useEffect(() => {
    if (file && XLS_CSV_TYPES.includes(file.type)) {
      saveSpreadsheet(file);
    }
  }, [file]);

  useEffect(() => {
    if (rowData.method) {
      if (rowData.method.includes('ai_methods::')) {
        const aiMethods = rowData.method
          .split('::')[1]
          .split(',') as AIModelE[];
        const defaultMethod = aiMethods.includes(AIModelE.Gemini)
          ? AIModelE.Gemini
          : aiMethods[0];
        if (file) {
          handleMultipleAI(file, aiMethods, defaultMethod);
        }
        return;
      }

      if (rowData.method.includes('tool_methods::')) {
        const toolMethods = rowData.method.split('::')[1].split(',');
        const defaultMethod = toolMethods.includes('extractTable')
          ? 'extractTable'
          : toolMethods[0];
        handleMultipleTools(toolMethods, defaultMethod);
        return;
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [rowData.method, file]);

  useEffect(() => {
    if (
      processForm?.promptText &&
      processForm.promptText !== lastPromptText &&
      file
    ) {
      if (
        fileData?.type === FileDataTypeE.MultiAI &&
        Array.isArray(fileData?.availableMethods) &&
        fileData.availableMethods.length > 0
      ) {
        const aiMethods = fileData.availableMethods as AIModelE[];
        const defaultMethod = fileData.defaultMethod as AIModelE;

        setLastPromptText(processForm.promptText);
        handleMultipleAI(file, aiMethods, defaultMethod);
      }

      if (
        processForm?.method === ProcessMethodE.AIResults &&
        fileData?.type === FileDataTypeE.MultiAI
      ) {
        const aiMethods = fileData.availableMethods as AIModelE[];
        const defaultMethod = fileData.defaultMethod as AIModelE;

        setLastPromptText(processForm.promptText);
        handleMultipleAI(file, aiMethods, defaultMethod);
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [processForm?.promptText]);

  useEffect(() => {
    if (rowData.method) {
      const [_method, _extractionStrId, extractionId] =
        rowData.method.split('::');
      if (extractionId) {
        setCurExtractionId(extractionId);
      }
    }
  }, [rowData.method]);

  useEffect(() => {
    if (rowData.override_file_path || curExtractionLoading) return;
    if (
      rowData.method?.includes('ai_methods::') ||
      rowData.method?.includes('tool_methods::')
    )
      return;

    const [method, extractionStrId] = rowData.method.split('::');
    if (file) {
      switch (method) {
        case 'documentAI':
          extractDataByGoogleDocumentAIService(file, extractionStrId);
          break;
        case 'extractTable':
          extractDataByExtractTableService(file, extractionStrId);
          break;
        case 'adobeExtract':
          extractDataByAdobePDFExtractService();
          break;
        case 'nanonets':
          extractDataByNanonetsService();
          break;
        case 'htmlExtract':
          extractDataByHtmlExtractService();
          break;
        default:
          extractDataByAIService(method);
          break;
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [curExtractionData, curExtractionLoading, file]);

  const handleMultipleAI = async (
    file: File,
    aiMethods: AIModelE[],
    defaultMethod: AIModelE
  ) => {
    if (loadingConfig.loading) return;

    setLoadingConfig({
      loading: true,
      message: `Processing with ${aiMethods.length} AI models...`,
    });

    try {
      const resp = await vertexPoster.mutateAsync({
        models: aiMethods,
        document_id: rowData.id,
        prompt: processForm?.promptText,
        type: file?.type,
        force_run: true,
      });

      const results = TestResultParser.parseResults(resp);

      setFileData({
        type: FileDataTypeE.MultiAI,
        data: results,
        defaultMethod: defaultMethod,
        availableMethods: aiMethods,
        error: '',
      });
    } catch (error) {
      console.error('Multi-AI extraction failed:', error);
      setFileData({
        type: FileDataTypeE.MultiError,
        error: error instanceof Error ? error.message : String(error),
        availableMethods: aiMethods,
        data: null,
      });
    } finally {
      setLoadingConfig({ loading: false });
    }
  };

  const handleMultipleTools = async (
    toolMethods: string[],
    defaultMethod: string
  ) => {
    if (loadingConfig.loading) return;

    setLoadingConfig({
      loading: true,
      message: `Processing with ${toolMethods.length} extraction tools...`,
    });

    try {
      const results: { [method: string]: any } = {};

      const executePromises = toolMethods.map(async (method) => {
        try {
          let result;
          switch (method) {
            case 'extractTable':
              result = await executeExtractTableService();
              break;
            case 'documentAI':
              result = await executeDocumentAIService();
              break;
            case 'adobeExtract':
              result = await executeAdobeExtractService();
              break;
            case 'nanonets':
              result = await executeNanonetsService();
              break;
            case 'htmlExtract':
              result = await executeHtmlExtractService();
              break;
            default:
              throw new Error(`Unknown extraction method: ${method}`);
          }

          results[method] = {
            status: 'success',
            data: result,
            method: method,
          };
        } catch (error) {
          results[method] = {
            status: 'error',
            error: error instanceof Error ? error.message : String(error),
            method: method,
          };
        }
      });

      await Promise.all(executePromises);

      setFileData({
        type: FileDataTypeE.MultiExtraction,
        data: results,
        defaultMethod: defaultMethod,
        availableMethods: toolMethods,
        error: '',
      });

      const defaultResult = results[defaultMethod];
      if (defaultResult && defaultResult.status === 'success') {
        if (typeof defaultResult.data === 'string') {
          setExtraction(defaultResult.data);
        } else {
          setExtraction(JSON.stringify(defaultResult.data));
        }
        setExtractionMethod(defaultMethod);
      }
    } catch (error) {
      console.error('Multi-tool extraction failed:', error);
      setFileData({
        type: FileDataTypeE.MultiError,
        error: error instanceof Error ? error.message : String(error),
        availableMethods: toolMethods,
        data: null,
      });
    } finally {
      setLoadingConfig({ loading: false });
    }
  };

  const executeExtractTableService = async () => {
    const extractionOption = rowData.method.split('::');
    const extractionId =
      extractionOption.length > 1 ? extractionOption.pop() : '';
    const param = {
      document_id: rowData.id,
      extract_job_id: rowData.extract_job_id,
      update: true,
      extractionId,
    };
    const resJson = await documentsExtractDataPoster.mutateAsync(param);
    if (resJson.error) {
      throw new Error(resJson.error);
    }
    return JSON.stringify(resJson);
  };

  const executeDocumentAIService = async () => {
    const data = {
      document_id: rowData.id,
      document_type: 'original',
    };
    const resJson = await documentAIPoster.mutateAsync(data);
    if (resJson.error) {
      throw new Error(resJson.error);
    }
    return resJson.data;
  };

  const executeAdobeExtractService = async () => {
    const param = { document_id: rowData.id };
    const resJson = await adobeExtractDataPoster.mutateAsync(param);
    if (resJson.error) {
      throw new Error(resJson.error);
    }
    return [resJson.data];
  };

  const executeNanonetsService = async () => {
    const param = { document_id: rowData.id };
    const resJson = await nanonetsDataPoster.mutateAsync(param);
    if (resJson.error) {
      throw new Error(resJson.error);
    }
    return [resJson.result];
  };

  const executeHtmlExtractService = async () => {
    const param = { document_id: rowData.id };
    const resJson = await htmlExtractPoster.mutateAsync(param);
    if (resJson.error) {
      throw new Error(resJson.error);
    }
    return [resJson.result];
  };

  const saveSpreadsheet = async (file: File) => {
    const res = await Spreadsheet.loadSpreadsheet(file);
    if (res) {
      setFileData({
        type: FileDataTypeE.Spreadsheet,
        data: res,
        error: '',
      });
    }
  };

  const extractDataByGoogleDocumentAIService = async (
    _file: File,
    extractionStrId?: string
  ) => {
    let tableData;
    if (!curExtractionData && !extractionStrId) {
      const data = {
        document_id: rowData.id,
        document_type: 'original',
      } as any;
      setLoadingConfig({
        loading: true,
        message: 'Processing document...',
        allowClose: true,
      });

      try {
        const resJson = await documentAIPoster.mutateAsync(data);
        setLoadingConfig({
          loading: false,
          message: '',
        });
        if (resJson.error) {
          setFileData({
            type: FileDataTypeE.DocumentAI,
            data: null,
            error: resJson.error,
          });
          captureException(resJson.error);
          return;
        }
        tableData = resJson.data;
      } catch (error) {
        setLoadingConfig({
          loading: false,
          message: '',
        });
        setFileData({
          type: FileDataTypeE.DocumentAI,
          data: null,
          error: `${error}`,
        });
        captureException(error);
      }
    } else if (curExtractionData) {
      const outputData = safeJsonParse(curExtractionData.output);
      tableData = outputData;
    }

    if (tableData) {
      setFileData({
        type: FileDataTypeE.DocumentAI,
        data: tableData,
        error: '',
      });
    }
  };

  const extractDataByAdobePDFExtractService = async () => {
    try {
      const [_method, _extractionStrId, extractionId] =
        rowData.method.split('::');

      if (extractionId) {
        const res = [safeJsonParse(curExtractionData.output)];
        setFileData({
          type: FileDataTypeE.AdobeExtract,
          data: res,
          error: '',
        });
        return;
      } else {
        const param = {
          document_id: rowData.id,
        } as any;
        setLoadingConfig({
          loading: true,
          message: 'Processing document...',
          allowClose: true,
        });
        const resJson = await adobeExtractDataPoster.mutateAsync(param);
        setLoadingConfig({
          loading: false,
        });
        if (resJson.error) {
          setFileData({
            type: FileDataTypeE.AdobeExtract,
            data: null,
            error: resJson.error,
          });
          captureException(resJson.error);
          return;
        }
        setFileData({
          type: FileDataTypeE.AdobeExtract,
          data: [resJson.data],
          error: '',
        });
      }
    } catch (error) {
      setFileData({
        type: FileDataTypeE.AdobeExtract,
        data: null,
        error: `${error}`,
      });
      captureException(error);
    } finally {
      setLoadingConfig({
        loading: false,
      });
    }
  };

  const extractDataByExtractTableService = async (
    _file: File,
    extractionStrId?: string
  ) => {
    const reextract = !extractionStrId;
    try {
      let res;
      if (rowData.extractions.length > 0 && !reextract) {
        res = curExtractionData.output;
      } else {
        const extractionOption = rowData.method.split('::');
        const extractionId =
          extractionOption.length > 1 ? extractionOption.pop() : '';
        const param = {
          document_id: rowData.id,
          extract_job_id: rowData.extract_job_id,
          update: reextract,
          extractionId,
        } as any;
        setLoadingConfig({
          loading: true,
          message: 'Processing document...',
          allowClose: true,
        });
        const resJson = await documentsExtractDataPoster.mutateAsync(param);
        setLoadingConfig({
          loading: false,
        });
        if (resJson.error) {
          setFileData({
            type: FileDataTypeE.ExtractTable,
            data: null,
            error: resJson.error,
          });
          captureException(resJson.error);
          return;
        }
        res = JSON.stringify(resJson);
      }
      setFileData({
        type: FileDataTypeE.ExtractTable,
        data: res,
        error: '',
      });
    } catch (error) {
      setFileData({
        type: FileDataTypeE.ExtractTable,
        data: null,
        error: `${error}`,
      });
      captureException(error);
    } finally {
      setLoadingConfig({
        loading: false,
      });
    }
  };

  const extractDataByNanonetsService = async () => {
    try {
      const [_method, _extractionStrId, extractionId] =
        rowData.method.split('::');

      if (extractionId) {
        const res = [safeJsonParse(curExtractionData.output)];
        setFileData({
          type: FileDataTypeE.Nanonets,
          data: res,
          error: '',
        });
        return;
      } else {
        const param = {
          document_id: rowData.id,
        } as any;
        setLoadingConfig({
          loading: true,
          message: 'Processing document...',
          allowClose: true,
        });
        const resJson = await nanonetsDataPoster.mutateAsync(param);
        setLoadingConfig({
          loading: false,
        });
        if (resJson.error) {
          setFileData({
            type: FileDataTypeE.Nanonets,
            data: null,
            error: resJson.error,
          });
          captureException(resJson.error);
          return;
        }
        setFileData({
          type: FileDataTypeE.Nanonets,
          data: [resJson.result],
          error: '',
        });
      }
    } catch (error) {
      setFileData({
        type: FileDataTypeE.Nanonets,
        data: null,
        error: `${error}`,
      });
      captureException(error);
    } finally {
      setLoadingConfig({
        loading: false,
      });
    }
  };

  const extractDataByHtmlExtractService = async () => {
    try {
      const [_method, _extractionStrId, extractionId] =
        rowData.method.split('::');

      if (extractionId) {
        const res = [safeJsonParse(curExtractionData.output)];
        setFileData({
          type: FileDataTypeE.HtmlExtract,
          data: res,
          error: '',
        });
        return;
      } else {
        const param = {
          document_id: rowData.id,
        } as any;
        setLoadingConfig({
          loading: true,
          message: 'Processing document...',
          allowClose: true,
        });
        const resJson = await htmlExtractPoster.mutateAsync(param);
        setLoadingConfig({
          loading: false,
        });
        if (resJson.error) {
          setFileData({
            type: FileDataTypeE.HtmlExtract,
            data: null,
            error: resJson.error,
          });
          captureException(resJson.error);
          return;
        }
        setFileData({
          type: FileDataTypeE.HtmlExtract,
          data: [resJson.result],
          error: '',
        });
      }
    } catch (error) {
      setFileData({
        type: FileDataTypeE.HtmlExtract,
        data: null,
        error: `${error}`,
      });
      captureException(error);
    } finally {
      setLoadingConfig({
        loading: false,
      });
    }
  };

  return {
    setFileData,
    fileData,
    file,
    setFile,
  };
};

export default useFileToData;
