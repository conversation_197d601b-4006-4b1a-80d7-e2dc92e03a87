import { Box, Divider } from '@mui/material';
import { ProcessorSelectorStatuses } from 'common/globalTypes';
import { tool } from 'common/tools';
import { cloneDeep } from 'lodash-es';
import { useCallback, useEffect, useMemo, useState } from 'react';

import API from '@/services/API';
import { normalizeCurrency } from '@/services/DataTransformation/normalizer';
import { convertFieldListToMapping } from '@/services/helpers';
import { DocumentProcessTypes } from '@/types';
import CommissionPreview from './CommissionPreview';
import ProcessForm from './ProcessForm';
import ProcessMappingForm from './ProcessMappingForm';
import useCommonData from './hoc/useCommonData';
import { IDocumentModel, IMappingModel, IProcessorModel } from './model';
import {
  DocumentTypeE,
  ProcessDataModel,
  ProcessFormKey,
  ProcessMethodE,
  ProcessorFormatModel,
  RangeDataModel,
  RowMappingModel,
  ShareDataModel,
  SpreadSheetProps,
  ProcessFormModel,
  FileDataTypeE,
} from './process';
import useCommissionStore from './stores/useCommissionStore';

interface CommissionMapperProps {
  file?: File | null;
  fileType?: DocumentTypeE;
  spreadsheet?: SpreadSheetProps | null;
  selectedSheet: string;
  setSelectedSheet: React.Dispatch<React.SetStateAction<string>>;
  errors: any;
  setErrors: React.Dispatch<React.SetStateAction<any>>;
  extraction: any;
  mpData: {
    mappings: IMappingModel[];
    processors: IProcessorModel[];
  };
  setProcessForm: React.Dispatch<React.SetStateAction<ProcessFormModel>>;
  processForm: ProcessFormModel;
  rowData: Partial<IDocumentModel>;
  setProcessFormatData: React.Dispatch<
    React.SetStateAction<ProcessorFormatModel>
  >;
  processFormatData: any;
  fileData: any;
  addActionCount: (type?: DocumentProcessTypes) => void;
}

const CommissionMapper = ({
  file,
  spreadsheet,
  fileType,
  selectedSheet,
  setSelectedSheet,
  errors,
  setErrors,
  extraction,
  mpData,
  setProcessForm,
  processForm,
  rowData,
  setProcessFormatData,
  processFormatData,
  fileData,
  addActionCount,
}: CommissionMapperProps) => {
  const [processedData, setProcessedData] = useState<ProcessDataModel>({
    data: [],
    fields: [],
    version: null,
  });
  const [rangeData, setRangeData] = useState<RangeDataModel[]>([
    {
      index: 0,
      count: 0,
      fields: [],
      data: [],
    },
  ]);

  const [missingRequiredFields, setMissingRequiredFields] = useState<string[]>(
    []
  );

  const [selectedDataRange, setSelectedDataRange] = useState<RangeDataModel>({
    index: 0,
    count: 0,
    fields: [],
    data: [],
  });
  const rowMapping = useCommissionStore((state) => state.rowMapping);
  const setRowMapping = useCommissionStore((state) => state.setRowMapping);

  // Used to store the current processor to trigger get method
  const [curProcessor, setCurProcessor] = useState<IProcessorModel>();
  const [suggestedProcessors, setSuggestedProcessors] = useState<any[]>([]);

  const { data: processorData } = API.getBasicQuery('processors');
  const { data: curProcessorData, isLoading: isCurPorcessorLoading } =
    API.getBasicQuery(
      `admin/processors/${curProcessor?.id}`,
      '',
      !!curProcessor?.id
    );

  const processorSelectorPoster = API.getMutation(
    'admin/processors/selector',
    'POST'
  );

  const { fields, isExcel, requiredRows, statements, allFieldKeys } =
    useCommonData(fileType, file);
  /**
   * Share data between the commission mapper and the commission preview
   */
  const getActualExtractionData = useCallback(() => {
    if (!fileData) {
      return extraction;
    }

    if (fileData.type === FileDataTypeE.MultiExtraction) {
      for (const method of fileData.availableMethods || []) {
        const data = fileData.data[method];
        if (data && data.status === 'success') {
          try {
            const parsed = JSON.parse(data.data);
            return parsed;
          } catch (e) {
            console.error(`Failed to parse ${method} data:`, e);
            continue;
          }
        }
      }
      return extraction;
    }

    if (fileData.type === FileDataTypeE.MultiAI) {
      return fileData.data;
    }

    return extraction;
  }, [fileData, extraction]);

  const [shareData, setShareData] = useState<ShareDataModel>({
    isExcel: false,
    sheets: [],
    fields: {},
    fileType: '',
    requiredRows: [],
    errors: {},
    allFieldKeys: [],
    fileData: {},
  });

  const sheets = useMemo(() => {
    if (spreadsheet) {
      return spreadsheet.getSheets();
    }
    return [];
  }, [spreadsheet]);

  useEffect(() => {
    if (
      processForm.selectedExtraction &&
      shareData?.fileData?.type === FileDataTypeE.MultiExtraction
    ) {
      setProcessedData({
        data: [],
        fields: [],
        version: null,
      });

      const newExtractionData = getActualExtractionData();
      if (newExtractionData && newExtractionData !== extraction) {
        if (typeof newExtractionData === 'string') {
          try {
            const parsed = JSON.parse(newExtractionData);

            if (parsed.Tables && parsed.Tables.length > 0) {
              const table = parsed.Tables[0];
              const fields = table.Header || [];
              const data = table.Rows || [];

              setProcessedData({
                data: data,
                fields: fields,
                version: ProcessMethodE.ExtractTable,
              });
            }
          } catch (e) {
            console.error('Failed to parse extractTable data in useEffect:', e);
          }
        } else if (typeof newExtractionData === 'object') {
          if (newExtractionData.Tables && newExtractionData.Tables.length > 0) {
            const table = newExtractionData.Tables[0];
            const fields = table.Header || [];
            const data = table.Rows || [];

            setProcessedData({
              data: data,
              fields: fields,
              version: ProcessMethodE.ExtractTable,
            });
          }
        }
      }

      if (
        processForm.method === ProcessMethodE.Processor &&
        processForm.processor
      ) {
        const targetProcessor = mpData.processors?.find(
          (p) => p.str_id === processForm.processor
        );
        if (targetProcessor) {
          setCurProcessor(targetProcessor);
        }
      } else if (processForm.method === ProcessMethodE.Mapping) {
        handleProcessFormChange(ProcessFormKey.method, ProcessMethodE.Mapping);
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [
    processForm.selectedExtraction,
    shareData?.fileData?.type,
    processForm.method,
    processForm.processor,
    mpData.processors,
  ]);

  useEffect(() => {
    setShareData((prev) => {
      const newData = {
        ...prev,
        isExcel,
        sheets,
        fields,
        allFieldKeys,
        fileType: fileType || '',
        requiredRows,
        errors,
        fileData,
      };
      return newData;
    });
  }, [
    fileType,
    fields,
    allFieldKeys,
    requiredRows,
    sheets,
    isExcel,
    errors,
    fileData,
  ]);

  // The key is the field name, the value is the index of the column in the spreadsheet
  const handleProcessFormChange = useCallback(
    (key: keyof ProcessFormModel, value: any) => {
      if (key === ProcessFormKey.method && value === ProcessMethodE.Mapping) {
        setProcessForm((prev) => ({
          ...prev,
          [key]: value,
          processor: '',
        }));

        if (spreadsheet && selectedSheet) {
          try {
            const curMetaData = spreadsheet.getJson(
              selectedSheet
            ) as string[][];
            if (Array.isArray(curMetaData)) {
              const sheetData = statements.getDataInfo(curMetaData);
              setRangeData(sheetData.rangeData);

              if (sheetData.rangeData.length > 0) {
                const currentData = sheetData.rangeData[0];
                setSelectedDataRange(currentData);
                setProcessedData({
                  data: currentData?.data,
                  fields: currentData?.fields,
                  version: ProcessMethodE.MappingSpreadsheet,
                });
              }
            }
          } catch (error) {
            console.error('Error initializing mapping data:', error);
          }
        }
        return;
      }

      if (key === ProcessFormKey.mapping) {
        if (value !== 0) {
          const mapping = mpData.mappings?.find((m) => m.str_id === value);
          if (mapping) {
            setRowMapping(mapping.mapping, true);
          }
        } else {
          const _mappings = cloneDeep(processFormatData.mappingOptions);
          setRowMapping(_mappings, true);
        }
      } else if (key === ProcessFormKey.selectedSheet) {
        setSelectedSheet(value);
      }

      setProcessForm((prev) => {
        const newData = {
          ...prev,
          [key]: value,
        };

        if (key === ProcessFormKey.mapping && value === 0) {
          newData.createMapping = false;
        }

        return newData;
      });
    },
    /*
     *
     *
     * https://linear.app/fintary/issue/PLT-2291
     *
     * MISSED REFs:  'setProcessForm', 'setRowMapping', 'setSelectedSheet', and 'statements'
     * WARN SINCE:  March/2025 ****
     */
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [
      mpData.mappings,
      processFormatData.mappingOptions,
      spreadsheet,
      selectedSheet,
    ]
  );

  // Load the originnal data based on the method
  useEffect(() => {
    if (sheets.length && spreadsheet) {
      const _sheet = selectedSheet || sheets[0];
      setSelectedSheet(_sheet);
    }
  }, [sheets, spreadsheet, selectedSheet, setSelectedSheet]);

  /**
   * Select process method
   */
  useEffect(() => {
    let preProcessor;
    const calculateCommissionTotal = (data: string[][], fields: string[]) => {
      const commissionIndex = fields.findIndex(
        (field) => field.toLowerCase() === 'commission_amount'
      );

      if (commissionIndex === -1 || !data?.length) return 'Invalid';

      const total = data.reduce((sum, row) => {
        const amount = normalizeCurrency(row[commissionIndex]);
        return amount ? sum + amount : sum;
      }, 0);

      return total === 0 ? 'Invalid' : total;
    };

    const processExtraction = (processors) => {
      return processors.map((processor) => {
        try {
          const processorFn = eval(`${processor.processor}`);
          const libs = {
            document: rowData,
            tools: tool,
          };

          let result: any;
          let successFound = false;

          if (spreadsheet && sheets && sheets.length > 0 && isExcel) {
            if (sheets.includes('All')) {
              const combinedSource = spreadsheet.getJson('All');
              const combinedResult = processorFn(combinedSource, libs);

              if (combinedResult?.data?.length) {
                result = combinedResult;
                successFound = true;
                setSelectedSheet('All');
              }
            }

            if (!successFound) {
              for (let i = 0; i < sheets.length; i++) {
                const sheet = sheets[i];
                if (sheet === 'All') continue;

                try {
                  const source = spreadsheet.getJson(sheet);
                  const sheetResult = processorFn(source, libs);

                  if (sheetResult?.data?.length) {
                    result = sheetResult;
                    successFound = true;
                    setSelectedSheet(sheet);
                    break;
                  }
                } catch (sheetError) {
                  console.log(`Processing sheet ${sheet} failed:`, sheetError);
                }
              }
            }

            if (!successFound && !result) {
              throw new Error('No valid result found in any sheet');
            }
          } else {
            const actualExtraction = getActualExtractionData();
            result = processorFn(actualExtraction, libs);
          }

          if (!result?.data?.length) {
            throw new Error('No row data');
          }

          const errorCount = result.data.filter((row) =>
            Object.values(row).some((val) => val == null || val === '')
          ).length;

          const baseResponse = {
            processor_id: processor.str_id,
            processor_name: processor.name,
            totalRows: result.data.length,
            commissionTotal: calculateCommissionTotal(
              result.data,
              result.fields
            ),
            source: 'selector',
          };

          if (
            baseResponse.totalRows <= 1 &&
            baseResponse.commissionTotal === 'Invalid'
          ) {
            throw new Error('Invalid data');
          }

          if (baseResponse.commissionTotal !== 'Invalid') {
            baseResponse.commissionTotal = Number(
              baseResponse.commissionTotal
            ).toFixed(2);
          }

          return errorCount > 0
            ? {
                ...baseResponse,
                status: ProcessorSelectorStatuses.PARTIAL,
                errorCount,
              }
            : { ...baseResponse, status: ProcessorSelectorStatuses.SUCCESS };
        } catch (error) {
          return {
            processor_id: processor.str_id,
            processor_name: processor.name,
            status: ProcessorSelectorStatuses.ERROR,
            error,
          };
        }
      });
    };

    const STATUS_PRIORITY = {
      [ProcessorSelectorStatuses.SUCCESS]: 3,
      [ProcessorSelectorStatuses.PARTIAL]: 2,
      [ProcessorSelectorStatuses.ERROR]: 1,
    };

    const sortProcessorResult = (results) => {
      return results.sort((a, b) => {
        const statusDiff =
          STATUS_PRIORITY[b.status] - STATUS_PRIORITY[a.status];
        if (statusDiff !== 0) return statusDiff;

        const statementAmount = +(rowData.statement_amount || 0);
        const aMatch = statementAmount == a.commissionTotal;
        const bMatch = statementAmount == b.commissionTotal;

        if (aMatch !== bMatch) return aMatch ? -1 : 1;

        return (b.totalRows || 0) - (a.totalRows || 0);
      });
    };

    if (processorData) {
      if (rowData.processor) {
        preProcessor = processorData.find(
          (p) => p.str_id === rowData.processor
        );
      }

      if (sheets && spreadsheet) {
        const selectProcessor = async () => {
          try {
            const processors = await processorSelectorPoster.mutateAsync({
              rowData,
            });

            let allResults: any[] = [];

            if (processors?.length) {
              const selectorResults = processExtraction(processors);
              allResults = [...selectorResults];
            }

            if (
              preProcessor &&
              !allResults.find((r) => r.processor_id === rowData.processor)
            ) {
              const tableResult = processExtraction([preProcessor])[0];
              tableResult.source = 'table';
              allResults.push(tableResult);
            }

            setSuggestedProcessors(allResults);

            const validProcessors = allResults.filter(
              (proc) => proc.status !== ProcessorSelectorStatuses.ERROR
            );

            if (validProcessors.length > 0) {
              const sortedResults = sortProcessorResult(validProcessors);

              handleProcessFormChange(
                ProcessFormKey.method,
                ProcessMethodE.Processor
              );
              handleProcessFormChange(
                ProcessFormKey.processor,
                sortedResults[0].processor_id
              );
            } else {
              switchToMappingMode();
            }
          } catch {
            switchToMappingMode();
          }
        };

        const switchToMappingMode = () => {
          handleProcessFormChange(
            ProcessFormKey.method,
            ProcessMethodE.Mapping
          );

          handleProcessFormChange(ProcessFormKey.processor, '');

          if (spreadsheet && selectedSheet) {
            try {
              const curMetaData = spreadsheet.getJson(
                selectedSheet
              ) as string[][];

              if (Array.isArray(curMetaData)) {
                const sheetData = statements.getDataInfo(curMetaData);
                setRangeData(sheetData.rangeData);

                if (sheetData.rangeData.length > 0) {
                  const currentData = sheetData.rangeData[0];
                  setSelectedDataRange(currentData);
                  setProcessedData({
                    data: currentData?.data,
                    fields: currentData?.fields,
                    version: ProcessMethodE.MappingSpreadsheet,
                  });
                }
              }
            } catch (error) {
              console.error('Error in processor selection:', error);
            }
          }
        };

        selectProcessor();
      } else if (
        (processForm.method === ProcessMethodE.Processor ||
          !processForm.method) &&
        (extraction || fileData)
      ) {
        const selectFn = async () => {
          try {
            const processors = await processorSelectorPoster.mutateAsync({
              rowData,
            });

            let allResults: any[] = [];

            if (processors?.length) {
              const selectorResults = processExtraction(processors);
              allResults = [...selectorResults];
            }

            if (
              preProcessor &&
              !allResults.find((r) => r.processor_id === rowData.processor)
            ) {
              const tableResult = processExtraction([preProcessor])[0];

              tableResult.source = 'table';
              allResults.push(tableResult);
            }

            if (allResults.length) {
              setSuggestedProcessors(allResults);
              handleProcessFormChange(
                ProcessFormKey.method,
                ProcessMethodE.Processor
              );
              handleProcessFormChange(
                ProcessFormKey.processor,
                allResults[0].processor_id
              );
            } else if (!processForm.method) {
              handleProcessFormChange(
                ProcessFormKey.method,
                ProcessMethodE.Mapping
              );
              handleProcessFormChange(ProcessFormKey.processor, '');
            }
          } catch (error) {
            console.error('Error in processor selection:', error);
            handleProcessFormChange(
              ProcessFormKey.method,
              ProcessMethodE.Mapping
            );
          }
        };

        selectFn();
      }
    }
    /*
     * Lint disabled due to uncertain impact on adding missed references.
     *
     * WARN SINCE:  Aug/2024
     * MISSED REFs:  'handleProcessFormChange', 'isExcel', 'processForm.method', 'processorSelectorPoster', 'setSelectedSheet', 'sheets', and 'statements'
     */
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [
    selectedSheet,
    spreadsheet,
    mpData.processors,
    rowData,
    shareData?.fileData,
    extraction,
    processorData,
    getActualExtractionData,
  ]);

  useEffect(() => {
    if (processForm.method === ProcessMethodE.Mapping) {
      const missingFields = shareData.requiredRows.filter(
        (k) => !Object.keys(rowMapping).includes(k)
      );
      setMissingRequiredFields(missingFields);
    }
  }, [rowMapping, shareData.requiredRows, processForm.method]);

  /**
   * If using the processor, then process the data
   */
  useEffect(() => {
    const doAction = async () => {
      if (
        processForm.method === ProcessMethodE.Processor &&
        processForm.processor
      ) {
        const targetProcessor = mpData.processors?.filter(
          (p) => p.str_id === processForm.processor
        )?.[0];
        if (targetProcessor) {
          setCurProcessor(targetProcessor);
        }
      }
    };
    doAction();

    return () => {
      setProcessedData({
        data: [],
        fields: [],
        version: null,
      });
    };
  }, [processForm.processor, processForm.method, mpData.processors]);

  useEffect(() => {
    if (!isCurPorcessorLoading && curProcessorData) {
      try {
        let source = getActualExtractionData();
        if (
          curProcessorData?.suggest_for &&
          sheets.includes(curProcessorData?.suggest_for)
        ) {
          setSelectedSheet(curProcessorData?.suggest_for);
        }
        if (isExcel && spreadsheet && selectedSheet) {
          source = spreadsheet.getJson(selectedSheet);
        }
        if (source && curProcessorData) {
          try {
            const processorFunc = eval(`${curProcessorData.processor}`);
            const libs = {
              document: rowData,
              tools: tool,
            };
            const res = processorFunc(source, libs);
            setProcessedData(res);

            const sheetData = statements.getDataInfo([res.fields, ...res.data]);
            setRangeData(sheetData.rangeData);
            setSelectedDataRange(sheetData.rangeData[0]);

            const missingFields = shareData.requiredRows.filter(
              (k) => !res?.fields?.includes(k)
            );
            setMissingRequiredFields(missingFields);
          } catch (e) {
            setErrors({
              ...errors,
              dataRows: `Error while processing document: ${e}`,
            });
          }
        }
      } catch (err) {
        console.log(err);
      }
    }
    /*
     * Lint disabled due to uncertain impact on adding missed references.
     *
     * WARN SINCE:  Aug/2024
     * MISSED REFs:  'errors', 'isExcel', 'rowData', 'setErrors', 'setSelectedSheet', 'shareData.requiredRows', 'sheets', 'spreadsheet', and 'statements'
     */
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [
    curProcessorData,
    isCurPorcessorLoading,
    selectedSheet,
    getActualExtractionData,
  ]);

  useEffect(() => {
    if (processedData?.fields?.length) {
      const tempMapping = convertFieldListToMapping(
        processedData?.fields ?? []
      ) as RowMappingModel;

      const fieldsInDB = Object.keys(shareData.fields);
      const fieldsInDBMappings = fieldsInDB.reduce((acc, cur) => {
        const opt = shareData.fields[cur].matches;
        const tempMappingKeys = Object.keys(tempMapping);
        const fieldInDBMapping = tempMappingKeys.find((k) =>
          [cur, ...opt].includes(k.toLowerCase())
        );
        if (fieldInDBMapping) {
          acc[cur] = {
            colIndex: tempMapping[fieldInDBMapping],
            colHeader: fieldInDBMapping,
          };
        }
        return acc;
      }, {});
      setRowMapping(fieldsInDBMappings);
      setErrors({
        ...errors,
        dataRows: '',
      });
    }
    /*
     * Lint disabled due to uncertain impact on adding missed references.
     *
     * WARN SINCE:  Set/2024
     * MISSED REFs: 'errors', 'setErrors', and 'setRowMapping'
     */
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [processedData, shareData.fields]);

  return (
    <>
      <Box
        sx={{
          flex: 1,
          display: 'flex',
          flexDirection: 'column',
          height: '100%',
          overflow: 'hidden',
        }}
      >
        <ProcessForm
          mpData={mpData}
          shareData={shareData}
          processForm={processForm}
          suggestedProcessors={suggestedProcessors}
          rowData={rowData}
          handleProcessFormChange={handleProcessFormChange}
          addActionCount={addActionCount}
        />
        <Divider sx={{ mt: 1, mb: 1 }} variant="middle" textAlign="center" />
        <Box sx={{ display: 'flex', flex: 1 }}>
          <ProcessMappingForm
            selectedDataRange={selectedDataRange}
            setSelectedDataRange={setSelectedDataRange}
            rangeData={rangeData}
            shareData={shareData}
            processForm={processForm}
            processedData={processedData}
            addActionCount={addActionCount}
            setProcessedData={setProcessedData}
          />

          <CommissionPreview
            processedData={processedData}
            setProcessedData={setProcessedData}
            shareData={shareData}
            setProcessFormatData={setProcessFormatData}
            setErrors={setErrors}
            missingRequiredFields={missingRequiredFields}
            handleProcessFormChange={handleProcessFormChange}
            addActionCount={addActionCount}
            rowData={rowData}
            selectedExtraction={processForm.selectedExtraction}
          />
        </Box>
      </Box>
    </>
  );
};

export default CommissionMapper;
