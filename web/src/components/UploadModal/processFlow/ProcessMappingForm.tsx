import { Box, Divider, TextField } from '@mui/material';
import { FC, useMemo, useState } from 'react';
import { Edit } from '@mui/icons-material';
import isNull from 'lodash/isNull';

import Spreadsheet from '@/services/Spreadsheet';
import { ShareDataModel, RangeDataModel, ProcessDataModel } from './process';
import { ProcessFormModel } from './process';
import { DocumentProcessTypes, DocumentProcessActionTypes } from '@/types';
import useCommissionStore from './stores/useCommissionStore';
import { EnhancedSelect } from '@/components/molecules/EnhancedSelect';

interface ProcessMappingFormModel {
  selectedDataRange: any;
  rangeData: RangeDataModel[];
  processForm: ProcessFormModel;
  processedData: ProcessDataModel;
  shareData: ShareDataModel;
  setSelectedDataRange: (range: any) => void;
  addActionCount: (type?: DocumentProcessTypes) => void;
  setProcessedData: React.Dispatch<React.SetStateAction<ProcessDataModel>>;
}

const ProcessMappingForm: FC<ProcessMappingFormModel> = ({
  selectedDataRange,
  rangeData,
  shareData,
  setSelectedDataRange,
  processForm,
  processedData,
  addActionCount,
  setProcessedData,
}) => {
  const rowMapping = useCommissionStore((state) => state.rowMapping);
  const setRowMapping = useCommissionStore((state) => state.setRowMapping);

  const [selectedFields, setSelectedFields] = useState<string[]>([]);

  const options = useMemo(() => {
    return Object.keys(shareData.fields)
      .sort((a, b) => {
        const aValue = shareData.fields[a];
        const bValue = shareData.fields[b];
        if (aValue.required && !bValue.required) return -1;
        else if (!aValue.required && bValue.required) return 1;
        else return aValue.label.localeCompare(bValue.label);
      })
      .map((k) => ({
        id: k,
        label: shareData.fields[k].label,
      }));
  }, [shareData.fields]);

  const mergedSelectedFields = useMemo(() => {
    const hasValueKeys = Object.keys(rowMapping);
    const mergedKeys = new Set([...selectedFields, ...hasValueKeys]);
    return options.filter((o) => mergedKeys.has(o.id));
  }, [options, rowMapping, selectedFields]);

  const headerRowOptions = useMemo(() => {
    return rangeData.map((row) => ({
      ...row,
      label: `Row ${row.index + 1}: ${row.fields
        .filter((f) => !isNull(f))
        .join(', ')}`,
    }));
  }, [rangeData]);

  const headerRowSelected = useMemo(() => {
    return headerRowOptions.find((o) => o.index === selectedDataRange?.index);
  }, [headerRowOptions, selectedDataRange?.index]);

  const onChangeHeaderRow = (v: (typeof headerRowOptions)[number]) => {
    if (!v) return;

    addActionCount(DocumentProcessActionTypes.EDIT_MAPPING);
    setSelectedDataRange(v);
    setProcessedData({
      data: v.data,
      fields: v.fields,
      version: processedData.version,
    });
  };

  const onChangeRowMapping = (k: string, v: any) => {
    addActionCount(DocumentProcessActionTypes.EDIT_MAPPING);
    setRowMapping({ [k]: v.id });
  };
  return (
    <Box
      sx={{
        width: 288,
        flexShrink: 0,
        mr: 1,
        height: '100%',
        display: 'flex',
        flexDirection: 'column',
      }}
    >
      <EnhancedSelect
        label="Spreadsheet header row"
        options={headerRowOptions}
        value={headerRowSelected}
        labelKey="label"
        valueKey="index"
        onChange={onChangeHeaderRow}
        enableSearch
        sx={{ mt: 1 }}
        containerWidth={500}
      />

      <Divider sx={{ mt: 2 }} />
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'column',
          mt: 2,
          flexGrow: 1,
        }}
      >
        <EnhancedSelect
          options={options}
          label="Select mapping fields"
          sx={{ width: '100%' }}
          value={mergedSelectedFields}
          labelKey="label"
          valueKey="id"
          onChange={(v) => {
            setSelectedFields(v.map((o) => o.id));
          }}
          multiple
          enableSearch
        />
        {/* Render fields */}
        <Box sx={{ position: 'relative', flexGrow: 1, mt: 1.5 }}>
          <Box
            sx={{
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              overflow: 'auto',
            }}
          >
            {mergedSelectedFields.map(({ id: k }) => {
              const v = shareData.fields[k];
              const fields = selectedDataRange?.fields || [];

              const options = fields
                .map((userField, originalIndex) =>
                  userField
                    ? {
                        id: originalIndex,
                        name: `Col ${Spreadsheet.indexToCol(originalIndex)}: ${userField}`,
                      }
                    : null
                )
                .filter(Boolean)
                .sort((a, b) => a.name.localeCompare(b.name));

              options.unshift({ id: 'NULL', name: '(None)' });
              options.unshift({ id: 'MANUAL_ENTRY', name: '[Manual entry]' });

              let idValue = rowMapping?.[k];
              if (typeof rowMapping?.[k] === 'string') {
                idValue = 'MANUAL_ENTRY';
              } else if (typeof rowMapping?.[k] === 'object') {
                idValue = rowMapping?.[k].colIndex;
              } else {
                idValue = rowMapping?.[k] != null ? rowMapping?.[k] : '';
              }
              const value = options.find((o) => o.id === idValue);

              // Prepare subfield options for manual entry fields
              const subfieldOptions = [
                { id: 'NULL', name: '(None)' },
                ...(v.options?.map((option) => ({
                  id: option,
                  name: option,
                })) || []),
              ];

              return (
                <Box
                  sx={{
                    mt: 1.5,
                    display: 'flex',
                    flexDirection: 'column',
                    width: '100%%',
                  }}
                  key={k}
                >
                  <EnhancedSelect
                    enableSearch
                    label={`${v.label}${v.required ? ' *' : ''}`}
                    options={options}
                    sortLabel={false}
                    value={value}
                    onChange={(v) => onChangeRowMapping(k, v)}
                    disabled={!!processForm.mapping}
                    sx={{ width: '100%' }}
                  />

                  {typeof rowMapping?.[k] === 'string' && (
                    <Box sx={{ width: '100%', display: 'flex', mt: 1, mb: 1 }}>
                      {!v.options?.length ? (
                        <TextField
                          id={`${v.key}-manual-entry`}
                          label={`${v.label} (manual entry)`}
                          variant="outlined"
                          sx={{
                            width: '100%',
                            border: '1px dashed #ccc',
                            borderRadius: 50,
                          }}
                          value={rowMapping[k] || ''}
                          onBlur={(e) => {
                            addActionCount(
                              DocumentProcessActionTypes.EDIT_MAPPING
                            );
                          }}
                          onChange={(e) => {
                            setRowMapping({
                              [k]: e.target.value,
                            });
                          }}
                          InputProps={{
                            endAdornment: <Edit />,
                          }}
                        />
                      ) : (
                        <EnhancedSelect
                          sx={{ width: '100%' }}
                          label={`${v.label} (manual entry)`}
                          options={subfieldOptions}
                          value={subfieldOptions.find(
                            (i) => i.id === rowMapping[k]
                          )}
                          onChange={(v) => {
                            addActionCount(
                              DocumentProcessActionTypes.EDIT_MAPPING
                            );
                            setRowMapping({
                              [k]: v.id,
                            });
                          }}
                        />
                      )}
                    </Box>
                  )}
                </Box>
              );
            })}
          </Box>
        </Box>
      </Box>
    </Box>
  );
};

export default ProcessMappingForm;
