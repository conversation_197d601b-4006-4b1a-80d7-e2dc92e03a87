import {
  Box,
  FormControl,
  MenuItem,
  TextField,
  Typography,
  Checkbox,
  FormControlLabel,
  Tooltip,
} from '@mui/material';
import { useEffect, useState } from 'react';
import Formatter from 'common/Formatter';
import { ProcessorSelectorStatuses } from 'common/globalTypes';

import StatusIcon from '@/components/atoms/StatusIcon';
import AutoCompleteCompanies from '@/components/molecules/AutoCompleteCompanies';
import {
  ShareDataModel,
  DocumentTypeE,
  ProcessFormModel,
  FileDataTypeE,
  ProcessMethodE,
} from './process';
import API from '@/services/API';
import { ProcessorSelectItem } from './model';
import { ProcessMethodList } from './config';
import { DocumentProcessTypes, DocumentProcessActionTypes } from '@/types';
import { EnhancedSelect } from '@/components/molecules/EnhancedSelect';

interface ProcessFormProps {
  mpData: {
    processors: any[];
    mappings: any[];
  };
  rowData: any;
  shareData: ShareDataModel;
  processForm: ProcessFormModel;
  suggestedProcessors: any[];
  handleProcessFormChange: (key: keyof ProcessFormModel, value: any) => void;
  addActionCount: (type?: DocumentProcessTypes) => void;
}

interface ProcessorOption {
  id: string;
  label?: string;
  companyName?: string;
  updatedAt?: string;
  processor_name?: string;
  status?: string;
  totalRows?: number;
  commissionTotal?: number | string;
  errorCount?: number;
  isDivider?: boolean;
}

const ProcessForm: React.FC<ProcessFormProps> = ({
  mpData,
  shareData,
  processForm,
  rowData,
  handleProcessFormChange,
  addActionCount,
  suggestedProcessors,
}) => {
  const [processorList, setProcessorList] = useState<ProcessorSelectItem[]>([]);
  const [processorItemValue, setProcessorItemValue] =
    useState<ProcessorSelectItem>({
      value: '',
      label: '',
      company_name: '',
      updated_at: '',
    });
  const [search, setSearch] = useState<string>('');

  const { data: prompts } = API.getBasicQuery('prompts');

  const isMultiAI = shareData.fileData?.type === FileDataTypeE.MultiAI;
  const isMultiExtraction =
    shareData.fileData?.type === FileDataTypeE.MultiExtraction;

  const getExtractionOptions = () => {
    if (isMultiAI && shareData.fileData?.availableMethods) {
      return shareData.fileData.availableMethods.map((method: string) => ({
        id: method,
        label: method.toUpperCase(),
      }));
    } else if (isMultiExtraction && shareData.fileData?.availableMethods) {
      return shareData.fileData.availableMethods.map((method: string) => ({
        id: method,
        label: method.charAt(0).toUpperCase() + method.slice(1),
      }));
    }
    return [];
  };

  const getMethodOptions = () => {
    if (isMultiAI) {
      return [{ id: ProcessMethodE.Mapping, label: 'Mapping' }];
    } else if (isMultiExtraction) {
      return [
        { id: ProcessMethodE.Processor, label: 'Processor' },
        { id: ProcessMethodE.Mapping, label: 'Mapping' },
      ];
    } else {
      return ProcessMethodList.map((method) => ({
        id: method.value,
        label: method.label,
      }));
    }
  };

  useEffect(() => {
    if (mpData.processors) {
      const list = mpData.processors.map((processor) => ({
        value: processor.str_id,
        label: processor.name,
        company_name:
          processor.companies_processors?.length > 0
            ? processor.companies_processors[0].company.company_name +
              (processor.companies_processors.length > 1
                ? ` and ${processor.companies_processors.length - 1} other${processor.companies_processors.length > 2 ? 's' : ''}`
                : '')
            : 'Unknown',
        companies:
          processor.companies_processors?.map((cp: any) => cp.company) || [],
        updated_at: processor.updated_at,
      }));
      setProcessorList(list);
    }
  }, [mpData.processors]);

  useEffect(() => {
    if (rowData) {
      if (prompts) {
        const rowDataCompany = rowData.company_str_id;
        const promptCompany = (prompts as any[]).find(
          (item) => item.company_str_id === rowDataCompany
        );
        if (promptCompany) {
          handleProcessFormChange('prompt', promptCompany.company_str_id);
          handleProcessFormChange('promptText', promptCompany.prompt);
        }
      }
    }
  }, [rowData, prompts, handleProcessFormChange]);

  useEffect(() => {
    if ((isMultiAI || isMultiExtraction) && shareData.fileData?.defaultMethod) {
      if (!processForm.selectedExtraction) {
        handleProcessFormChange(
          'selectedExtraction',
          shareData.fileData.defaultMethod
        );
      }
    }
  }, [
    isMultiAI,
    isMultiExtraction,
    shareData.fileData?.defaultMethod,
    processForm.selectedExtraction,
    handleProcessFormChange,
  ]);

  useEffect(() => {
    if (processForm.processor) {
      const processor = processorList?.find(
        (processor) => processor.value === processForm.processor
      );
      if (processor) {
        setProcessorItemValue(processor);
      }
    }
  }, [processForm.processor, processorList]);

  const processorOptions = (() => {
    const baseOptions = processorList.map((option) => ({
      id: option.value,
      label: option.label,
      companyName: option.company_name,
      updatedAt: option.updated_at,
      isDivider: false,
    }));

    const suggestedMap = new Map(
      suggestedProcessors.map((proc) => [proc.processor_id, proc])
    );

    const [suggested, recentSelector, others] = baseOptions.reduce<
      [ProcessorOption[], ProcessorOption[], ProcessorOption[]]
    >(
      ([sugg, recent, others], option) => {
        if (suggestedMap.has(option.id)) {
          const suggestedProc = suggestedMap.get(option.id)!;
          if (suggestedProc.source === 'table') {
            recent.push({
              ...option,
              processor_name: suggestedProc.processor_name,
              status: suggestedProc.status,
              totalRows: suggestedProc.totalRows,
              commissionTotal: suggestedProc.commissionTotal,
              errorCount: suggestedProc.errorCount,
            });
          } else {
            sugg.push({
              ...option,
              processor_name: suggestedProc.processor_name,
              status: suggestedProc.status,
              totalRows: suggestedProc.totalRows,
              commissionTotal: suggestedProc.commissionTotal,
              errorCount: suggestedProc.errorCount,
            });
          }
        } else {
          others.push(option);
        }
        return [sugg, recent, others];
      },
      [[], [], []]
    );

    const sortedOthers = others.sort((a, b) => {
      return (a.label || '').localeCompare(b.label || '');
    });

    const suggestedTitleOption: ProcessorOption = {
      id: 'suggested_title',
      label: 'Suggested processors',
      companyName: undefined,
      updatedAt: undefined,
      isDivider: true,
    };

    const recentSelectorTitleOption: ProcessorOption = {
      id: 'recent_selector_title',
      label: 'Recent Selector',
      companyName: undefined,
      updatedAt: undefined,
      isDivider: true,
    };

    const othersTitleOption: ProcessorOption = {
      id: 'other_title',
      label: 'Other processors',
      companyName: undefined,
      updatedAt: undefined,
      isDivider: true,
    };

    const allOptions = [
      ...(suggested.length > 0 ? [suggestedTitleOption, ...suggested] : []),
      ...(recentSelector.length > 0
        ? [recentSelectorTitleOption, ...recentSelector]
        : []),
      othersTitleOption,
      ...sortedOthers,
    ];

    return allOptions.filter((item) => {
      if (search) {
        const searchLower = search.toLowerCase();
        return item.label?.toLowerCase().includes(searchLower);
      }
      return true;
    });
  })();

  const enhancedSelectorStyle = {
    minWidth: 125,
    width: 'auto',
    '& .MuiBox-root': {
      maxWidth: 'calc(100% - 24px)',
      overflow: 'hidden',
      textOverflow: 'ellipsis',
      whiteSpace: 'nowrap',
    },
  };

  return (
    <Box
      sx={{
        gap: 2,
        display: 'flex',
        width: '100%',
        alignItems: 'center',
        paddingTop: 1,
      }}
    >
      <Box
        sx={{
          display: 'flex',
          gap: 2,
          alignItems: 'center',
          flex: 1,
          minWidth: 0,
        }}
      >
        {shareData.sheets.length > 1 && (
          <FormControl fullWidth>
            <EnhancedSelect
              enableSearch
              label={shareData.isExcel ? 'Sheet' : 'Table'}
              sx={enhancedSelectorStyle}
              options={shareData.sheets.map((sheet) => ({
                id: sheet,
                label: sheet,
              }))}
              value={{
                id: processForm.selectedSheet || '',
                label: processForm.selectedSheet || '',
              }}
              onChange={(value) => {
                addActionCount(DocumentProcessActionTypes.SELECT_SPREADSHEET);
                handleProcessFormChange('selectedSheet', value.id);
              }}
            />
          </FormControl>
        )}

        {(isMultiAI || isMultiExtraction) && (
          <FormControl fullWidth>
            <EnhancedSelect
              label="Extractions"
              sx={enhancedSelectorStyle}
              options={getExtractionOptions()}
              value={{
                id: processForm.selectedExtraction || '',
                label:
                  getExtractionOptions().find(
                    (opt) => opt.id === processForm.selectedExtraction
                  )?.label || '',
              }}
              onChange={(value) => {
                addActionCount(DocumentProcessActionTypes.SELECT_SPREADSHEET);
                handleProcessFormChange('selectedExtraction', value.id);
              }}
            />
          </FormControl>
        )}

        <FormControl fullWidth>
          <EnhancedSelect
            label="Method"
            sx={enhancedSelectorStyle}
            options={getMethodOptions()}
            value={{
              id: processForm.method || '',
              label:
                getMethodOptions().find((m) => m.id === processForm.method)
                  ?.label || '',
            }}
            onChange={(value) => {
              addActionCount(DocumentProcessActionTypes.SELECT_METHOD);
              handleProcessFormChange('method', value.id);
            }}
          />
        </FormControl>

        {processForm.method === ProcessMethodE.Processor && (
          <FormControl fullWidth>
            <EnhancedSelect
              enableSearch
              label="Processor"
              sortLabel={false}
              sx={enhancedSelectorStyle}
              listContainerSx={{
                minWidth: 850,
                p: 1,
              }}
              value={{
                id: processorItemValue?.value || '',
                label: processorItemValue?.label || '',
                companyName: processorItemValue?.company_name,
                updatedAt: processorItemValue?.updated_at,
                ...(processorItemValue?.status
                  ? { status: processorItemValue.status }
                  : {}),
              }}
              onChange={(value) => {
                addActionCount(DocumentProcessActionTypes.SELECT_PROCESSOR);
                handleProcessFormChange('processor', value.id);
              }}
              onSearch={setSearch}
              searchKeyword={search}
              options={processorOptions}
              renderLabel={(input) => {
                const option = processorOptions.find(
                  (o) => o.id === input?.key
                );
                if (!option) return null;

                if (option.isDivider) {
                  return (
                    <div
                      className="px-3 py-1.5 bg-gray-50 cursor-default select-none"
                      onClick={(e) => e.preventDefault()}
                    >
                      <Typography variant="subtitle2" className="text-gray-500">
                        {option.label}
                      </Typography>
                    </div>
                  );
                }

                const isSuggested = option.status !== undefined;
                let isCommissionMatch: boolean | undefined;
                if (rowData.statement_amount) {
                  isCommissionMatch =
                    Number(option.commissionTotal) ==
                    Number(rowData.statement_amount);
                }

                return (
                  <MenuItem key={option.id} value={option.id}>
                    <Box className="overflow-x-auto">
                      <Box
                        className="flex items-center px-3 py-2 whitespace-nowrap"
                        sx={{ minWidth: '780px' }}
                      >
                        <Box className="flex items-center gap-3 min-w-[200px]">
                          <Typography>{option.label}</Typography>
                          {isSuggested &&
                            option.status ===
                              ProcessorSelectorStatuses.SUCCESS && (
                              <Typography color="success.main">✅</Typography>
                            )}
                          {isSuggested &&
                            option.status ===
                              ProcessorSelectorStatuses.PARTIAL && (
                              <Typography color="warning.main">⚠️</Typography>
                            )}
                          {isSuggested &&
                            option.status ===
                              ProcessorSelectorStatuses.ERROR && (
                              <Typography color="error.main">❌</Typography>
                            )}
                        </Box>

                        {isSuggested && (
                          <Box className="flex items-center gap-6 mx-4">
                            {option.totalRows && (
                              <Typography
                                variant="caption"
                                className="whitespace-nowrap text-gray-500"
                              >
                                Rows: {option.totalRows}
                              </Typography>
                            )}
                            {option.commissionTotal && (
                              <Typography
                                variant="caption"
                                className="whitespace-nowrap text-gray-500"
                              >
                                Statement amount:{' '}
                                <span
                                  className={
                                    isCommissionMatch !== undefined
                                      ? isCommissionMatch
                                        ? 'text-green-600'
                                        : 'text-red-600'
                                      : ''
                                  }
                                >
                                  {option.commissionTotal}
                                </span>
                              </Typography>
                            )}
                            {option.errorCount && (
                              <Typography
                                variant="caption"
                                className="whitespace-nowrap text-gray-500"
                              >
                                Empty counts:{' '}
                                <span className="text-red-600">
                                  {option.errorCount}
                                </span>
                              </Typography>
                            )}
                          </Box>
                        )}

                        <Typography
                          variant="caption"
                          className="text-gray-400 ml-auto whitespace-nowrap"
                        >
                          ({option.companyName || 'Unknown'} -{' '}
                          {option.updatedAt
                            ? new Date(option.updatedAt).toLocaleDateString()
                            : 'No date'}
                          )
                        </Typography>
                      </Box>
                    </Box>
                  </MenuItem>
                );
              }}
            />
          </FormControl>
        )}

        {(processForm.method === ProcessMethodE.Gemini ||
          processForm.method === 'ai_results' ||
          isMultiAI) && (
          <FormControl fullWidth>
            <EnhancedSelect
              label="Prompt"
              sx={enhancedSelectorStyle}
              options={(prompts || []).map((c: any) => ({
                id: c.str_id,
                label: c.name,
              }))}
              value={{
                id: processForm.prompt || '',
                label:
                  (prompts || []).find(
                    (c: any) => c.str_id === processForm.prompt
                  )?.name || '',
              }}
              onChange={(value) => {
                addActionCount(DocumentProcessActionTypes.SELECT_GEMINI_PROMPT);
                handleProcessFormChange('prompt', value.id);

                const selectedPrompt = (prompts || []).find(
                  (c: any) => c.str_id === value.id
                );
                if (selectedPrompt) {
                  handleProcessFormChange('promptText', selectedPrompt.prompt);
                }
              }}
            />
          </FormControl>
        )}

        <FormControl fullWidth>
          <EnhancedSelect
            label="Mapping"
            sx={enhancedSelectorStyle}
            listContainerSx={{
              minWidth: 600,
              p: 1,
            }}
            value={{
              id: processForm.mapping || 0,
              label:
                mpData.mappings?.find(
                  (m: any) => m.str_id === processForm.mapping
                )?.name || 'New',
            }}
            options={[
              { id: 0, label: 'New' },
              ...(mpData.mappings?.map((mapping: any) => ({
                id: mapping.str_id,
                label: mapping,
              })) || []),
            ]}
            onChange={(value) => {
              if (value.id === 0) {
                handleProcessFormChange('mapping', '');
              } else {
                handleProcessFormChange('mapping', value.id);
              }
              addActionCount(DocumentProcessActionTypes.SELECT_MAPPING);
            }}
            renderLabel={({ key }: { key?: string | number }) => {
              const item = mpData.mappings
                ? mpData.mappings.find((item: any) => item.str_id === key)
                : null;
              if (!item)
                return (
                  <MenuItem>
                    <Box sx={{ display: 'flex', width: '100%' }}>
                      <Box sx={{ flex: 1 }}>New</Box>
                    </Box>
                  </MenuItem>
                );

              return (
                <MenuItem key={item.str_id} value={item.str_id}>
                  <Box sx={{ display: 'flex', width: '100%' }}>
                    <Box sx={{ flex: 1 }}>{item.name}</Box>
                    <Typography
                      variant="caption"
                      component="span"
                      sx={{ color: '#555', ml: 1 }}
                    >
                      {`(${
                        rowData.companies?.company_name &&
                        `${rowData.companies?.company_name} - `
                      }${Formatter.date(item.created_at, true, 'MM/DD/YYYY hh:mmA')})`}
                    </Typography>
                  </Box>
                </MenuItem>
              );
            }}
          />
        </FormControl>

        {!processForm.mapping &&
          processForm.createMapping &&
          shareData.fileType === DocumentTypeE.Statement && (
            <FormControl fullWidth>
              <AutoCompleteCompanies
                sx={{
                  '& .MuiInputBase-root': {
                    height: 35,
                  },
                }}
                value={processForm.newMappingCarrier}
                onChange={(event, newEvent) => {
                  addActionCount(DocumentProcessActionTypes.SELECT_COMPANY);
                  handleProcessFormChange('newMappingCarrier', newEvent);
                }}
              />
            </FormControl>
          )}

        {!processForm.mapping && processForm.createMapping && (
          <FormControl fullWidth>
            <TextField
              label="Mapping name"
              variant="outlined"
              value={processForm.newMappingName}
              onBlur={() => {
                addActionCount(DocumentProcessActionTypes.EDIT_MAPPING);
              }}
              onChange={(e) => {
                handleProcessFormChange('newMappingName', e.target.value);
              }}
              sx={{
                '& .MuiInputBase-root': {
                  height: 35,
                },
              }}
            />
          </FormControl>
        )}
      </Box>

      <Box
        sx={{
          display: 'flex',
          alignItems: 'center',
          gap: 1.5,
          flex: '0 0 auto',
          minWidth: 'fit-content',
        }}
      >
        <Tooltip
          title={!processForm.mapping ? 'Save this mapping' : ''}
          placement="top"
          arrow
        >
          <Box>
            <FormControlLabel
              control={
                <Checkbox
                  checked={
                    !processForm.mapping
                      ? processForm.createMapping || false
                      : false
                  }
                  disabled={!!processForm.mapping}
                  onChange={(e) => {
                    if (!processForm.mapping) {
                      addActionCount(DocumentProcessActionTypes.EDIT_MAPPING);
                      handleProcessFormChange(
                        'createMapping',
                        e.target.checked
                      );
                      if (!e.target.checked) {
                        handleProcessFormChange('newMappingName', '');
                        handleProcessFormChange('newMappingCarrier', null);
                      }
                    }
                  }}
                  size="small"
                />
              }
              label="Save"
              sx={{
                '& .MuiFormControlLabel-label': {
                  fontSize: '0.875rem',
                  whiteSpace: 'nowrap',
                  color: processForm.mapping
                    ? 'rgba(0, 0, 0, 0.38)'
                    : 'inherit',
                },
                minWidth: 'fit-content',
                margin: 0,
              }}
            />
          </Box>
        </Tooltip>

        {shareData.errors.mapping ? (
          <StatusIcon
            icon="info"
            color="#F4B400"
            message={shareData.errors.mapping}
          />
        ) : (
          <StatusIcon icon="complete" color="#0F9D58" message="" />
        )}
      </Box>
    </Box>
  );
};

export default ProcessForm;
