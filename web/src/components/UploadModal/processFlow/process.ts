export enum AIModelE {
  Gemini = 'gemini',
  ChatGPT = 'chatgpt',
  Grok = 'grok',
  <PERSON> = 'claude',
  LlamaIndex = 'llamaindex',
  Mistral = 'mistral',
}

export enum ExtractionToolE {
  DocumentAI = 'documentAI',
  ExtractTable = 'extractTable',
  AdobeExtract = 'adobeExtract',
  Nanonets = 'nanonets',
  HtmlExtract = 'htmlExtract',
}

export enum ProcessMethodE {
  Processor = 'processor',
  Mapping = 'mapping',
  Compgrid = 'compgrid',
  InvalidJSON = 'InvalidJSON',
  DocumentAI = 'documentAI',
  ExtractTable = 'extractTable',
  AdobeExtract = 'adobeExtract',
  Nanonets = 'nanonets',
  HtmlExtract = 'htmlExtract',
  Gemini = 'gemini',
  ChatGPT = 'chatgpt',
  Grok = 'grok',
  Claude = 'claude',
  LlamaIndex = 'llamaindex',
  Mistral = 'mistral',
  MappingSpreadsheet = 'MappingSpreadsheet',
  AIResults = 'ai_results',
  MultiAI = 'multi_ai',
  MultiTools = 'multi_extraction',
}

export enum FileDataTypeE {
  DocumentAI = 'documentAI',
  ExtractTable = 'extractTable',
  AdobeExtract = 'adobeExtract',
  Nanonets = 'nanonets',
  HtmlExtract = 'htmlExtract',
  Gemini = 'gemini',
  ChatGPT = 'chatgpt',
  Grok = 'grok',
  Claude = 'claude',
  LlamaIndex = 'llamaindex',
  Mistral = 'mistral',
  Spreadsheet = 'spreadsheet',
  MultiAI = 'multi_ai',
  MultiExtraction = 'multi_extraction',
  MultiError = 'multi_error',
  InvalidJSON = 'InvalidJSON',
}

export enum ProcessFormKey {
  mapping = 'mapping',
  newMappingCarrier = 'newMappingCarrier',
  newMappingName = 'newMappingName',
  processor = 'processor',
  method = 'method',
  fileName = 'fileName',
  fileType = 'fileType',
  selectedSheet = 'selectedSheet',
  prompt = 'prompt',
  promptText = 'promptText',
  selectedExtraction = 'selectedExtraction',
}

export enum DocumentTypeE {
  Statement = 'statement',
  Policy = 'policy',
  Report = 'report',
}

interface IEnabledFor {
  default: boolean;
  insurance: boolean;
}

interface IRequiredFor {
  default: boolean;
  insurance: boolean;
}

export interface FieldsProps {
  id: number;
  str_id: string;
  key: string;
  model: string;
  label: string;
  options: string[];
  enabled_for: IEnabledFor;
  required_for: IRequiredFor;
  type: string;
  description: string;
  notes: any;
  formatter: any;
  normalizer: any;
  state: string;
  created_at: string;
  updated_at: string;
  created_by: string;
  updated_by: string;
  required?: boolean;
  enabled?: boolean;
}

export interface IFieldsProps {
  id: number;
  str_id: string;
  key: string;
  model: string;
  label: string;
  options: string[];
  enabled_for: IEnabledFor;
  required_for: IRequiredFor;
  type: string;
  description: string;
  notes: any;
  formatter: any;
  normalizer: any;
  state: string;
  created_at: string;
  updated_at: string;
  created_by: string;
  updated_by: string;
  required?: boolean;
  enabled?: boolean;
}

interface FieldItemModel {
  id: number;
  str_id: string;
  state: string;
  created_at: string;
  created_by: string;
  created_proxied_by: any;
  updated_at: string;
  updated_by: string;
  updated_proxied_by: any;
  copyable: boolean;
  description: string;
  enabled_for: IEnabledFor;
  formatter: any;
  key: string;
  label: string;
  model: string;
  normalizer: any;
  notes: string;
  options: string[];
  matches: string[];
  required_for: IRequiredFor;
  type: string;
  required: boolean;
  enabled: boolean;
}

export interface FieldModel {
  [key: string]: FieldItemModel;
}

export interface RowMappingModel {
  [key: string]: number | string | { colIndex: number; colHeader: string };
}

export interface ErrorMsg {
  dataRows?: string;
  extraction?: string;
  upload?: string;
  mapping?: string;
  missingField?: string;
  [key: string]: string | undefined;
}

export interface FileDataModel {
  type?: FileDataTypeE;
  data?: any;
  extraction?: {
    id: number;
    str_id: string;
    method: string;
  };
  error?: string;
  defaultMethod?: AIModelE | ExtractionToolE | string;
  availableMethods?: Array<AIModelE | ExtractionToolE | string>;
}

export interface ProcessDataModel {
  fields: string[];
  version: ProcessMethodE | null;
  data: string[][];
  sheet?: string;
}

export interface RangeDataModel {
  index: number;
  count: number;
  fields: string[];
  data: string[][];
}

export interface ProcessorFormatModel {
  mappingOptions: RowMappingModel;
  cmsTotal?: string;
  data: {
    [key: string]: string | number | boolean | '' | null | string[];
  }[];
  rowData: {
    [key: string]: any;
  }[];
}

export interface SpreadSheetProps {
  getSheets: () => string[];
  getJson: (i: number | string) => any[];
}

export interface ShareDataModel {
  isExcel: boolean;
  sheets: string[];
  statementFields?: any[];
  reportFields?: any[];
  fields: FieldModel;
  requiredRows: string[];
  allFieldKeys: FieldsProps[];
  fileType: string;
  errors: ErrorMsg;
  fileData: FileDataModel;
}

export interface ProcessFormModel {
  mapping?: string | number;
  newMappingCarrier?: string | number | any;
  newMappingName?: string;
  processor?: string;
  method?: ProcessMethodE | string;
  fileName?: string;
  fileType?: string;
  selectedSheet?: string;
  prompt?: string;
  promptText?: string;
  selectedExtraction?: string;
  createMapping?: boolean;
}

export interface ParsedMethod {
  type: 'ai_methods' | 'tool_methods' | 'single';
  methods: string[];
  defaultMethod: string;
  extractionId?: string;
}

export interface MethodExecutionResult {
  status: 'success' | 'error';
  data?: any;
  error?: string;
  method: string;
}

export type AllExtractionMethodsE = AIModelE | ExtractionToolE;

export type MultiMethodType =
  | ProcessMethodE.MultiAI
  | ProcessMethodE.MultiTools;

export const isAIMethod = (method: string): method is AIModelE => {
  return Object.values(AIModelE).includes(method as AIModelE);
};

export const isExtractionTool = (method: string): method is ExtractionToolE => {
  return Object.values(ExtractionToolE).includes(method as ExtractionToolE);
};

export const isMultiMethod = (method: string): method is MultiMethodType => {
  return (
    method === ProcessMethodE.MultiAI || method === ProcessMethodE.MultiTools
  );
};

export const isCombinedMethod = (methodString: string): boolean => {
  return (
    methodString.includes('::') &&
    (methodString.startsWith('ai_methods::') ||
      methodString.startsWith('tool_methods::'))
  );
};

export const AI_METHOD_PRIORITY = [
  AIModelE.Gemini,
  AIModelE.ChatGPT,
  AIModelE.Claude,
  AIModelE.Grok,
  AIModelE.LlamaIndex,
  AIModelE.Mistral,
];

export const EXTRACTION_TOOL_PRIORITY = [
  ExtractionToolE.ExtractTable,
  ExtractionToolE.DocumentAI,
  ExtractionToolE.AdobeExtract,
  ExtractionToolE.Nanonets,
  ExtractionToolE.HtmlExtract,
];
