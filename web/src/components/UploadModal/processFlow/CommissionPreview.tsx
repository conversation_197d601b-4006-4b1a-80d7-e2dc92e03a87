import React, { FC, useEffect, useMemo, useRef, useState } from 'react';
import { isEqual } from 'lodash-es';
import currency from 'currency.js';
import { AgGridReact } from 'ag-grid-react';
import { Clear, FileDownload, InfoOutlined, Search } from '@mui/icons-material';
import {
  Box,
  Button,
  Chip,
  IconButton,
  Tooltip,
  InputAdornment,
  TextField,
} from '@mui/material';
import {
  CellValueChangedEvent,
  ColDef,
  FilterChangedEvent,
} from 'ag-grid-community';
import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';
import { tool } from 'common/tools';
import { ProcessorSelectorStatuses } from 'common/globalTypes';

import { exportCsv } from '@/services/helpers';
import AddFieldsDialog from './addFieldDialog';
import { normalizeCurrency } from '@/services/DataTransformation/normalizer';
import {
  <PERSON>ieldsProps,
  ProcessDataModel,
  ProcessMethodE,
  FileDataTypeE,
  RowMappingModel,
  ShareDataModel,
} from './process';
import RemoveFieldsDialog from './removeFieldDialog';
import VertexResult from './VertexResult';
import { DocumentProcessActionTypes, DocumentProcessTypes } from '@/types';
import { IDocumentModel } from './model';
import useCommissionStore from './stores/useCommissionStore';

interface ICommissionPreviewProps {
  processedData: ProcessDataModel;
  setProcessedData: (data: any) => void;
  shareData: ShareDataModel;
  missingRequiredFields: string[];
  setProcessFormatData: (data: any) => void;
  handleProcessFormChange: (k, v) => void;
  setErrors: (data: any) => void;
  addActionCount: (type?: DocumentProcessTypes) => void;
  rowData: Partial<IDocumentModel>;
  selectedExtraction?: string;
  processForm?: any;
}

dayjs.extend(utc);

const CommissionPreview: FC<ICommissionPreviewProps> = ({
  processedData,
  setProcessedData,
  shareData,
  setProcessFormatData,
  handleProcessFormChange,
  setErrors,
  missingRequiredFields,
  addActionCount,
  rowData,
  selectedExtraction,
  processForm,
}) => {
  const rowMapping = useCommissionStore((state) => state.rowMapping);
  const setRowMapping = useCommissionStore((state) => state.setRowMapping);
  const [columnDefs, setColumnDefs] = useState<ColDef[]>([]);
  const [customData, setCustomData] = useState<string[][]>([]);
  const [pinnedBottomRowData, setPinnedBottomRowData] = useState<any[]>([]);
  const [removeNode, setRemoveNode] = useState<any>();

  const [rowDataDef, setRowDataDef] = useState<RowMappingModel[]>([]);

  const [open, setOpen] = useState(false);
  const [showRemove, setShowRemove] = useState(false);
  const [canAddFields, setCanAddFields] = useState<IFieldsProps[]>([]);
  const [canRemoveFields, setCanRemoveFields] = useState<IFieldsProps[]>([]);

  const [showVertexRes, setShowVertexRes] = useState(false);
  const [invalidVertexRes, setInvalidVertexRes] = useState('');
  const [parsedVertexJson, setParsedVertexJson] = useState<any[]>([]);
  const [resultTableSearch, setResultTableSearch] = useState('');
  const gridRef = useRef<AgGridReact>(null);

  const getCurrentExtractionData = () => {
    if (!shareData?.fileData) {
      return processedData;
    }

    if (shareData.fileData.type === FileDataTypeE.MultiAI) {
      const selectedMethod =
        selectedExtraction || shareData.fileData.defaultMethod;

      if (!selectedMethod) {
        return { data: [], fields: [], version: null };
      }

      const aiData = shareData.fileData.data[selectedMethod];
      console.log('aiData', aiData);

      if (aiData && aiData.error) {
        setErrors((prevErrors) => ({
          ...prevErrors,
          extraction: aiData.error,
        }));
        return { data: [], fields: [], version: null };
      }

      let entries: any[] = [];

      if (aiData && aiData.data_entries && Array.isArray(aiData.data_entries)) {
        entries = aiData.data_entries;
      } else if (
        aiData &&
        aiData.role === 'model' &&
        aiData.parts &&
        Array.isArray(aiData.parts)
      ) {
        if (aiData.parts.length > 0 && aiData.parts[0].text) {
          const parsedText = JSON.parse(aiData.parts[0].text);
          if (Array.isArray(parsedText)) {
            entries = parsedText;
          }
        }
      } else if (aiData && aiData.text && typeof aiData.text === 'string') {
        const parsedText = JSON.parse(aiData.text);
        if (Array.isArray(parsedText)) {
          entries = parsedText;
        }
      } else if (aiData && aiData.entries && Array.isArray(aiData.entries)) {
        entries = aiData.entries;
      } else if (Array.isArray(aiData)) {
        entries = aiData;
      } else if (aiData && aiData.fields && aiData.data) {
        return {
          data: aiData.data,
          fields: aiData.fields,
          version: ProcessMethodE.MultiAI,
        };
      }

      if (entries.length > 0) {
        const fields = Object.keys(entries[0]);
        const data = entries.map((item) => Object.values(item));
        return {
          data: data,
          fields: fields,
          version: ProcessMethodE.MultiAI,
        };
      }

      return { data: [], fields: [], version: null };
    } else if (shareData.fileData.type === FileDataTypeE.MultiExtraction) {
      const extractionMethod =
        selectedExtraction || shareData.fileData.defaultMethod;

      if (!extractionMethod) {
        return { data: [], fields: [], version: null };
      }

      const methodData = shareData.fileData.data[extractionMethod];
      console.log('methodData', methodData);

      if (
        methodData &&
        methodData.status === ProcessorSelectorStatuses.SUCCESS
      ) {
        let extractionData = methodData.data;

        if (extractionMethod === ProcessMethodE.ExtractTable) {
          extractionData = JSON.parse(extractionData);
        } else {
          if (Array.isArray(extractionData)) {
            const { keys, values } = tool.convertMapToArray(extractionData);
            return {
              data: values,
              fields: keys,
              version: ProcessMethodE.ExtractTable,
            };
          }

          if (extractionData?.fields && extractionData?.data) {
            return extractionData;
          }
        }
      } else {
        return { data: [], fields: [], version: null };
      }
    }
    return processedData;
  };

  const onConfirmVertexRes = (jsonStr: string) => {
    if (!jsonStr) return;
    try {
      const res = JSON.parse(jsonStr);
      setParsedVertexJson(res);
      addActionCount(DocumentProcessActionTypes.FIX_EXTRACTION);
      // Update the extractions data
    } catch (err) {
      console.log(err);
    } finally {
      setShowVertexRes(false);
    }
  };

  useEffect(() => {
    if (processForm?.method === ProcessMethodE.Mapping) {
      const currentData = getCurrentExtractionData();

      if (
        currentData &&
        currentData.fields &&
        currentData.fields.length > 0 &&
        shareData.fields
      ) {
        const autoMapping = {};
        const fieldsInDB = Object.keys(shareData.fields);

        fieldsInDB.forEach((dbField) => {
          const fieldConfig = shareData.fields[dbField];
          const matches = fieldConfig.matches || [];

          if (!fieldConfig.enabled) {
            return;
          }

          const matchingField = currentData.fields.find((extractedField) => {
            const extractedLower = extractedField.toLowerCase();
            const searchList = [
              dbField.toLowerCase(),
              ...matches.map((m) => m.toLowerCase()),
            ];
            return searchList.includes(extractedLower);
          });

          if (matchingField) {
            const columnIndex = currentData.fields.indexOf(matchingField);
            autoMapping[dbField] = {
              colIndex: columnIndex,
              colHeader: matchingField,
            };
          }
        });

        if (Object.keys(autoMapping).length > 0) {
          setRowMapping(autoMapping, true);
        }
      }
    }

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [processForm?.method, shareData?.fileData, shareData.fields]);

  useEffect(() => {
    const currentData = getCurrentExtractionData();

    if (
      currentData &&
      currentData.fields &&
      currentData.fields.length > 0 &&
      shareData.fields &&
      Object.keys(rowMapping).length <= 1
    ) {
      const autoMapping = {};
      const fieldsInDB = Object.keys(shareData.fields);

      fieldsInDB.forEach((dbField) => {
        const fieldConfig = shareData.fields[dbField];
        const matches = fieldConfig.matches || [];

        if (!fieldConfig.enabled) {
          return;
        }

        const matchingField = currentData.fields.find((extractedField) => {
          const extractedLower = extractedField.toLowerCase();
          const searchList = [
            dbField.toLowerCase(),
            ...matches.map((m) => m.toLowerCase()),
          ];
          return searchList.includes(extractedLower);
        });

        if (matchingField) {
          const columnIndex = currentData.fields.indexOf(matchingField);
          autoMapping[dbField] = {
            colIndex: columnIndex,
            colHeader: matchingField,
          };
        }
      });

      if (Object.keys(autoMapping).length > 0) {
        setRowMapping(autoMapping, true);
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [shareData?.fileData, selectedExtraction, shareData.fields, rowMapping]);

  useEffect(() => {
    if (shareData?.fileData?.type === FileDataTypeE.InvalidJSON) {
      setInvalidVertexRes(shareData.fileData?.data);
      setShowVertexRes(true);
    }
  }, [shareData?.fileData]);

  useEffect(() => {
    if (parsedVertexJson && parsedVertexJson.length) {
      const { keys, values } = tool.convertMapToArray(parsedVertexJson);
      setProcessedData({
        data: values,
        fields: keys,
        version: ProcessMethodE.AIResults,
      });
    }
  }, [parsedVertexJson, setProcessedData]);

  useEffect(() => {
    let data: string[][] = [];

    const currentData = getCurrentExtractionData();

    if (currentData && currentData.version && currentData.data?.length) {
      data = [...currentData.data];
      if (currentData.sheet) {
        handleProcessFormChange('selectedSheet', currentData.sheet);
      }
    }

    if (data.length) {
      setCustomData(data);
    } else {
      setCustomData([]);
    }
    /*
     * Lint disabled due to uncertain impact on adding missed references.
     *
     * WARN SINCE:  Set/2024
     * MISSED REFs: 'handleProcessFormChange'
     */
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [
    processedData,
    shareData?.fileData,
    selectedExtraction,
    handleProcessFormChange,
  ]);

  /**
   * Get processed data by mapping and custom data (closely like the data in the table)
   */
  useEffect(() => {
    const _keys = Object.keys(rowMapping);
    if (_keys.length && customData.length && shareData.fields) {
      const mapingEntries = Object.entries(rowMapping);
      const rowList = customData.map((item) => {
        const row = mapingEntries.reduce((acc, [k, v]) => {
          // Format value
          let formattedValue: string;
          const targetField = shareData.fields?.[k] || {};
          const index = typeof v === 'object' ? v.colIndex : v;
          if (typeof v === 'string') {
            formattedValue = v;
          } else if (targetField.formatter) {
            formattedValue = targetField.formatter(item[index]);
          } else {
            formattedValue = item[index];
          }
          acc[k] = formattedValue;
          return acc;
        }, {});
        return row;
      });
      setRowDataDef(rowList);
    } else if (!customData.length || !_keys.length) {
      setRowDataDef([]);
    }
  }, [customData, rowMapping, shareData.fields]);

  /**
   * Check if missing required fields
   */
  useEffect(() => {
    const tips = missingRequiredFields.length
      ? `Missing required fields: ${missingRequiredFields.join(', ')}`
      : undefined;

    setErrors((error) => {
      return {
        ...error,
        missingField: tips,
      };
    });
  }, [missingRequiredFields, setErrors]);

  const getTotalRowItem = () => {
    if (!rowDataDef.length || !shareData?.fields) return;
    const mappingKeys = Object.keys(rowMapping);
    const amtKeys = Object.entries(shareData.fields)
      .map(([k, v]) => {
        return v.type === 'currency' && mappingKeys.includes(k) ? k : null;
      })
      .filter(Boolean) as string[];

    const totalRowItem = {};
    amtKeys.forEach((k) => {
      const total = (rowDataDef as any[]).reduce((acc, cur) => {
        if (!cur[k] || cur[k]?.toString().includes('❌')) return acc;
        const calcVal = currency(acc).add(cur[k!]).format();
        return calcVal;
      }, 0);
      totalRowItem[k!] = total;
    });

    return totalRowItem;
  };

  useEffect(() => {
    const totalRowItem = getTotalRowItem();
    setPinnedBottomRowData([totalRowItem]);
    /*
     * Lint disabled due to uncertain impact on adding missed references.
     *
     * WARN SINCE:  Set/2024
     * MISSED REFs: 'getTotalRowItem'
     */
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [rowDataDef, shareData?.fields, rowMapping]);

  useEffect(() => {
    if (!rowDataDef.length) return;
    setProcessFormatData((prev: any) => {
      return {
        ...prev,
        rowData: rowDataDef,
        mappingOptions: rowMapping,
      };
    });
  }, [rowDataDef, rowMapping, setProcessFormatData]);

  useEffect(() => {
    if (pinnedBottomRowData.length) {
      setProcessFormatData((prev: any) => {
        return {
          ...prev,
          cmsTotal: normalizeCurrency(
            pinnedBottomRowData[0]?.commission_amount || ''
          ),
        };
      });
    }
  }, [pinnedBottomRowData, setProcessFormatData]);

  const dataTypeDefinitions: any = useMemo(() => {
    return {
      dateString: {
        baseDataType: 'dateString',
        extendsDataType: 'dateString',
        valueParser: (params) =>
          params.newValue != null &&
          params.newValue.match('\\d{2}/\\d{2}/\\d{4}')
            ? params.newValue
            : null,
        valueFormatter: (params) => (params.value == null ? '' : params.value),
        dataTypeMatcher: (value) =>
          typeof value === 'string' && !!value.match('\\d{2}/\\d{2}/\\d{4}'),
        dateParser: (value) => {
          if (value == null || value === '') {
            return undefined;
          }
          const dateParts = value.split('/');
          return dateParts.length === 3
            ? dayjs.utc(new Date(value)).toDate()
            : undefined;
        },
        dateFormatter: (value) => {
          if (value == null) {
            return undefined;
          }
          const date = String(value.getDate());
          const month = String(value.getMonth() + 1);
          return `${month.length === 1 ? '0' + month : month}/${date.length === 1 ? '0' + date : date}/${value.getFullYear()}`;
        },
      },
    };
  }, []);

  // Load table column by mapping
  useEffect(() => {
    const mappings =
      shareData?.fileData?.type === FileDataTypeE.ChatGPT
        ? shareData.fileData?.data[0]
        : rowMapping;

    const _keys = Object.keys(mappings);
    if (_keys.length) {
      const _columnDefs = _keys.map((k) => {
        const targetField = shareData?.fields?.[k] || {};
        const baseConf: ColDef = {
          headerName: targetField.label || k,
          field: k,
          minWidth: 160,
          editable: (params) => {
            return !params.node.rowPinned;
          },
          filter: true,
          resizable: true,
          valueParser: (params) => {
            if (targetField.formatter && targetField.type !== 'date') {
              return targetField.formatter(params.newValue);
            }
            return params.newValue;
          },
        };
        if (
          (targetField.type && targetField.type === 'date') ||
          k.includes('_date')
        ) {
          return {
            ...baseConf,
            cellEditor: 'agDateStringCellEditor',
            cellEditorParams: {
              min: '1900-01-01',
              max: '2100-01-01',
            },
          } as ColDef;
        } else {
          return baseConf;
        }
      });
      const actionColumn: ColDef = {
        headerName: '',
        field: '',
        pinned: 'left',
        width: 40,
        cellStyle: {
          padding: 0,
          display: 'flex',
          alignItems: 'center',
        },
        cellRenderer: (params) => {
          if (!params.node.rowPinned) {
            return (
              <IconButton
                sx={{
                  ':hover': {
                    opacity: 1,
                    color: 'error.main',
                  },
                  opacity: 0.55,
                }}
                onClick={() => {
                  setRemoveNode(params);
                }}
              >
                <Clear />
              </IconButton>
            );
          } else {
            return (
              <Tooltip title="Totals calculated and provided for review only. This row will not be imported.">
                <IconButton sx={{ opacity: 0.55 }}>
                  <InfoOutlined />
                </IconButton>
              </Tooltip>
            );
          }
        },
      };
      setColumnDefs([actionColumn, ..._columnDefs]);
    } else {
      setColumnDefs([]);
    }
  }, [rowMapping, shareData?.fields, shareData?.fileData]);

  useEffect(() => {
    if (removeNode) {
      gridRef.current!.api.applyTransaction({
        remove: [removeNode.data],
      });

      const rowIndex = removeNode.node.childIndex;
      setCustomData((prev) => {
        return prev.filter((_, index) => index !== rowIndex);
      });

      const fileteredRowData = rowDataDef.filter(
        (_, index) => index !== rowIndex
      );
      setRowDataDef(fileteredRowData);

      addActionCount(DocumentProcessActionTypes.DELETE_DATA);
      setRemoveNode(undefined);
    }
  }, [removeNode, rowDataDef, addActionCount]);

  useEffect(() => {
    if (customData.length > 0 && rowData.companies?.company_name) {
      updateCarrierName();
    }
    /*
     *
     *
     * https://linear.app/fintary/issue/PLT-2291
     *
     * MISSED REFs: updateCarrierName
     * WARN SINCE:  March/2025 ****
     */
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [customData.length, rowData.companies]);

  const onCellValueChanged = (params: CellValueChangedEvent) => {
    addActionCount(DocumentProcessActionTypes.EDIT_DATA);
    const totalRowItem: any = getTotalRowItem();
    if (!isEqual(pinnedBottomRowData[0], totalRowItem)) {
      setPinnedBottomRowData([totalRowItem]);
    }

    const {
      value,
      colDef: { field },
      rowIndex,
    } = params;
    if (field === 'action') return;
    if (rowIndex !== null && field) {
      const newData = [...customData];

      const allFieldCellIsEmpty = newData.every((row) => {
        const index =
          typeof rowMapping[field] === 'object'
            ? rowMapping[field].colIndex
            : rowMapping[field];
        return !row[index];
      });

      const isAddedField = typeof rowMapping[field] === 'string';
      if (isAddedField) {
        setRowMapping({
          [field]: value,
        });
      } else {
        if (allFieldCellIsEmpty) {
          newData.forEach((row) => {
            const index =
              typeof rowMapping[field] === 'object'
                ? rowMapping[field].colIndex
                : rowMapping[field];
            row[index] = value;
          });
          setCustomData(newData);
        } else {
          const index =
            typeof rowMapping[field] === 'object'
              ? rowMapping[field].colIndex
              : rowMapping[field];
          newData[rowIndex][index] = value;
          setCustomData(newData);
        }
      }
    }

    setProcessFormatData((prev: any) => {
      return {
        ...prev,
        rowData: rowDataDef,
      };
    });
  };

  const onFilterChanged = (_params: FilterChangedEvent) => {
    const filteredRows = getAllRows();
    setProcessFormatData((prev: any) => {
      return {
        ...prev,
        rowData: filteredRows,
      };
    });
    addActionCount(DocumentProcessActionTypes.FILTER_DATA);
  };

  const gridOptions = useMemo(() => {
    return {
      defaultColDef: {
        sortable: true,
        flex: 1,
      },
      animateRows: true,
      suppressMenuHide: true,
      alwaysShowHorizontalScroll: true,
    };
  }, []);

  const addField = () => {
    const currentFields = columnDefs.map((item) => item.field).filter(Boolean);
    const _canAddFields = shareData.allFieldKeys
      .filter((item) => item.model === 'statements')
      .map((item) => {
        return !currentFields.includes(item.key) ? item : null;
      })
      .filter(Boolean) as IFieldsProps[];
    setCanAddFields(_canAddFields);
    setOpen(true);
  };

  const removeField = () => {
    const currentFields = columnDefs.map((item) => item.field).filter(Boolean);
    const formattedFields = currentFields
      .filter((s) => s && !s.includes('action'))
      .map((k) => {
        const target = shareData.allFieldKeys.find((item) => item.key === k);
        if (target) {
          return target;
        } else {
          return {
            key: k,
            label: k,
            type: 'text',
          };
        }
      });
    setCanRemoveFields(formattedFields as any);
    setShowRemove(true);
  };

  const generateNewCustomedRowData = () => {
    const currentData = getCurrentExtractionData();
    const processedDataLength =
      currentData?.fields?.length || processedData.fields.length;
    const newRow = Array.from({ length: processedDataLength }, () => '');
    return newRow;
  };

  const getAllRows = () => {
    if (!gridRef.current) return [];
    const rows = gridRef.current.api.getRenderedNodes();
    return rows.map((row) => row.data);
  };

  const addRow = () => {
    const newRowData = generateNewCustomedRowData();
    const allRows = [newRowData, ...customData];
    setCustomData(allRows);
    addActionCount(DocumentProcessActionTypes.ADD_DATA);
  };

  const onAddFields = (data: IFieldsProps | undefined) => {
    addActionCount(DocumentProcessActionTypes.ADD_DATA);
    if (!data) return;
    const baseConf: ColDef = {
      headerName: data.label,
      field: data.key,
      minWidth: 160,
      editable: true,
      filter: true,
      resizable: true,
    };
    let option = baseConf;
    if (data.key.includes('_date') || data.type === 'date') {
      option = {
        ...baseConf,
        cellEditor: 'agDateStringCellEditor',
        cellEditorParams: {
          min: '01/01/2020',
          max: '01/01/2050',
        },
      } as ColDef;
    }
    setColumnDefs(columnDefs.concat(option));

    setRowMapping({ [data.key]: '' });

    setOpen(false);
  };

  const onRemoveFields = (data: IFieldsProps[]) => {
    addActionCount(DocumentProcessActionTypes.DELETE_DATA);
    if (!data.length) return;
    const delList = data.map((item) => item.key);
    const newColumns = columnDefs.filter((item) => {
      return !delList.includes(item.field!);
    }) as any;

    setColumnDefs(newColumns);

    const newMapping = { ...rowMapping };
    delList.forEach((k) => {
      Reflect.deleteProperty(newMapping, k);
    });
    setRowMapping(newMapping);
    setShowRemove(false);
  };

  const getRowStyle = (params) => {
    if (params.node.rowPinned) {
      return { background: '#F0F0F0' };
    }
  };

  const updateCarrierName = () => {
    let carrierNameIndex;

    if (!columnDefs.some((col) => col.field === 'carrier_name')) {
      const carrierColumn = {
        headerName: 'Paying entity',
        field: 'carrier_name',
        minWidth: 160,
        editable: true,
        filter: true,
        resizable: true,
      };
      setColumnDefs((prev) => [...prev, carrierColumn]);
      carrierNameIndex = customData[0]?.length || 0;
      setRowMapping({ carrier_name: carrierNameIndex });
    } else {
      const mapping = rowMapping['carrier_name'];
      if (
        typeof mapping === 'object' &&
        mapping !== null &&
        'colIndex' in mapping
      ) {
        carrierNameIndex = mapping.colIndex;
      } else if (typeof mapping === 'number') {
        carrierNameIndex = mapping;
      } else {
        carrierNameIndex = customData[0]?.length || 0;
        setRowMapping({ carrier_name: carrierNameIndex });
      }
    }

    const updatedCustomData = customData.map((row) => {
      if (!Array.isArray(row)) return row;

      const newRow = [...row];
      if (newRow.length <= carrierNameIndex) {
        newRow.length = carrierNameIndex + 1;
        newRow.fill('', row.length, carrierNameIndex);
      }

      newRow[carrierNameIndex] = rowData.companies?.company_name || '';
      return newRow;
    });

    setCustomData(updatedCustomData);
  };

  return (
    <>
      <Box
        sx={{
          height: '100%',
          display: 'flex',
          flexDirection: 'column',
          gap: 1,
          overflow: 'auto',
        }}
      >
        <Box
          sx={{
            display: 'flex',
            width: '100%',
            gap: 1,
            alignItems: 'center',
            justifyContent: 'flex-end',
          }}
        >
          <TextField
            size="small"
            placeholder="Search..."
            onChange={(e) => setResultTableSearch(e.target.value)}
            sx={{
              width: '200px',
              '& .MuiOutlinedInput-root': {
                borderRadius: '20px',
                height: '32px',
              },
              '& .MuiInputLabel-root': {
                lineHeight: '1em',
              },
            }}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <Search />
                </InputAdornment>
              ),
            }}
          />
          {rowDataDef.length ? (
            <Chip
              sx={{
                minWidth: '90px',
              }}
              label={`Rows: ${gridRef?.current?.api.getDisplayedRowCount()}`}
            />
          ) : (
            ''
          )}
          <Button variant="outlined" onClick={addField}>
            Add field
          </Button>
          <Button variant="outlined" onClick={addRow}>
            Add row
          </Button>
          <Button variant="outlined" onClick={removeField}>
            Remove field
          </Button>
          <Tooltip title="Export as CSV" placement="bottom">
            <IconButton
              onClick={() => {
                exportCsv(
                  Object.keys(rowDataDef[0]).filter((k) => k !== 'action'),
                  rowDataDef,
                  `Fintary-Preview-Export.csv`
                );
              }}
              disabled={!rowDataDef[0]}
            >
              <FileDownload sx={{ color: '#aaa' }} />
            </IconButton>
          </Tooltip>
          <Tooltip title="Double-click cell to edit" placement="left">
            <InfoOutlined sx={{ color: '#ccc' }} />
          </Tooltip>
        </Box>
        <Box
          sx={{ width: '100%', flex: 1, overflow: 'auto' }}
          className="ag-theme-material"
        >
          <AgGridReact
            ref={gridRef}
            headerHeight={40}
            rowData={rowDataDef}
            columnDefs={columnDefs}
            onCellValueChanged={onCellValueChanged}
            onFilterChanged={onFilterChanged}
            dataTypeDefinitions={dataTypeDefinitions}
            getRowStyle={getRowStyle}
            pinnedBottomRowData={pinnedBottomRowData}
            quickFilterText={resultTableSearch}
            {...gridOptions}
          />
        </Box>
      </Box>
      <RemoveFieldsDialog
        open={showRemove}
        fieldsSource={canRemoveFields}
        onClose={() => setShowRemove(false)}
        onConfirm={onRemoveFields}
      />
      <VertexResult
        open={showVertexRes}
        onClose={() => setShowVertexRes(false)}
        onConfirm={onConfirmVertexRes}
        json={invalidVertexRes}
        extraction={shareData?.fileData?.extraction}
      />
      <AddFieldsDialog
        open={open}
        fieldsSource={canAddFields}
        onClose={() => setOpen(false)}
        onConfirm={onAddFields}
      />
    </>
  );
};

export default CommissionPreview;
