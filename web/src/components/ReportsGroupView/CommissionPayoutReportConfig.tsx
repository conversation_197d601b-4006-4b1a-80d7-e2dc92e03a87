import { LoadingButton } from '@mui/lab';
import {
  Box,
  Button,
  Checkbox,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  FormControlLabel,
  TextField,
  ToggleButton,
  ToggleButtonGroup,
  Tooltip,
  Typography,
} from '@mui/material';
import {
  AgentCommissionsStatusesLabels,
  CompReportDateFilter,
  CompReportPrefixConfig,
} from 'common/globalTypes';
import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';
import { useEffect, useMemo, useState } from 'react';

import BasicDateRangePicker from '@/common/BasicDateRangePicker';
import { EnhancedSelect } from '@/components/molecules/EnhancedSelect';
import useSnackbar from '@/contexts/useSnackbar';
import API from '@/services/API';
import Formatter from '@/services/Formatter';
import Statements from '@/services/Statements';
import { useAccountStore } from '@/store';
import { AccountSettings } from '@/types';
import { BlankLabel } from '@/components/molecules/EnhancedSelect/constants';

dayjs.extend(utc);

type Agent = {
  id: number;
  name: string;
};

type AgentGroup = {
  id: number;
  name: string;
  contacts: Agent[];
};

type CommissionPayoutReportConfigProps = {
  open: boolean;
  setOpen: (open: boolean) => void;
  refreshData?: () => void;
  accountSettings: AccountSettings;
};

enum ReportDefaults {
  Prefix = 'Commissions: start_date - end_date',
}

const CommissionPayoutReportConfig = ({
  open,
  setOpen,
  refreshData = () => {},
  accountSettings,
}: CommissionPayoutReportConfigProps) => {
  const [startDate, setStartDate] = useState<dayjs.Dayjs | string | null>(
    dayjs.utc().startOf('day')
  );
  const [endDate, setEndDate] = useState<dayjs.Dayjs | string | null>(
    dayjs.utc().startOf('day')
  );
  const [agents, setAgents] = useState<number[]>([]);
  const [agentGroups, setAgentGroups] = useState<number[]>([]);
  const [compensationTypes, setCompensationTypes] = useState<string[]>([]);
  const [contactStatuses, setContactStatuses] = useState<string[]>([]);
  const [contactPayableStatuses, setContactPayableStatuses] = useState<
    string[]
  >([]);
  const [agentCommissionStatuses, setAgentCommissionStatuses] = useState<
    string[]
  >([]);
  const [agentCommissionStatuses2, setAgentCommissionStatuses2] = useState<
    string[]
  >(Object.values(AgentCommissionsStatusesLabels));
  const [loading, setLoading] = useState(false);
  const [isAgentGroupSelectionEnabled, setIsAgentGroupSelectionEnabled] =
    useState(false);
  const [selectedDateFilter, setSelectedDateFilter] = useState<string>(
    CompReportDateFilter.ProcessingDate
  );
  const [selectedReportPrefix, setSelectedReportPrefix] = useState<string>(
    CompReportPrefixConfig.Default
  );
  const [reportPrefix, setReportPrefix] = useState<string>(
    ReportDefaults.Prefix
  );
  const [includeLinkedCommissions, setIncludeLinkedCommissions] =
    useState<boolean>(false);
  const [includeZeroSplitCommissions, setIncludeZeroSplitCommissions] =
    useState<boolean>(false);

  const { selectedAccount } = useAccountStore();

  const mode = selectedAccount?.accountMode;
  const statements = new Statements(mode);

  const isContactsGroupsEnabled =
    accountSettings?.pages_settings?.agents_groups?.show_page ?? true;

  const { data: _agentGroups, isFetched: _isAgentGroupsFetched } =
    API.getBasicQuery('contacts/groups', '', isContactsGroupsEnabled);
  const agentGroupList: AgentGroup[] = _agentGroups?.sort((a, b) =>
    a.name > b.name ? 1 : -1
  );

  const poster = API.getMutation(
    'saved_reports/groups/gen_commissions_report',
    'POST'
  );

  const isValidOrNull = (date: any) => {
    if (date === null || date == '') return true;
    return dayjs(date).isValid();
  };

  const shouldFetchContacts =
    isValidOrNull(startDate) && isValidOrNull(endDate);

  const { data: _agentList, isFetched } = API.getBasicQuery(
    'contacts/commission-contacts',
    `date_start=${startDate}&date_end=${endDate}&date_type=${selectedDateFilter}`,
    shouldFetchContacts
  );

  const { data: _contactOptions, isFetched: isFetchedContactOptions } =
    API.getBasicQuery(`contacts/options`);
  const {
    data: _statementDataOptions,
    isFetched: isFetchedStatementDataOptions,
  } = API.getBasicQuery(`statement_data/options`);

  const { showSnackbar } = useSnackbar();

  const agentList = useMemo(
    () =>
      _agentList?.data?.sort((a: any, b: any) =>
        Formatter.contact(a, { account_id: selectedAccount?.accountId }) >
        Formatter.contact(b, { account_id: selectedAccount?.accountId })
          ? 1
          : -1
      ) ?? [],
    [_agentList, selectedAccount?.accountId]
  );

  const contactStatusesSet = new Set<string | null>(_contactOptions?.status);
  contactStatusesSet.add(BlankLabel);

  const contactStatusesList = Array.from(contactStatusesSet).sort((a, b) => {
    const strA = String(a || '');
    const strB = String(b || '');
    if (strA === BlankLabel) return -1;
    if (strB === BlankLabel) return 1;
    return (strA as string).localeCompare(strB as string);
  });

  const contactPayableStatusesSet = new Set<string | null>(
    _contactOptions?.payable_status
  );
  contactPayableStatusesSet.add(BlankLabel);

  const contactPayableStatusesList = Array.from(contactPayableStatusesSet).sort(
    (a, b) => {
      const strA = String(a || '');
      const strB = String(b || '');
      if (strA === BlankLabel) return -1;
      if (strB === BlankLabel) return 1;
      return (strA as string).localeCompare(strB as string);
    }
  );

  const statementCompensationTypesList = useMemo(
    () => [
      BlankLabel,
      ...(_statementDataOptions?.compensation_type?.sort((a, b) => {
        const strA = String(a || '');
        const strB = String(b || '');
        return strA.localeCompare(strB);
      }) || []),
    ],
    [_statementDataOptions]
  );

  const statementAgentCommissionStatusesList = useMemo(
    () => [
      BlankLabel,
      ...(_statementDataOptions?.agent_commissions_status?.sort((a, b) => {
        const strA = String(a || '');
        const strB = String(b || '');
        return strA.localeCompare(strB);
      }) || []),
    ],
    [_statementDataOptions]
  );
  const agentValuer = (val) => val.id;

  useEffect(() => {
    if (isFetched && agents?.length === 0 && agentList?.length > 0) {
      setAgents(agentList.map((agent) => agentValuer(agent)));
    }

    if (
      _isAgentGroupsFetched &&
      agentGroups?.length === 0 &&
      agentGroupList?.length > 0
    ) {
      setAgentGroups(
        agentGroupList.map((agentGroup) => agentValuer(agentGroup))
      );
    }

    if (
      isFetchedContactOptions &&
      ((contactStatuses?.length === 0 && contactStatusesList?.length > 0) ||
        (contactPayableStatuses?.length === 0 &&
          contactPayableStatusesList?.length > 0))
    ) {
      setContactStatuses(contactStatusesList as string[]);
      setContactPayableStatuses(contactPayableStatusesList as string[]);
    }

    if (
      isFetchedStatementDataOptions &&
      ((compensationTypes?.length === 0 &&
        statementCompensationTypesList?.length > 0) ||
        (agentCommissionStatuses?.length === 0 &&
          statementAgentCommissionStatusesList?.length > 0))
    ) {
      setCompensationTypes(statementCompensationTypesList as string[]);
      setAgentCommissionStatuses(
        statementAgentCommissionStatusesList.filter(
          (status) => String(status || '').toLowerCase() !== 'paid'
        )
      );
    }
  }, [
    agentList,
    isFetched,
    agentGroupList,
    _isAgentGroupsFetched,
    isFetchedContactOptions,
    isFetchedStatementDataOptions,
    contactStatusesList,
    contactPayableStatusesList,
    statementCompensationTypesList,
    statementAgentCommissionStatusesList,
    agents?.length,
    agentGroups?.length,
    contactStatuses?.length,
    contactPayableStatuses?.length,
    compensationTypes?.length,
    agentCommissionStatuses?.length,
  ]);

  useEffect(() => {
    if (
      _isAgentGroupsFetched &&
      isAgentGroupSelectionEnabled &&
      agentGroups.length > 0
    ) {
      const agentGroupContacts = agentGroups.flatMap((agentGroupId) => {
        const agentGroup = agentGroupList.find(
          (group) => group.id === agentGroupId
        );
        return agentGroup && Array.isArray(agentGroup.contacts)
          ? agentGroup.contacts.map((contact) => contact.id)
          : [];
      });
      setAgents(agentGroupContacts);
    } else if (isFetched) {
      setAgents(agentList.map((agent) => agentValuer(agent)));
    }
  }, [
    agentGroupList,
    _isAgentGroupsFetched,
    agentGroups,
    isAgentGroupSelectionEnabled,
    isFetched,
    agentList,
  ]);

  const applyLegacyDynamicSelectsWorkaround = (
    statementFields: typeof statements.fields
  ) => {
    // For old dynamic selects, it uses "table" key to get dynamic data,
    // but the Statements is using new dynamic selects config
    // which is not compatible with the old one and cause issue at report page.
    // So we need to add back "table" key to make it work at report page.
    // This is not a good solution and we need to refactor the code later
    const fields = { ...statementFields };
    statements.dynamicSelectsConfig?.forEach(({ table, collectDataFields }) => {
      collectDataFields.forEach((field) => {
        if (field in fields) {
          fields[field].table = table;
        }
      });
    });
    return fields;
  };

  const genReport = async () => {
    try {
      const fields = applyLegacyDynamicSelectsWorkaround(statements.fields);
      const response = await poster.mutateAsync({
        start_date: startDate,
        end_date: endDate,
        dateFilter: selectedDateFilter,
        reportPrefix:
          reportPrefix === ReportDefaults.Prefix ? '' : reportPrefix,
        contacts: agents,
        compensation_types: compensationTypes,
        contact_statuses: contactStatuses,
        contact_payable_statuses: contactPayableStatuses,
        agent_commission_statuses: agentCommissionStatuses,
        agent_commission_statuses2: agentCommissionStatuses2,
        include_linked_commissions: includeLinkedCommissions,
        include_zero_split_commissions: includeZeroSplitCommissions,
        headers: JSON.stringify(Object.values(fields)),
      });
      if (response.data?.reports?.length > 0) {
        showSnackbar(
          `${response.data?.reports?.length} reports generated`,
          'info'
        );
        refreshData();
      } else {
        showSnackbar('No reports generated', 'error');
      }
    } catch (error: any) {
      showSnackbar(error?.message || error, 'error');
    }
  };

  const agentListWithFormattedName = useMemo(() => {
    const list =
      agentList?.map((agent) => {
        agent.name = Formatter.contact(agent, {
          account_id: selectedAccount?.accountId,
        });
        return agent;
      }) || [];
    return list;
  }, [agentList, selectedAccount?.accountId]);

  return (
    <Dialog open={open} onClose={() => setOpen(false)}>
      <DialogTitle>Generate comp payout report</DialogTitle>
      <DialogContent sx={{ width: 450 }}>
        <Box
          sx={{
            p: 1,
            mt: 1,
            borderStyle: 'solid',
            borderColor: 'silver',
            borderWidth: 1,
            borderRadius: 4,
            display: 'inline-block',
            width: '100%',
            backgroundColor: '#2196f30a',
          }}
        >
          <Box
            display="flex"
            justifyContent="space-between"
            alignItems="center"
            position="relative"
            sx={{ ml: 0.5, mb: 0.5 }}
          >
            <Typography variant="body2">Report settings</Typography>
          </Box>
          <Box sx={{ display: 'flex', flexDirection: 'column' }}>
            <ToggleButtonGroup
              sx={{ height: '20px' }}
              value={selectedReportPrefix}
              exclusive
              onChange={(event, reportPrefix) => {
                setSelectedReportPrefix(reportPrefix);
                if (reportPrefix === CompReportPrefixConfig.Default) {
                  setReportPrefix(ReportDefaults.Prefix);
                }
              }}
              color="primary"
            >
              <ToggleButton
                value={CompReportPrefixConfig.Default}
                sx={{
                  borderRadius: '12px',
                }}
              >
                Default prefix
              </ToggleButton>
              <ToggleButton
                value={CompReportPrefixConfig.Custom}
                sx={{
                  borderRadius: '12px',
                }}
              >
                Custom prefix
              </ToggleButton>
            </ToggleButtonGroup>
            <Box sx={{ mt: 2 }}>
              <TextField
                label="Report prefix"
                value={reportPrefix}
                onChange={(event) => setReportPrefix(event.target.value)}
                fullWidth
                disabled={
                  selectedReportPrefix === CompReportPrefixConfig.Default
                }
              />
            </Box>
          </Box>
        </Box>
        <Box
          sx={{
            p: 1,
            mt: 1,
            borderStyle: 'solid',
            borderColor: 'silver',
            borderWidth: 1,
            borderRadius: 4,
            display: 'inline-block',
            width: '100%',
            backgroundColor: '#2196f30a',
          }}
        >
          <Box
            display="flex"
            justifyContent="space-between"
            alignItems="center"
            position="relative"
            sx={{ ml: 0.5, mb: 0.5 }}
          >
            <Typography variant="body2">Date filters</Typography>
          </Box>
          <Box sx={{ display: 'flex', flexDirection: 'column' }}>
            <ToggleButtonGroup
              sx={{ height: '20px' }}
              value={selectedDateFilter}
              exclusive
              onChange={(event, selectedDateFilter) => {
                setSelectedDateFilter(selectedDateFilter);
              }}
              color="primary"
            >
              <ToggleButton
                value={CompReportDateFilter.ProcessingDate}
                sx={{
                  borderRadius: '12px',
                }}
              >
                Processing date
              </ToggleButton>
              <ToggleButton
                value={CompReportDateFilter.PaymentDate}
                sx={{
                  borderRadius: '12px',
                }}
              >
                Payment date
              </ToggleButton>
            </ToggleButtonGroup>
            <Box sx={{ mt: 2 }}>
              <BasicDateRangePicker
                range={{
                  startDate: startDate,
                  startDateLabel:
                    selectedDateFilter === CompReportDateFilter.ProcessingDate
                      ? 'Processing date start'
                      : 'Payment date start',
                  endDate: endDate,
                  endDateLabel:
                    selectedDateFilter === CompReportDateFilter.ProcessingDate
                      ? 'Processing date end'
                      : 'Payment date end',
                }}
                onChange={(range) => {
                  setStartDate(range.startDate);
                  setEndDate(range.endDate);
                }}
                justify="left"
              />
            </Box>
          </Box>
        </Box>
        <Box
          sx={{
            p: 1,
            mt: 1,
            borderStyle: 'solid',
            borderColor: 'silver',
            borderWidth: 1,
            borderRadius: 4,
            display: 'inline-block',
            width: '100%',
            backgroundColor: '#2196f30a',
          }}
        >
          <Box
            display="flex"
            justifyContent="space-between"
            alignItems="center"
            position="relative"
            sx={{ ml: 0.5, mb: 0.5 }}
          >
            <Typography variant="body2">Agent filters</Typography>
          </Box>
          <Box sx={{ display: 'flex', flexDirection: 'column' }}>
            {isContactsGroupsEnabled && (
              <ToggleButtonGroup
                sx={{ height: '20px' }}
                value={isAgentGroupSelectionEnabled ? 'agentGroups' : 'agents'}
                exclusive
                onChange={(event, newSelectionType) => {
                  setIsAgentGroupSelectionEnabled(
                    newSelectionType === 'agentGroups'
                  );
                }}
                color="primary"
              >
                <ToggleButton
                  value="agents"
                  sx={{
                    borderRadius: '12px',
                  }}
                >
                  Agents
                </ToggleButton>
                <ToggleButton
                  value="agentGroups"
                  sx={{
                    borderRadius: '12px',
                  }}
                >
                  Agent groups
                </ToggleButton>
              </ToggleButtonGroup>
            )}
            {agentListWithFormattedName.length === 0 ? (
              <Typography variant="body2" sx={{ ml: 0.5, mt: 1 }}>
                No available agents given the selected date range
              </Typography>
            ) : (
              <>
                <Box sx={{ mt: 1.5 }}>
                  {isAgentGroupSelectionEnabled ? (
                    <>
                      <EnhancedSelect
                        label="Agent group(s)"
                        multiple
                        options={agentGroupList || []}
                        value={agentGroupList?.filter((group) =>
                          agentGroups.includes(group.id)
                        )}
                        onChange={(v) =>
                          setAgentGroups(v.map((group) => group.id))
                        }
                        enableSearch
                      />
                    </>
                  ) : (
                    <>
                      <EnhancedSelect
                        sx={{ width: '100%' }}
                        label="Agent(s)"
                        enableSearch
                        multiple
                        options={agentListWithFormattedName}
                        value={agentListWithFormattedName.filter((agent) =>
                          agents.includes(agent.id)
                        )}
                        onChange={(v) => {
                          setAgents(v.map((agent) => agent.id));
                        }}
                        isLoading={!isFetched}
                      />
                    </>
                  )}
                </Box>
                <Box sx={{ mt: 1.5 }}>
                  <EnhancedSelect
                    label="Agent status"
                    multiple
                    enableSearch
                    options={contactStatusesList as string[]}
                    value={contactStatuses}
                    onChange={(v) => setContactStatuses(v)}
                    sx={{ width: '100%' }}
                  />
                </Box>
                <Box sx={{ mt: 1.5 }}>
                  <EnhancedSelect
                    label="Agent payable status"
                    multiple
                    enableSearch
                    options={contactPayableStatusesList as string[]}
                    value={contactPayableStatuses}
                    onChange={(v) => setContactPayableStatuses(v)}
                    sx={{ width: '100%' }}
                  />
                </Box>
              </>
            )}
          </Box>
        </Box>
        <Box
          sx={{
            mt: 1,
            p: 1,
            borderStyle: 'solid',
            borderColor: 'silver',
            borderWidth: 1,
            borderRadius: 4,
            display: 'inline-block',
            width: '100%',
            backgroundColor: '#2196f30a',
          }}
        >
          <Box
            display="flex"
            justifyContent="space-between"
            alignItems="center"
            position="relative"
            sx={{ ml: 0.5, mb: 0.5 }}
          >
            <Typography variant="body2">Commission filters</Typography>
          </Box>
          <Box sx={{ display: 'flex', flexDirection: 'column' }}>
            <Box sx={{ ml: 1 }}>
              <FormControlLabel
                control={
                  <Checkbox
                    checked={includeLinkedCommissions}
                    onChange={(e) =>
                      setIncludeLinkedCommissions(e.target.checked)
                    }
                  />
                }
                label="Include linked commissions"
              />
              <FormControlLabel
                control={
                  <Checkbox
                    checked={includeZeroSplitCommissions}
                    onChange={(e) =>
                      setIncludeZeroSplitCommissions(e.target.checked)
                    }
                  />
                }
                label="Include zero split commissions"
              />
            </Box>
            <Box sx={{ mt: 1.5 }}>
              <EnhancedSelect
                label="Compensation type"
                multiple
                enableSearch
                options={statementCompensationTypesList}
                value={compensationTypes}
                onChange={(v) => setCompensationTypes(v)}
                sx={{ width: '100%' }}
              />
            </Box>
            <Box sx={{ mt: 1.5 }}>
              <EnhancedSelect
                label="Payout status"
                multiple
                enableSearch
                options={statementAgentCommissionStatusesList}
                value={agentCommissionStatuses}
                onChange={(v) => setAgentCommissionStatuses(v)}
                sx={{ width: '100%' }}
              />
            </Box>
            <Box sx={{ mt: 1.5 }}>
              <EnhancedSelect
                label="Per agent payout status"
                multiple
                enableSearch
                options={Object.values(AgentCommissionsStatusesLabels)}
                value={agentCommissionStatuses2}
                onChange={(v) => setAgentCommissionStatuses2(v)}
                sx={{ width: '100%' }}
              />
            </Box>
          </Box>
        </Box>
      </DialogContent>
      <DialogActions sx={{ pt: 0 }}>
        <Button onClick={() => setOpen(false)}>Cancel</Button>
        <Tooltip
          title={
            agentListWithFormattedName.length === 0
              ? ' No available agents given the selected date range.'
              : ''
          }
        >
          <span>
            <LoadingButton
              variant="contained"
              loading={loading}
              onClick={async () => {
                setLoading(true);
                await genReport();
                setLoading(false);
                setOpen(false);
              }}
              disabled={agentListWithFormattedName.length === 0}
            >
              Generate
            </LoadingButton>
          </span>
        </Tooltip>
      </DialogActions>
    </Dialog>
  );
};

export default CommissionPayoutReportConfig;
