import React from 'react';
import { render } from '@testing-library/react';
import { ThemeProvider } from '@mui/material';
import { createTheme } from '@mui/material/styles';

import DialogHost from '@/components/DialogHost';

jest.mock('@/components/EmailVerificationDialog', () => () => (
  <div>EmailVerificationDialog</div>
));

describe('DialogHost', () => {
  const theme = createTheme({});

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('renders without crashing', () => {
    const mockDialogs = {
      signUpDialog: {
        dialogProps: {
          onClose: jest.fn(),
          open: false,
        },
      },
      signInDialog: {
        dialogProps: {
          onClose: jest.fn(),
          open: false,
        },
      },
      emailVerificationDialog: {
        dialogProps: {
          onClose: jest.fn(),
          open: false,
        },
      },
    };

    render(
      <ThemeProvider theme={theme}>
        <DialogHost theme={theme} user={null} dialogs={mockDialogs} />
      </ThemeProvider>
    );
  });

  it('should not render any dialog if user is present', () => {
    const mockDialogs = {
      emailVerificationDialog: {
        dialogProps: {
          onClose: jest.fn(),
          open: false,
        },
      },
    };

    const { queryByText } = render(
      <ThemeProvider theme={theme}>
        <DialogHost
          theme={theme}
          user={{
            email: '<EMAIL>',
            uid: '**********',
            emailVerified: true,
          }}
          dialogs={mockDialogs}
        />
      </ThemeProvider>
    );
    expect(queryByText('EmailVerificationDialog')).not.toBeInTheDocument();
  });
  it("should render email verification dialog if user is present and it's email is not verified", () => {
    const mockDialogs = {
      signUpDialog: {
        dialogProps: {
          onClose: jest.fn(),
          open: false,
        },
      },
      signInDialog: {
        dialogProps: {
          onClose: jest.fn(),
          open: false,
        },
      },
      emailVerificationDialog: {
        dialogProps: {
          onClose: jest.fn(),
          open: true,
        },
      },
    };

    const { queryByText } = render(
      <ThemeProvider theme={theme}>
        <DialogHost
          theme={theme}
          user={{
            email: '<EMAIL>',
            uid: '**********',
            emailVerified: false,
          }}
          dialogs={mockDialogs}
        />
      </ThemeProvider>
    );
    expect(queryByText('EmailVerificationDialog')).toBeInTheDocument();
  });
});
