import { Theme } from '@mui/material/styles';

import EmailVerificationDialog from '@/components/EmailVerificationDialog';

interface DialogProps {
  dialogProps: {
    onClose: (callback?: () => void) => void;
    open: boolean;
    email?: string;
  };
}

interface Dialogs {
  emailVerificationDialog: DialogProps;
}

interface User {
  email: string;
  uid: string;
  emailVerified: boolean;
}

interface DialogHostProps {
  theme: Theme;
  user: User | null;
  dialogs: Dialogs;
}

const DialogHost: React.FC<DialogHostProps> = ({ theme, user, dialogs }) => {
  const { emailVerificationDialog } = dialogs;

  return (
    <>
      {!user?.emailVerified && (
        <>
          <EmailVerificationDialog
            dialogProps={emailVerificationDialog.dialogProps}
            theme={theme}
          />
        </>
      )}
    </>
  );
};

export default DialogHost;
