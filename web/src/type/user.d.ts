import { USER_SUB_TYPES, USER_TYPES } from 'common/user.constants';

import { Roles, States } from '@/types';
import type { User as FirebaseUser } from '@/firebase';

declare global {
  type AdminUser = {
    isAdmin: boolean;
    email: string;
    sub_type: keyof typeof USER_SUB_TYPES;
    type: keyof typeof USER_TYPES;
  };

  type User = FirebaseUser & {
    email: string;
    uid: string;
    emailVerified: boolean;
    role_name?: string;
    sso?: boolean;
  };

  type UserState = {
    userOverallState: States;
    userEmail?: string;
    userOnboardingNeeded: boolean;
    accountOnboardingNeeded: boolean;
    userAccounts: UserAccount[];
  };

  type UserLogin = {
    action: string;
    error: {
      message: string;
    };
    response: UserState;
    statusText: string;
    type: string;
  };

  type UserAccount = {
    account_id: string;
    role_id: Roles;
    state: States;
    account: {
      str_id: string;
      name: string;
      short_name: string;
      mode: string;
      comp_grids_enabled: boolean;
      accounting_transactions_enabled: boolean;
      white_label_mode: boolean;
      logo_url: string;
      uid: string;
    };
  };
}
