import * as Sentry from '@sentry/react';
import { TRANSGLOBAL_SIGNIN_URL } from 'common/customer/customer.constants';

import firebase, { analytics, auth, firestore } from '@/firebase';
import { LOCAL_STORAGE_KEYS } from '@/constants/account';

const isFirebaseError = (payload: unknown): payload is firebase.FirebaseError =>
  payload instanceof Error && payload.name === 'FirebaseError';

const calculateFriendlyFirebaseMessage = (error: firebase.FirebaseError) => {
  switch (error.code) {
    case 'auth/invalid-credential':
      return `Incorrect email or password. Please try again or reset your password.`;
    case 'auth/too-many-requests':
      return 'Too many login attempts. Please try again later.';
    default:
      return error.message.replace(/Firebase: (.*) \(.*\)\./, '$1');
  }
};

const authentication = {
  signUpWithEmailAddressAndPassword: async (
    emailAddress: string,
    password: string
  ) => {
    if (!emailAddress || !password) {
      throw new Error('No e-mail address or password');
    }

    if (auth.currentUser) {
      throw new Error('No current user');
    }

    let user: firebase.User | null = null;

    try {
      const createUserResult = await auth.createUserWithEmailAndPassword(
        emailAddress,
        password
      );
      user = createUserResult.user;
    } catch (error: unknown) {
      if (isFirebaseError(error)) {
        throw new Error(calculateFriendlyFirebaseMessage(error));
      }
      throw error;
    }

    if (!user) {
      throw new Error('No user');
    }

    const { uid } = user;

    if (!uid) {
      throw new Error('No UID');
    }

    const userDocumentReference = firestore.collection('users').doc(uid);

    await userDocumentReference.set({}, { merge: true });

    analytics.logEvent('sign_up', {
      method: 'password',
    });

    return user;
  },
  signIn: async (emailAddress: string, password: string) => {
    if (!emailAddress || !password) {
      throw new Error('No e-mail address or password');
    }

    if (auth.currentUser) {
      throw new Error('Cannot sign in when a user is already signed in');
    }

    let user: firebase.User | null = null;
    try {
      const signInResult = await auth.signInWithEmailAndPassword(
        emailAddress,
        password
      );
      user = signInResult.user;
    } catch (error: unknown) {
      if (isFirebaseError(error)) {
        throw new Error(calculateFriendlyFirebaseMessage(error));
      }
      throw error;
    }

    if (!user) {
      throw new Error('No user');
    }

    const { uid } = user;

    if (!uid) {
      throw new Error('No UID');
    }

    const userDocumentReference = firestore.collection('users').doc(uid);

    const userDocument = await userDocumentReference.get({
      source: 'server',
    });

    if (!userDocument.exists) {
      await userDocumentReference.set({}, { merge: true });
    }

    analytics.logEvent('login', {
      method: 'password',
    });

    return user;
  },
  signInLinkToEmail: async (emailAddress: string) => {
    if (!emailAddress) {
      throw new Error('No e-mail address');
    }

    if (auth.currentUser) {
      throw new Error('No current user');
    }

    const url = process.env.REACT_APP_HOMEPAGE;

    if (typeof url === 'undefined') {
      Sentry.captureException(
        'REACT_APP_HOMEPAGE environment variable is undefined'
      );
      throw new Error('REACT_APP_HOMEPAGE environment variable is undefined');
    }

    const actionCodeSettings = {
      url: url,
      handleCodeInApp: true,
    };

    const value = await auth.sendSignInLinkToEmail(
      emailAddress,
      actionCodeSettings
    );

    analytics.logEvent('send_sign_in_link_to_email');

    localStorage.setItem('emailAddress', emailAddress);

    return value;
  },
  signInWithEmailLink: async (emailAddress: string, emailLink: string) => {
    if (!emailAddress || !emailLink) {
      throw new Error('No e-mail address or e-mail link');
    }

    if (auth.currentUser) {
      throw new Error('No current user');
    }

    const value = await auth.signInWithEmailLink(emailAddress, emailLink);

    analytics.logEvent('login', {
      method: 'email-link',
    });

    localStorage.removeItem('emailAddress');

    return value;
  },
  signInWithAuthProvider: async (
    provider: {
      id: string;
      scopes?: string[];
    } & Record<string, unknown>
  ) => {
    if (auth.currentUser) {
      throw new Error('Cannot sign in when a user is already signed in');
    }

    if (!provider || provider.id == null) {
      throw new Error('No auth provider was specified');
    }
    const authProvider = new firebase.auth.OAuthProvider(provider.id);
    const { scopes } = provider;

    if (scopes) {
      scopes.forEach((scope) => {
        authProvider.addScope(scope);
      });
    }
    const { user } = await auth.signInWithPopup(authProvider);

    if (user == null) {
      throw new Error('No user was found');
    }

    const { uid } = user;

    if (uid == null) {
      throw new Error('Failed to sign user in with no UID');
    }

    const userDocumentReference = firestore.collection('users').doc(uid);

    const userDocument = await userDocumentReference.get({ source: 'server' });

    const signInAnalyticsEvent: { method: string; firstTimeCreated?: boolean } =
      {
        method: provider.id,
      };

    if (!userDocument.exists) {
      await userDocumentReference.set({}, { merge: true });
      signInAnalyticsEvent.firstTimeCreated = true;
    }

    analytics.logEvent('login', signInAnalyticsEvent);
    return user;
  },
  signOut: async () => {
    // Check sso token in local storage
    const ssoToken = localStorage.getItem(LOCAL_STORAGE_KEYS.ssoToken);
    if (ssoToken) {
      localStorage.removeItem(LOCAL_STORAGE_KEYS.ssoToken);
      localStorage.setItem('triggerReload', Date.now().toString());
      window.location.href = TRANSGLOBAL_SIGNIN_URL;
      analytics.logEvent('sign_out');
      return 'sign out';
    }

    const { currentUser } = auth;

    if (!currentUser) {
      throw new Error('No current user');
    }

    const value = await auth.signOut();
    analytics.logEvent('sign_out');
    return value;
  },

  signOutExistingUser: async () => {
    const { currentUser } = auth;

    if (!currentUser) {
      return 'No current user';
    }

    const value = await auth.signOut();
    analytics.logEvent('sign_out');
    return value;
  },
  resetPassword: async (emailAddress: string) => {
    if (!emailAddress) {
      throw new Error('No e-mail address');
    }

    if (auth.currentUser) {
      throw new Error('No current user');
    }

    const value = await auth.sendPasswordResetEmail(emailAddress);
    analytics.logEvent('reset_password');
    return value;
  },
  sendEmailVerification: async () => {
    const { currentUser } = auth;

    if (!currentUser) {
      throw new Error('No current user');
    }

    const response = await currentUser.sendEmailVerification();
    analytics.logEvent('send_email_verification');
    return response;
  },
};

export default authentication;
