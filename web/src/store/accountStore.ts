import { create } from 'zustand';

import { LOCAL_STORAGE_KEYS } from '@/constants/account';
import {
  getLocalData,
  removeLocalData,
  setLocalData,
} from '@/utils/localStorage';

type Account = {
  accountId: string | null;
  accountMode: string | null;
  accountName: string | null;
  accountShortName: string | null;
  compGridsEnabled: boolean | null;
  accountingTransactionsEnabled: boolean | null;
  whiteLabelMode: boolean | null;
  logoUrl: string | null;
  uid: string | null;
};

type AccountStore = {
  logoUrl?: string;
  selectedAccount: Account | null | undefined;
  userState?: UserState;
};

type Setter = {
  setUserState: (s?: UserState) => void;
  setLogoUrl: (logoUrl: string) => void;
  setSelectedAccount: (account: Account | null) => void;
  resetAccountStore: () => void;
};

const defaultValues: AccountStore = {
  logoUrl: '/fintary-logo-white.svg',
  userState: undefined,
  selectedAccount: undefined,
};

const useAccountStore = create<AccountStore & Setter>((set) => ({
  ...defaultValues,
  selectedAccount: getLocalData(LOCAL_STORAGE_KEYS.selectedAccount),
  accountShortName: getLocalData(LOCAL_STORAGE_KEYS.accountShortName),

  setSelectedAccount: (account) => {
    setLocalData(LOCAL_STORAGE_KEYS.selectedAccount, account);
    set({ selectedAccount: account });
  },
  setLogoUrl: (logoUrl) => set({ logoUrl }),
  setUserState: (state) => set({ userState: state }),
  resetAccountStore: () => {
    removeLocalData(LOCAL_STORAGE_KEYS.selectedAccount);
    set(defaultValues);
  },
}));

export default useAccountStore;
