import dayjs from 'dayjs';
import { z } from 'zod';
import { TransactionStatuses } from 'common/globalTypes';

const dateSchema = z
  .string()
  .refine((v) => dayjs(v).isValid())
  .transform((v) => dayjs(v).toDate());

const statusSchema = z.enum([
  TransactionStatuses.DRAFT,
  TransactionStatuses.APPROVED,
  TransactionStatuses.PAID,
  TransactionStatuses.SETTLEMENT,
]);

export const PerAgentGetTransactionsSchema = z.object({
  agent_str_id: z.string(),
  start_date: dateSchema.optional().nullable(),
  end_date: dateSchema.optional().nullable(),
  transaction_str_id: z.string().optional().nullable(),
  page: z.string().optional(),
  limit: z.string().optional(),
});

export type PerAgentGetTransactionsDto = z.infer<
  typeof PerAgentGetTransactionsSchema
>;

export const TransactionDetailSchema = z.object({
  transaction_detail_str_id: z.string().optional(),
  date: dateSchema.optional(),
  amount: z.number(),
  status: statusSchema.optional(),
  tags: z.array(z.string()).optional(),
  notes: z.string().optional(),
});

export type TransactionDetailDto = z.infer<typeof TransactionDetailSchema>;

export const TransactionSchema = z.object({
  transaction_str_id: z.string().optional(),
  date: dateSchema.optional(),

  amount: z.number(),
  status: statusSchema.optional(),
  notes: z.string().optional(),
  details: z.array(TransactionDetailSchema).optional(),
});

export const TransactionDetailDeletedSchema = z.object({
  str_id: z.string(),
});

export const TransactionDeletedSchema = z.object({
  str_id: z.string(),
  details: z.array(TransactionDetailDeletedSchema).optional(),
});

export const TransactionUpdatesSchema = z.object({
  updates: z.array(TransactionSchema),
  deletes: z.array(TransactionDeletedSchema),
});

export type TransactionDto = z.infer<typeof TransactionSchema>;

export const PerAgentPostTransactionSchema = z.object({
  agent_str_id: z.string(),
  transactions: TransactionUpdatesSchema,
});

export type PerAgentPostTransactionDto = z.infer<
  typeof PerAgentPostTransactionSchema
>;
