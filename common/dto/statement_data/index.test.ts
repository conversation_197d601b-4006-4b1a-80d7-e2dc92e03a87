import { SortOrder } from 'common/globalTypes';
import dayjs from 'dayjs';
import { describe, expect, it } from 'vitest';

import { StatementDataQueryDtoSchema } from './index';

describe('StatementDataQueryDtoSchema', () => {
  describe('basic validation', () => {
    it('should validate an empty object and apply defaults', () => {
      const result = StatementDataQueryDtoSchema.parse({});
      expect(result).toEqual({
        disableFilter: false,
        flags: [],
        is_commission_report: false,
        orderBy: 'created_at',
        producer_view: false,
        sort: SortOrder.DESC,
      });
    });

    it('should validate a complete valid object', () => {
      const today = dayjs().format('YYYY-MM-DD');
      const data = {
        agent_name: '<PERSON>',
        disableFilter: true,
        compensation_type: 'Base',
        carrier_name: 'TestCarrier',
        incl_dupes: true,
        limit: 10,
        page: 2,
        payment_date_start: today,
        payment_date_end: today,
        payment_status: 'Paid',
        product_name: 'Insurance',
        sort: SortOrder.ASC,
      };

      const result = StatementDataQueryDtoSchema.parse(data);
      expect(result).toMatchObject({
        agent_name: ['John Doe'],
        disableFilter: true,
        compensation_type: ['Base'],
        carrier_name: ['TestCarrier'],
        incl_dupes: true,
        limit: 10,
        page: 2,
        payment_date_start: today,
        payment_date_end: today,
        payment_status: ['Paid'],
        product_name: ['Insurance'],
        sort: SortOrder.ASC,
      });
    });
  });

  describe('string to array transformations', () => {
    it('should transform string values to arrays for specified fields', () => {
      const data = {
        agent_name: 'John Doe',
        compensation_type: 'Base',
        contacts: 'Contact1',
        carrier_name: 'TestCarrier',
        payment_status: 'Paid',
        product_name: 'Insurance',
        product_type: 'Type1',
        status: 'Active',
        tags: 'tag1',
        writing_carrier_name: 'WritingCarrier',
        premium_type: 'Premium1',
        document_id: 'doc1',
        account_type: 'Type1',
        agent_commissions_status: 'Status1',
        agent_commissions_status2: 'Status2',
        comp_calc_status: 'CalcStatus',
        import_id: 'import1',
        report_data_id: 'report1',
        transaction_type: 'Type2',
      };

      const result = StatementDataQueryDtoSchema.parse(data);

      // Check that all string fields are transformed to arrays
      expect(result.agent_name).toEqual(['John Doe']);
      expect(result.compensation_type).toEqual(['Base']);
      expect(result.contacts).toEqual(['Contact1']);
      expect(result.carrier_name).toEqual(['TestCarrier']);
      expect(result.payment_status).toEqual(['Paid']);
      expect(result.product_name).toEqual(['Insurance']);
      expect(result.product_type).toEqual(['Type1']);
      expect(result.status).toEqual(['Active']);
      expect(result.tags).toEqual(['tag1']);
      expect(result.writing_carrier_name).toEqual(['WritingCarrier']);
      expect(result.premium_type).toEqual(['Premium1']);
      expect(result.document_id).toEqual(['doc1']);
      expect(result.account_type).toEqual(['Type1']);
      expect(result.agent_commissions_status).toEqual(['Status1']);
      expect(result.agent_commissions_status2).toEqual(['Status2']);
      expect(result.comp_calc_status).toEqual(['CalcStatus']);
      expect(result.import_id).toEqual(['import1']);
      expect(result.report_data_id).toEqual(['report1']);
      expect(result.transaction_type).toEqual(['Type2']);
    });

    it('should keep array values as arrays', () => {
      const data = {
        agent_name: ['John Doe', 'Jane Smith'],
        compensation_type: ['Base', 'Bonus'],
        transaction_type: ['Type1', 'Type2'],
      };

      const result = StatementDataQueryDtoSchema.parse(data);

      expect(result.agent_name).toEqual(['John Doe', 'Jane Smith']);
      expect(result.compensation_type).toEqual(['Base', 'Bonus']);
      expect(result.transaction_type).toEqual(['Type1', 'Type2']);
    });
  });

  describe('date validation and transformation', () => {
    it('should accept valid dates', () => {
      const validDate = dayjs().format('YYYY-MM-DD');
      const data = {
        payment_date_start: validDate,
        payment_date_end: validDate,
        processing_date_start: validDate,
        processing_date_end: validDate,
        invoice_date_start: validDate,
        invoice_date_end: validDate,
        effective_date_start: validDate,
        effective_date_end: validDate,
      };

      const result = StatementDataQueryDtoSchema.parse(data);

      expect(result.payment_date_start).toEqual(validDate);
      expect(result.payment_date_end).toEqual(validDate);
      expect(result.processing_date_start).toEqual(validDate);
      expect(result.processing_date_end).toEqual(validDate);
      expect(result.invoice_date_start).toEqual(validDate);
      expect(result.invoice_date_end).toEqual(validDate);
      expect(result.effective_date_start).toEqual(validDate);
      expect(result.effective_date_end).toEqual(validDate);
    });

    it('should transform invalid dates to undefined', () => {
      const invalidDate = 'not-a-date';
      const data = {
        payment_date_start: invalidDate,
        payment_date_end: invalidDate,
      };

      const result = StatementDataQueryDtoSchema.parse(data);

      expect(result.payment_date_start).toBeUndefined();
      expect(result.payment_date_end).toBeUndefined();
    });
  });

  describe('number and string conversions', () => {
    it('should accept numbers for limit and page', () => {
      const data = {
        limit: 10,
        page: 5,
      };

      const result = StatementDataQueryDtoSchema.parse(data);

      expect(result.limit).toBe(10);
      expect(result.page).toBe(5);
    });

    it('should convert string numbers to integers for limit and page', () => {
      const data = {
        limit: '10',
        page: '5',
      };

      const result = StatementDataQueryDtoSchema.parse(data);

      expect(result.limit).toBe(10);
      expect(result.page).toBe(5);
    });
  });

  describe('default values', () => {
    it('should apply default values when fields are not provided', () => {
      const result = StatementDataQueryDtoSchema.parse({});

      expect(result.disableFilter).toBe(false);
      expect(result.orderBy).toBe('created_at');
      expect(result.sort).toBe(SortOrder.DESC);
      expect(result.producer_view).toBe(false);
      expect(result.is_commission_report).toBe(false);
    });

    it('should allow overriding default values', () => {
      const data = {
        disableFilter: true,
        orderBy: 'custom_field',
        sort: SortOrder.ASC,
        producer_view: true,
        is_commission_report: true,
      };

      const result = StatementDataQueryDtoSchema.parse(data);

      expect(result.disableFilter).toBe(true);
      expect(result.orderBy).toBe('custom_field');
      expect(result.sort).toBe(SortOrder.ASC);
      expect(result.producer_view).toBe(true);
      expect(result.is_commission_report).toBe(true);
    });
  });

  describe('boolean fields', () => {
    it('should properly handle boolean fields', () => {
      const data = {
        incl_dupes: true,
        incl_linked: true,
        incl_zero_commissions: true,
        incl_zero_split: true,
        hide_no_payout_calc_commissions: true,
        hide_payout_calc_commissions: true,
        payment_date_empty: true,
        processing_date_empty: true,
        invoice_date_empty: true,
        effective_date_empty: true,
      };

      const result = StatementDataQueryDtoSchema.parse(data);

      expect(result.incl_dupes).toBe(true);
      expect(result.incl_linked).toBe(true);
      expect(result.incl_zero_commissions).toBe(true);
      expect(result.incl_zero_split).toBe(true);
      expect(result.hide_no_payout_calc_commissions).toBe(true);
      expect(result.hide_payout_calc_commissions).toBe(true);
      expect(result.payment_date_empty).toBe(true);
      expect(result.processing_date_empty).toBe(true);
      expect(result.invoice_date_empty).toBe(true);
      expect(result.effective_date_empty).toBe(true);
    });

    it('should coerce string boolean values to actual booleans', () => {
      const data = {
        disableFilter: 'true',
        incl_dupes: 'true',
        incl_linked: 'true',
        incl_zero_commissions: 'true',
        incl_zero_split: 'true',
        hide_no_payout_calc_commissions: 'true',
        hide_payout_calc_commissions: 'false',
        payment_date_empty: 'true',
        processing_date_empty: 'true',
        invoice_date_empty: 'false',
        effective_date_empty: 'false',
        producer_view: 'true',
        is_commission_report: 'true',
      };

      const result = StatementDataQueryDtoSchema.parse(data);

      expect(result.disableFilter).toBe(true);
      expect(result.incl_dupes).toBe(true);
      expect(result.incl_linked).toBe(true);
      expect(result.incl_zero_commissions).toBe(true);
      expect(result.incl_zero_split).toBe(true);
      expect(result.hide_no_payout_calc_commissions).toBe(true);
      expect(result.hide_payout_calc_commissions).toBe(false);
      expect(result.payment_date_empty).toBe(true);
      expect(result.processing_date_empty).toBe(true);
      expect(result.invoice_date_empty).toBe(false);
      expect(result.effective_date_empty).toBe(false);
      expect(result.producer_view).toBe(true);
      expect(result.is_commission_report).toBe(true);
    });
  });

  describe('statement_data_ids field', () => {
    it('should accept a single number for statement_data_ids', () => {
      const data = {
        statement_data_ids: 123,
      };

      const result = StatementDataQueryDtoSchema.parse(data);

      expect(result.statement_data_ids).toBe(123);
    });

    it('should accept an array of numbers for statement_data_ids', () => {
      const data = {
        statement_data_ids: [123, 456, 789],
      };

      const result = StatementDataQueryDtoSchema.parse(data);

      expect(result.statement_data_ids).toEqual([123, 456, 789]);
    });
  });
});
