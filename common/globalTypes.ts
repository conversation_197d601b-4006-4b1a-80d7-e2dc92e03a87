import { JsonValue } from '@prisma/client/runtime/library';

export enum SystemRoles {
  ADMIN = 'admin',
}

export enum CompGridRateFields {
  ALL = 'all',
  CARRIER_RATE = 'carrier_rate',
  HOUSE_RATE = 'house_rate',
  TOTAL_RATE = 'total_rate',
}

export interface BulkEditBody {
  ids: number[];
  policy_id?: string;
  agent_name?: string;
  writing_carrier_name?: string;
  carrier_name?: string;
  customer_name?: string;
  product_type?: string;
  product_name?: string;
  effective_date?: string;
  notes?: string;
  contacts?: any;
  agent_commissions_status?: string;
  compensation_type?: string;
  transaction_type?: string;
  policy_status?: string;
  product_option_name?: string;
  processing_date?: string;
  payment_date?: string;
  period_date?: string;
  internal_id?: string;
  split_percentage?: number;
  premium_type?: string;
  tags?: string[] | string;
}

export enum ContactPayableStatuses {
  PAYABLE = 'payable',
  NON_PAYABLE = 'non_payable',
}

export enum CompReportDateFilter {
  ProcessingDate = 'processing_date',
  PaymentDate = 'payment_date',
}

export enum CompReportPrefixConfig {
  Default = 'default_prefix',
  Custom = 'custom_prefix',
}

export enum ProductTypes {
  LIFE = 'Life',
  ANNUITY = 'Annuity',
}

export enum CompensationTypes {
  // COMMISSION = 'Commission',
  // OVERRIDE = 'Override',
  // EXCESS = 'Excess',
  RENEWAL_COMMISSION = 'Renewal Commission',
  // TRAIL = 'Trail',
  // Use TG definitions until we have a config for this
  ADVANCED = 'Advanced',
  COMMISSION = 'FYC',
  EXCESS = 'Excess',
  RENEWAL = 'Renewal',
  TRAIL = 'Trail',
}

// TODO: Define more detailed types for saved report snapshot data
export interface SnapshotDataData {
  data: any[];
  count: number;
  totals: any;
  contactUser: string;
  contactStrId: string;
  fieldOptions: any;
  contactBankInfo: string;
  contactPayableStatus: string;
}
export interface SavedReportSnapshotData {
  data: SnapshotDataData;
  headers: any[];
  reportPage: string;
}

export enum CompReportViewTypes {
  PRODUCER_VIEW = 'prod-view',
  ADMIN_VIEW = 'admin-view',
}

export enum CompReportGroupTypes {
  NONE = 'none',
  POLICY_GROUP = 'policy-group',
}

export enum FiltersOperators {
  // TODO: Add directional operators (e.g. within one year before | within one year after)
  EQ = 'eq',
  NEQ = 'neq',
  EQNUM = 'eqnum',
  NEQNUM = 'neqnum',
  GT = 'gt',
  LT = 'lt',
  GTE = 'gte',
  LTE = 'lte',
  CONTAINS = 'contains',
  NCONTAINS = 'ncontains',
  CONTAINEDIN = 'containedin',
  NCONTAINEDIN = 'ncontainedin',
  STARTSWITH = 'startswith',
  ENDSWITH = 'endswith',
  BEFORE = 'before',
  BEFORE_EQUALS = 'beforeEquals',
  AFTER = 'after',
  AFTER_EQUALS = 'afterEquals',
  IS_EMPTY = 'isEmpty',
  IS_WRITING_AGENT = 'Is writing agent',
  NOT_WRITING_AGENT = 'Not writing agent',
  WITHIN_ONE_YEAR = 'withinOneYear',
  AT_LEAST_ONE_YEAR = 'atLeastOneYear',
  CUSTOM = 'custom',
  IS_NOT_EMPTY = 'isNotEmpty',
  WITHIN = 'within',
}

export const FiltersOperatorLabelMap: Map<FiltersOperators, string> = new Map([
  [FiltersOperators.EQ, 'Equals'],
  [FiltersOperators.NEQ, 'Not equals'],
  [FiltersOperators.CONTAINS, 'Contains'],
  [FiltersOperators.NCONTAINS, 'Not contains'],
  [FiltersOperators.GT, 'Greater than'],
  [FiltersOperators.LT, 'Less than'],
  [FiltersOperators.GTE, 'Greater than or equal'],
  [FiltersOperators.LTE, 'Less than or equal'],
  [FiltersOperators.CONTAINEDIN, 'In list'],
  [FiltersOperators.NCONTAINEDIN, 'Not in list'],
  [FiltersOperators.STARTSWITH, 'Starts with'],
  [FiltersOperators.ENDSWITH, 'Ends with'],
  [FiltersOperators.BEFORE, 'Before'],
  [FiltersOperators.BEFORE_EQUALS, 'Before equals'],
  [FiltersOperators.AFTER, 'After'],
  [FiltersOperators.AFTER_EQUALS, 'After equals'],
  [FiltersOperators.IS_EMPTY, 'Is empty'],
  [FiltersOperators.IS_NOT_EMPTY, 'Is not empty'],
  [FiltersOperators.WITHIN, 'Within'],
  [FiltersOperators.WITHIN_ONE_YEAR, 'Within one year'],
  [FiltersOperators.AT_LEAST_ONE_YEAR, 'At least one year'],
  [FiltersOperators.CUSTOM, 'Custom'],
]);

export enum DataProcessingAction {
  SET_FIELD_VALUE = 'set_field_value',
  SET_VALUE_FROM_FIELD = 'set_value_from_field',
  SET_BY_CUSTOM_RULE = 'set_by_custom_rule',
}

export interface DataUpdateFilter {
  field?: string;
  op?: FiltersOperators;
  value?: string[] | string | boolean;
  skipEmpty?: boolean;
  usePolicyData?: boolean;
  caseSensitive?: boolean;
  action?: string;
  type?: string;
  field_to_use?: string;
  code_to_use?: string;
  custom_param?: string;
  custom_function?: boolean;
  clear_field?: boolean;
  number?: string;
  from?: string;
  unit?: FieldMatchertUnitOptions;
}

export enum FieldMatchertDateOperatorOptions {
  FromDateField = 'from_date_field',
  FromFixedDate = 'from_fixed_date',
}

export const FieldMatchertDateOperatorOptionsLabels = {
  [FieldMatchertDateOperatorOptions.FromDateField]: 'Date field',
  [FieldMatchertDateOperatorOptions.FromFixedDate]: 'Fixed date',
};

export enum FieldMatchertUnitOptions {
  Empty = '',
  Days = 'days',
  Months = 'months',
  Years = 'years',
}

export const FieldMatchertUnitOptionsLabels = {
  [FieldMatchertUnitOptions.Empty]: 'Select one',
  [FieldMatchertUnitOptions.Days]: 'Days',
  [FieldMatchertUnitOptions.Months]: 'Months',
  [FieldMatchertUnitOptions.Years]: 'Years',
};

export interface DataUpdateRules {
  [id: number]: DataUpdateFilter[];
}

export enum DataEntities {
  COMMISSIONS = 'Commissions',
  POLICIES = 'Policies',
}

export enum ContactStatuses {
  INACTIVE = 'Inactive',
  // TODO: Add more statuses
}

export enum AccessTypes {
  GLOBAL = 'global',
  ACCOUNT = 'account',
  USER = 'user',
  USER_LIST = 'user_list',
}

export enum SortOrder {
  ASC = 'asc',
  DESC = 'desc',
}

export interface SortParams {
  order: SortOrder;
  orderBy: string;
}

export interface CustomSortParams<T> {
  array: T[];
  orderBy: keyof T | undefined;
  sort: SortOrder;
}

export enum ProcessorReviewStatuses {
  None = '',
  Draft = 'draft',
  Approved = 'approved',
  InReview = 'in_review',
  NeedsUpdate = 'needs_update',
}

export const ProcessorReviewStatusesLabels = {
  [ProcessorReviewStatuses.None]: '',
  [ProcessorReviewStatuses.Draft]: 'Draft',
  [ProcessorReviewStatuses.Approved]: 'Approved',
  [ProcessorReviewStatuses.InReview]: 'In review',
  [ProcessorReviewStatuses.NeedsUpdate]: 'Needs update',
};

export enum ProcessorStatuses {
  NEW = 'new',
  PROCESSED = 'processed',
}

export enum DocumentStatuses {
  PENDING_UPLOAD = 'pending_upload',
  NEW = 'new',
  PROCESSING = 'processing',
  PENDING_REVIEW = 'pending_review',
  PROCESSED = 'processed',
}

export const DocumentStatusesLabels = {
  [DocumentStatuses.PENDING_UPLOAD]: 'Pending upload',
  [DocumentStatuses.NEW]: 'New',
  [DocumentStatuses.PROCESSING]: 'Processing',
  [DocumentStatuses.PENDING_REVIEW]: 'Pending review',
  [DocumentStatuses.PROCESSED]: 'Processed',
};

export enum ProcessorSelectorStatuses {
  SUCCESS = 'success',
  PARTIAL = 'partial',
  ERROR = 'error',
  MULTI = 'multi',
  SPREADSHEET = 'spreadsheet',
}

export enum TransactionStatuses {
  DRAFT = 'Draft',
  APPROVED = 'Approved',
  PAID = 'Paid',
  SETTLEMENT = 'Settlement',
}

export const TransactionStatusesLabels = {
  [TransactionStatuses.DRAFT]: 'Draft',
  [TransactionStatuses.APPROVED]: 'Approved',
  [TransactionStatuses.PAID]: 'Paid',
  [TransactionStatuses.SETTLEMENT]: 'Settlement',
};

export const TransactionStatusesOptions = Object.values(
  TransactionStatuses
).map((status) => ({
  value: status,
  label: TransactionStatusesLabels[status],
}));

export enum SavedReportStatuses {
  DRAFT = 'Draft',
  APPROVED = 'Approved',
  PAID = 'Paid',
}

export const SavedReportStatusesLabels = {
  [SavedReportStatuses.DRAFT]: 'Draft',
  [SavedReportStatuses.APPROVED]: 'Approved',
  [SavedReportStatuses.PAID]: 'Paid',
};

export const SavedReportStatusOptions = Object.values(SavedReportStatuses).map(
  (status) => ({
    value: status,
    label: SavedReportStatusesLabels[status],
  })
);

export enum ReportGroupsStatuses {
  DRAFT = 'Draft',
  APPROVED = 'Approved',
  PAID = 'Paid',
  IN_PROGRESS = 'In_progress',
}

export const ReportGroupsStatusesLabels = {
  [ReportGroupsStatuses.DRAFT]: 'Draft',
  [ReportGroupsStatuses.APPROVED]: 'Approved',
  [ReportGroupsStatuses.PAID]: 'Paid',
  [ReportGroupsStatuses.IN_PROGRESS]: 'In progress',
};

export const ReportGroupsStatusOptions = Object.values(
  ReportGroupsStatuses
).map((status) => ({
  value: status,
  label: ReportGroupsStatusesLabels[status],
}));

export enum AgentCommissionsStatuses {
  NONE = '',
  DRAFT = 'Draft',
  REVIEWED = 'Reviewed',
  MANUAL = 'Manual',
  OFFSET = 'Offset',
  NO_PAYMENT = 'No_payment',
  APPROVED = 'Approved',
  PAID = 'Paid',
  REJECTED = 'Rejected',
}

export const AgentCommissionsStatusesLabels = {
  [AgentCommissionsStatuses.NONE]: '(Blank)',
  [AgentCommissionsStatuses.DRAFT]: 'Draft',
  [AgentCommissionsStatuses.REVIEWED]: 'Reviewed',
  [AgentCommissionsStatuses.MANUAL]: 'Manual',
  [AgentCommissionsStatuses.OFFSET]: 'Offset',
  [AgentCommissionsStatuses.NO_PAYMENT]: 'No payment',
  [AgentCommissionsStatuses.APPROVED]: 'Approved',
  [AgentCommissionsStatuses.PAID]: 'Paid',
  [AgentCommissionsStatuses.REJECTED]: 'Rejected',
};

export const AgentCommissionsStatusOptions = Object.values(
  AgentCommissionsStatuses
).map((status) => ({
  value: status,
  label: AgentCommissionsStatusesLabels[status],
}));

export interface SnapshotData {
  data: {
    data: Array<{ id: string; [key: string]: any }>;
    [key: string]: any;
  };
  [key: string]: any;
}

export interface ConflictingReportData {
  reportId: number;
  reportStrId: string;
  reportName: string | JsonValue;
}

export enum ExtractTableStatuses {
  SUCCESS = 'success',
  PROCESSING = 'processing',
  FAILED = 'failed',
}

export enum ImportStatuses {
  NONE = 'none',
  REQUEST_REVIEW = 'request_review',
  AUTO_IMPORT = 'auto_import',
}

export const ImportStatusesLabels = {
  [ImportStatuses.NONE]: 'None',
  [ImportStatuses.REQUEST_REVIEW]: 'Request review',
  [ImportStatuses.AUTO_IMPORT]: 'Auto import',
};

export enum ImportMethod {
  MANUAL = 'manual',
  AUTO = 'auto',
}

export enum UploadSource {
  WEB = 'web',
  API = 'api',
  EMAIL = 'email',
}

export const UploadSourceLabels = {
  [UploadSource.WEB]: 'Web',
  [UploadSource.API]: 'API',
  [UploadSource.EMAIL]: 'Email',
};

export enum ProcessMethod {
  MANUAL = 'manual',
  AUTO = 'automatic',
}

export enum DocumentImportMethod {
  PROCESSOR = 'processor',
  MAPPING = 'mapping',
}

export const ProcessMethodLabels = {
  [ProcessMethod.MANUAL]: 'Manual',
  [ProcessMethod.AUTO]: 'Automatic',
};

export enum DocumentPreviewTypes {
  PDF = 'pdf',
  IMG = 'img',
  EXCEL = 'excel',
  TEXT = 'text',
  HTML = 'html',
}

export enum CompProfileMatcherState {
  MATCHED = 'Matched',
  UNMATCHED = 'Unmatched',
}

export enum TransactionType {
  RECEIVABLE = 'receivable',
  PAYABLE = 'payable',
}

export enum TransactionParty {
  AGENT = 'agent',
  AGENCY = 'agency',
  POLICY = 'policy',
}
export enum TransactionRate {
  AGENT = 'receivable_value_agent_rate',
  AGENCY = 'receivable_value_override_rate',
  POLICY = 'receivable_value_agency_rate',
}

export enum PriorityList {
  URGENT = 'urgent',
  HIGH = 'high',
  MEDIUM = 'medium',
  LOW = 'low',
}

export const PriorityListLabels = {
  [PriorityList.URGENT]: 'Urgent',
  [PriorityList.HIGH]: 'High',
  [PriorityList.MEDIUM]: 'Medium',
  [PriorityList.LOW]: 'Low',
};

export enum StatusList {
  DRAFT = 'draft',
  IN_REVIEW = 'in_review',
  NEED_UPDATE = 'need_update',
  APPROVED = 'approved',
}

export const StatusListLabels = {
  [StatusList.DRAFT]: 'Draft',
  [StatusList.IN_REVIEW]: 'In review',
  [StatusList.NEED_UPDATE]: 'Need update',
  [StatusList.APPROVED]: 'Approved',
};

export interface CompReportData {
  str_id: string;
  name: string;
  notes: string;
  created_at: Date;
  status: string;
  users_white_list: CompReportWhiteListUser[];
  access: string;
  users_reports_reviewed_by: {
    str_id: string;
    first_name: string;
    last_name: string;
    email: string;
  };
  contact: {
    str_id: string;
    first_name: string;
    last_name: string;
    email: string;
    agent_payable_status?: string;
    bank_info?: string;
  };
  transaction_str_id?: string;
  transaction_amount?: number;
  statement_data: StatementDataItem[];
  statement_data_count: number;
  statement_data_totals: {
    commissionable_premium_amount?: number;
    commission_amount?: number;
    commission_paid_amount?: number;
    fees?: number;
    customer_paid_premium_amount?: number;
    premium_amount?: number;
    comp_calc?: Record<string, number>;
  };
}

interface CompReportWhiteListUser {
  str_id: string;
  email: string;
}

export interface StatementDataItem {
  transaction_detail_str_id: string;
  transaction_detail_tags: string[];
  comp_calc?: Record<string, number>;
  comp_calc_status?: Record<string, string>;
  comp_calc_log?: Record<string, any>;
  [key: string]: unknown; // For dynamically added fields from statementData
}

export enum ExtractionGroup {
  EXISTING = 'existing',
  TOOL = 'tool',
  AI = 'ai',
}

export const METHOD_LABELS: Record<string, string> = {
  extractTable: 'Extract Table',
  documentAI: 'Google Document AI',
  adobeExtract: 'Adobe PDF Extract',
  nanonets: 'Nanonets',
  htmlExtract: 'HTML Extract',
};

export const DEFAULT_METHODS = {
  pdf: 'extractTable',
  html: 'htmlExtract',
};

export const PDF_OPTIONS = [
  { value: 'extractTable', label: 'Extract Table' },
  { value: 'nanonets', label: 'Nanonets' },
  { value: 'adobeExtract', label: 'Adobe PDF Extract' },
  { value: 'documentAI', label: 'Google Document AI' },
];

export const HTML_OPTIONS = [{ value: 'htmlExtract', label: 'HTML Extract' }];

// TODO: We need to search in our code base all places where we use agent commissions and use this type
export type AgentCommissions = Record<string, number> & { total?: number };
