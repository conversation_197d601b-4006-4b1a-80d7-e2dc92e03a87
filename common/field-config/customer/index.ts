import { CustomerGender } from 'common/customer/customer.constants';
import { CustomerType } from 'common/customer/customer.constants';
import { formatDate } from 'common/helpers/DataTransformation/formatter';
import { FieldTypes } from 'common/constants';
import { capitalize } from 'lodash-es';

import { CustomerTextFormatter } from './text-formatter';

const genderOptions: { id: string; label: string }[] = [
  { id: CustomerGender.male, label: 'Male' },
  { id: CustomerGender.female, label: 'Female' },
];

const typeOptions: { id: string; label: string }[] = [
  { id: CustomerType.individual, label: 'Individual' },
  { id: CustomerType.group, label: 'Group' },
];

export const getCustomerFieldConfig = () => {
  const textFormatter = new CustomerTextFormatter();

  const fields = {
    str_id: {
      label: 'Customer ID',
      enabled: true,
      isRelationalField: true,
      exportOnly: true,
    },
    type: {
      label: 'Type',
      enabled: true,
      type: FieldTypes.SELECT,
      options: typeOptions,
      textFormatter: capitalize,
    },
    first_name: {
      label: 'First name',
      enabled: true,
    },
    middle_name: {
      label: 'Middle name',
      enabled: false,
    },
    last_name: {
      label: 'Last name',
      enabled: true,
    },
    nickname: {
      label: 'Nickname',
      enabled: true,
    },
    dob: {
      label: 'Birthday',
      enabled: true,
      type: FieldTypes.DATE,
      textFormatter: formatDate,
    },
    gender: {
      label: 'Gender',
      enabled: false,
      type: FieldTypes.SELECT,
      options: genderOptions,
      textFormatter: capitalize,
    },
    company_name: {
      label: 'Company name',
      enabled: true,
    },
    website: {
      label: 'Website',
      enabled: true,
    },
    email: {
      label: 'Email',
      enabled: true,
    },
    phone: {
      label: 'Phone',
      enabled: true,
    },
    address: {
      label: 'Address',
      enabled: true,
      type: FieldTypes.CUSTOM,
      textFormatter: textFormatter.addressTextFormatter,
    },
    group_id: {
      label: 'Group ID',
      enabled: true,
    },
    start_date: {
      label: 'Start date',
      enabled: true,
      type: FieldTypes.DATE,
      textFormatter: formatDate,
    },
    end_date: {
      label: 'End date',
      enabled: true,
      type: FieldTypes.DATE,
      textFormatter: formatDate,
    },
    report_data: {
      label: 'Report data',
      enabled: true,
      type: FieldTypes.CUSTOM,
      readOnly: true,
      textFormatter: textFormatter.reportDataTextFormatter,
    },
  };

  return {
    label: 'Customers',
    table: 'customers',
    editable: true,
    fields: fields,
    bulkAddFields: [
      {
        id: 'geo_state',
      },
    ],
    queryChips: {},
    queryChipsType: 'select',
  };
};
