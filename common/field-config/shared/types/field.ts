export type FieldConfig = {
  label: string;
  enabled?: boolean;
  readOnly?: boolean;
  hidden?: boolean;
  bulkEdit?: boolean;
  isRelationalField?: boolean;
  exportOnly?: boolean;
  textFormatter?: (
    v?: string | object | boolean | number,
    row?: unknown,
    args?: TextFormatterArgs // Passed dynamic select values
  ) => string | number;
  ref?: string;
};

export type TextFormatterArgs = {
  allDynamicSelectMap?: Record<string, Map<string, unknown>>;
};

export interface DynamicSelectConfig {
  table: string;
  queryParamName: string;
  queryParamValue: (string | number)[];
  collectDataFields: string[];
  dataExtractors?: {
    [field: string]: (
      rows: any,
      totals?: { [key: string]: any }
    ) => (string | number)[];
  };
  mapKey?: string;
}
