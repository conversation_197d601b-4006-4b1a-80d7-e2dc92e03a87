# common

## 0.24.7 (2025-07-14 21:58:47)

<details>
<summary>Changes</summary>

### Patch Changes

- 48371a9: Updated the statement amount extractor to exclude date-like values such as 06.16 or 6.16, also enable extracting the negative amounts.
- cd9fde0: Fix E2E test failures caused by new EnhancedSelector.
</details>

<details>
<summary>Previous Versions</summary>

## 0.24.6 (2025-07-14 15:34:20)

<details>
<summary>Changes</summary>

### Patch Changes

- d6ee6a7: Improved transaction management with pagination support and refactored backend logic
- 5547d60: Added support for AI extraction(Gemini, ChatGPT, Claude, etc.) and enabled the ability to choose between multiple extraction methods in document processing.
- fefa9a4: - Introduced a new field 'processing_date' in the reconciliation configuration.
  - Enhanced filtering logic to handle fields specific to reconciliation version 2.
  - Added constants for reconciliation versions and fields to improve maintainability.
</details>

## 0.24.5 (2025-07-14 09:00:53)

<details>
<summary>Changes</summary>

### Patch Changes

- f53d05c: Skip requirement for comp grid criteria if calc method doesn't require comp grid rates.
- 1c9d5ce: Customers page:
  - Move config from web to common, and add textFormatter for use in export.
  - Updated the `ExportCustomersHandler` to export based on the new config.
  - Added unit tests for textFormatter.
</details>

## 0.24.4 (2025-07-09 00:17:30)

<details>
<summary>Changes</summary>

### Patch Changes

- db2a894: Fix failing test in DocumentTextFormatter
- 624c907: Export alignment on Policy page
  - Move config from web to common, and add textFormatter for use in export
  - Move DataTransformation from web to common to be used by textFormatter
  - Refactor the report_data export logic with the following steps: getData → get fields based on config and accountSetting → fetch dynamicSelect data → formatData → export CSV
- df70eef: - Introduced a new model `uploaded_saved_reports` to manage file metadata.
  - Updated existing `saved_reports` model to include a foreign key reference to `uploaded_saved_reports`.
  - Added new endpoints for uploading, downloading, listing and filtering saved report files.
  - Created DTOs for file upload and retrieval operations.
- d519450: Refactor reconciliation_data v2 api
  - Remove paging when querying the database
  - Implement sorting and pagination in JS
  - Add tests for sorting and pagination
  - Implemented batch retrieval instead of retrieving all records in a single query
- 07887a5: Fix grouping failure caused by null premium_amount & split_percent
- c09430e: - Introduced `policyDataIfEmptyFields` constant to manage fields sourced from policy data.
  - Updated `queryFieldValues` to utilize `findMany` for fetching statement data, enhancing flexibility in selecting fields.
  - Modified `StatementFilterService` to incorporate policy data fields in filter logic.
  - Refactored table formatting in `Statements.tsx` to improve tooltip handling for policy data discrepancies.
</details>

## 0.24.3 (2025-07-07 09:57:19)

<details>
<summary>Changes</summary>

### Patch Changes

- 9e548c2: - Updated the `is_saved_report` parameter in `shouldGetSelectedFields` to accept a string instead of a boolean.
  - Adjusted related function calls across various API endpoints to pass the `is_saved_report` as a string.
  - Removed unnecessary comments and TODOs from the codebase for clarity.
  - Introduced `ChildRelationshipWithContact` type for better type safety.
- 0a84843: - Move config from web to common, and add textFormatter for use in export.
  - Implemented `getDocumentFieldConfig` to retrieve field configurations based on account mode and timezone.
  - Added `formatExportData` and `mapHeaderFromFieldConfigs` to process export data and headers.
  - Updated the `ExportDocumentsHandler` to include timezone as a parameter.
  - Introduced new constants for field types and labels to improve maintainability.
</details>

## 0.24.2 (2025-07-04 17:56:46)

<details>
<summary>Changes</summary>

### Patch Changes

- 6a7dee2: In admin accounts, sort users by active first. Add user state formatter.
</details>

## 0.24.1 (2025-07-03 06:25:20)

<details>
<summary>Changes</summary>

### Patch Changes

- daa48af: Allow user to select manual grouping calculation method
</details>

## 0.24.0 (2025-07-02 08:45:48)

<details>
<summary>Changes</summary>

### Minor Changes

- 034e734: Update Companies & Global Companies Page: 1. Link Suggestions: Added a feature to suggest potential account companies that can be linked to each global company. It shows how many unlinked companies are available and allows linking them directly. 2. Merge Function: Introduced a merge feature that lets users merge selected fields from one global company into another, helping maintain cleaner and more consistent data.
### Patch Changes

- dc66daf: Made export agent columns CSV and added Formatter.label
- 2c59195: Replace comp reports data source from snapshotdata to accounting transactions in new endpoint and new FE component in '.../comp-reports/report_str_id' route.
- 2c59195: Replace comp reports data source from snapshotdata to accounting transactions part IV
</details>

## 0.23.8 (2025-06-27 16:51:32)

<details>
<summary>Changes</summary>

### Patch Changes

- 7c1e7a0: Replace comp reports data source from snapshotdata to accounting transactions part IV
</details>

## 0.23.7 (2025-06-26 19:28:31)

<details>
<summary>Changes</summary>

### Patch Changes

- cf38844: Align formatter for consistent decimal place handling
- 1cf7578: Fixed agentCommissionPayout calculation on backend.
</details>

## 0.23.6 (2025-06-26 07:54:07)

<details>
<summary>Changes</summary>

### Patch Changes

- 6c97bf9: Enabled report processors for all roles.
</details>

## 0.23.5 (2025-06-26 04:21:21)

<details>
<summary>Changes</summary>

### Patch Changes

- 710bafc: Normalized formatting for commissions amount and rates.
- a76f08e: Suggested change
Refactored payout rate formatter logic and zero-value filtering for commissions page.
</details>

## 0.23.4 (2025-06-25 03:00:44)

<details>
<summary>Changes</summary>

### Patch Changes

- 89aaadc: Fix agent receivables not showing up on commissions page
- 20f9bf0: Replace comp reports data source from snapshotdata to accounting transactions part III
</details>

## 0.23.3 (2025-06-24 08:09:30)

<details>
<summary>Changes</summary>

### Patch Changes

- d8f52f3: Document edit modal now shows any valid status.
- 134f822: Added support for select statements based on processing date range when grouping
</details>

## 0.23.2 (2025-06-23 02:31:10)

<details>
<summary>Changes</summary>

### Patch Changes

- 9d270e4: Fixed lint warns
</details>

## 0.23.1 (2025-06-21 05:55:46)

<details>
<summary>Changes</summary>

### Patch Changes

- de2f9bb: Replace comp reports data source from snapshotdata to accounting transactions part II
- 302466b: Implement unselected agents for the dashboard filter
</details>

## 0.23.0 (2025-06-19 20:18:24)

<details>
<summary>Changes</summary>

### Minor Changes

- 370baa2: Implemented BA MOO-specific grouping calculation method
</details>

## 0.22.1 (2025-06-19 02:50:07)

<details>
<summary>Changes</summary>

### Patch Changes

- a0a3e8a: Implemented a new services for handling AI provider interactions.
Added abstraction layer to support multiple providers (OpenAI, Grok, Claude).
Introduced provider-specific configurations and API clients.
Updated prompt processing logic to be provider-agnostic.
Added basic usage examples and documentation.
</details>

## 0.22.0 (2025-06-19 01:20:20)

<details>
<summary>Changes</summary>

### Minor Changes

- 2c36d77: RiskTag now can sync member count back to the BenefitPoint
</details>

## 0.21.0 (2025-06-18 06:00:21)

<details>
<summary>Changes</summary>

### Minor Changes

- 3f30a1b: The new grouping process generates virtual records based on the grouped items and infers the correct premium amount and commission rate according to the calculation method specified in the grouping rules.
### Patch Changes

- 2aa1fb5: Updated to max 3 digits the rate format for "Agent payout rate"
- 3f30a1b: This change allows clients to set up grouping rules by specifying on how to filter the data and how the grouping keys are transformed for specific rule
</details>

## 0.20.6 (2025-06-13 17:57:14)

<details>
<summary>Changes</summary>

### Patch Changes

- 592a243: Added new field to the "Commissions" page, "Advanced Amount" to track the "Advanced" column in carriers' statements.
</details>

## 0.20.5 (2025-06-12 23:32:27)

<details>
<summary>Changes</summary>

### Patch Changes

- 1d887fd: Report processors - replaced codemirror with fully featured processor playground
</details>

## 0.20.4 (2025-06-11 09:39:48)

<details>
<summary>Changes</summary>

### Patch Changes

- 8ee096a: Fix tests failure caused by timezone
</details>

## 0.20.3 (2025-06-10 19:59:47)

<details>
<summary>Changes</summary>

### Patch Changes

- 86b5b44: Add Cursor rules
</details>

## 0.20.2 (2025-06-10 05:21:34)

<details>
<summary>Changes</summary>

### Patch Changes

- c80cd7e: Fixed agent compensation values when exports CSV from comissions page.
</details>

## 0.20.1 (2025-06-06 20:42:25)

<details>
<summary>Changes</summary>

### Patch Changes

- e491e88: Add tags field to accounting transaction details in the agents page
</details>

## 0.20.0 (2025-06-06 16:02:22)

<details>
<summary>Changes</summary>

### Minor Changes

- 0c7e4c0: Added mapping support for e2e document processing. Now users can select a mapping under the document profile within the company page. If the uploaded file is a spreadsheet and no appropriate processor is available, the system will run the selected mapping and automatically import the data provided all conditions are met.
</details>

## 0.19.2 (2025-06-05 05:10:30)

<details>
<summary>Changes</summary>

### Patch Changes

- 798d6c3: Remove 'profile_str_id' from zod processor validation.
</details>

## 0.19.1 (2025-06-04 23:15:56)

<details>
<summary>Changes</summary>

### Patch Changes

- b7f7b25: Add new Timezone decorator to extract timezone from request headers, defaulting to 'UTC'
  Apply timezone when formatting dates in the Accounting Transactions table
  Updated constants to include HTTP header for timezone
- ced8c14: Check valid date range input at edit form, start date must be before end date
</details>

## 0.19.0 (2025-06-04 04:02:21)

<details>
<summary>Changes</summary>

### Minor Changes

- c1fdfb5: Implement the new database model for DocumentProfile and its relationships. Updated the related codebase across multiple modules and built a new document profile management view.
  Key changes include:
  1. New DocumentProfile view
  - Allows creating, editing, viewing, and deleting individual document profiles.
  - Supports linking profiles to documents, global companies, mappings, prompts, and various configuration settings.
  - Shows the total document count under each profile and allows accessing special files via links.
  2. Document view updates
  - Enables document selection of an associated DocumentProfile.
  3. Company / Global company view updates
  - Allows associating companies with specific document profiles.
  4. Processor, Processor selector, E2e document processing, Data import, and Mapping page updates
  - Updated relationship logic to support the new DocumentProfile structure.
### Patch Changes

- 721040a: Add support for comma-separated values in CONTAINS and NCONTAINS operators in field matcher when 'Treat as array' option is true.
- 9693592: Added reverse comp report approval script to code base
</details>

## 0.18.4 (2025-06-03 06:52:24)

<details>
<summary>Changes</summary>

### Patch Changes

- d92cf5c: When saving snapshot_data into saved_report, retrieve all fields from the data source to support view changes in the "Views and Fields" settings.
  Display the fields in the report based on the configuration defined in the settings.
- 2e487a8: Migrated deprecated Formatter to Typescript
- 064f6c6: Fix pdf rendering issue caused by worker service is not compiled during production build
</details>

## 0.18.3 (2025-05-29 18:53:31)

<details>
<summary>Changes</summary>

### Patch Changes

- a8f9d9e: Define widget structure
Enhance no data handling in Chart and Table widgets
</details>

## 0.18.2 (2025-05-29 05:39:24)

<details>
<summary>Changes</summary>

### Patch Changes

- 0044f7f: Improve the performance of generating release notes without installing deps
</details>

## 0.18.1 (2025-05-27 08:44:04)

<details>
<summary>Changes</summary>

### Patch Changes

- 51dc80f: Display Multiplier information in Agent commission log when exporting
</details>

## 0.18.0 (2025-05-26 08:04:52)

<details>
<summary>Changes</summary>

### Minor Changes

- 26d347a: Improve ui/ux in Comp grid viewer (collapse columns, date ranges, improve btns)
### Patch Changes

- c526b66: Fixed an issue of group name filter that doesn't work properly at commissions and policies page
- f511da4: Add the custom_view_name field to Agent settings
  Include the id to support updating Agent settings
  Get the correct current account_role_setting when updating
- 67dd206: Changed multiline-text input to codemirror for report processors. Added a few rendering UI fixes as well for the report processor views.
</details>

## 0.17.3 (2025-05-22 07:15:46)

<details>
<summary>Changes</summary>

### Patch Changes

- 9cd0c0b: Add support for company, document type, and statement classification in email and API document uploads. If conditions are met, include them in the auto document processing flow. Also, add a verification system for this field.
- 4d53798: Migrate all dto to Zod schema
</details>

## 0.17.2 (2025-05-19 05:59:23)

<details>
<summary>Changes</summary>

### Patch Changes

- eea9234: Aligned all `ESLint` versions across the workspace and moved the base configuration to the root project.
</details>

## 0.17.1 (2025-05-19 03:33:36)

<details>
<summary>Changes</summary>

### Patch Changes

- 442592b: Only show "Show duplicates" option for Fintary admins
</details>

## 0.17.0 (2025-05-16 02:02:45)

<details>
<summary>Changes</summary>

### Minor Changes

- f4256c2: Added report processor capability for custom policies and commissions exports.
### Patch Changes

- b112990: Fix CodeMirror version conflicts between v6 core and @uiw v4 wrappers
</details>

## 0.16.0 (2025-05-14 03:51:00)

<details>
<summary>Changes</summary>

### Minor Changes

- 4a1ec00: Fix the CodeMirror dependency issue.
</details>

## 0.15.2 (2025-05-14 01:52:06)

<details>
<summary>Changes</summary>

### Patch Changes

- b7d5bca: Update e2e test locators.
</details>

## 0.15.1 (2025-05-13 18:51:25)

<details>
<summary>Changes</summary>

### Patch Changes

- 56dd937: Update account_role_settings to require custom_view_name and adjust related functionality
Able to create new account_role_settings in the Views and Fields screen
</details>

## 0.15.0 (2025-05-12 00:30:02)

<details>
<summary>Changes</summary>

### Minor Changes

- 8576995: Removed account specific fields from all DTOs.
</details>

## 0.14.9 (2025-05-08 07:54:09)

<details>
<summary>Changes</summary>

### Patch Changes

- cc856f2: Update E2E test cases to make them simpler, strategy-driven, and easier to onboard.
- 5ed9a3a: Add an "upload_source" field and label to differentiate upload sources: Web, API, and Email.
</details>

## 0.14.8 (2025-05-06 19:04:25)

<details>
<summary>Changes</summary>

### Patch Changes

- 9be6b1d: Exclude records in non-editable states by default when running grouping/deduping and reconciliation with grouping
- 37c5975: Implemented new date operators (Within, before, after, n days/months/years) for the Data Actions tool.
</details>

## 0.14.7 (2025-05-05 07:59:52)

<details>
<summary>Changes</summary>

### Patch Changes

- c00c194: Tweak the document processing workflow and resolve the issue where ExtractTable cannot be run during the workflow. Also fix the "split_percentage" validation issue. Implement an "process_method" field to distinguish between automatic and manual processing.
- a18a4fc: Set the type of accounting_transactions to "comp_report" when generating comp payout reports
</details>

## 0.14.6 (2025-05-01 19:12:55)

<details>
<summary>Changes</summary>

### Patch Changes

- 724886d: Fixed reconciler bug where account_id wasn't found due to DTO
</details>

## 0.14.5 (2025-04-30 16:49:38)

<details>
<summary>Changes</summary>

### Patch Changes

- e92548d: Update "xlsx" package version
</details>

## 0.14.4 (2025-04-30 04:36:41)

<details>
<summary>Changes</summary>

### Patch Changes

- 7f20a58: Remove validation on account_id, it can be read from req directly
</details>

## 0.14.3 (2025-04-30 00:02:34)

<details>
<summary>Changes</summary>

### Patch Changes

- 251eaaa: Update packages that with vulnerabilities
</details>

## 0.14.2 (2025-04-29 05:33:57)

<details>
<summary>Changes</summary>

### Patch Changes

- 2f23b8e: Allow users to specify payment method when uploading documents and will set to corresponding payment_method when syncing to BenefitPoint
</details>

## 0.14.1 (2025-04-26 02:13:03)

<details>
<summary>Changes</summary>

### Patch Changes

- 99762a7: Fix date validation issue of invalid date
</details>

## 0.14.0 (2025-04-25 16:24:04)

<details>
<summary>Changes</summary>

### Minor Changes

- f5ccea9: Add regression test feature in admin tools to validate new commission calc updates on provided use cases
</details>

## 0.13.1 (2025-04-24 17:48:54)

<details>
<summary>Changes</summary>

### Patch Changes

- 3e85e99: Remove the unused "web/common/tools" module and update related dependencies. Add new date types for "findAllDate" and "dateProcessor". Add a new utility function "splitAtPositions", and update the default processor code.
</details>

## 0.13.0 (2025-04-15 06:51:42)

<details>
<summary>Changes</summary>

### Minor Changes

- 120df69: Added agent payout rate calculation when editing related commission fields values
</details>

## 0.12.0 (2025-04-14 08:34:07)

<details>
<summary>Changes</summary>

### Minor Changes

- 2b965fe: Fix statementAmountExtraction for document process, retrieve the amount by adding the unit test cases
</details>

## 0.11.0 (2025-04-12 00:25:16)

<details>
<summary>Changes</summary>

### Minor Changes

- fd2bc0d: Extract statement amount from filenames when uploading files.
### Patch Changes

- b8e51db: Added clear field operation for data actions tool
</details>

## 0.10.0 (2025-04-10 17:36:25)

<details>
<summary>Changes</summary>

### Minor Changes

- d01793d: Allow users to ungroup grouped commission line items
### Patch Changes

- f66031b: Normalize agent_payout_rate and agent_commission_payout_rate data
- 18faa4f: Change activity log filters to a dropdown instead of chips, and add Gmail sync option
</details>

## 0.9.3 (2025-04-09 04:43:48)

<details>
<summary>Changes</summary>

### Patch Changes

- 1f93f5d: Replace the implicit '\_companies_processors' table with an intermediate table, and add an 'import_status' field to it. And update both the UI for companies and the admin companies UI to allow users to select the 'import_status'.
</details>

## 0.9.2 (2025-04-08 16:39:43)

<details>
<summary>Changes</summary>

### Patch Changes

- 6b07603: Two decimals formatter fix for BuddyIns account for agent payout rate field.
</details>

## 0.9.1 (2025-04-05 01:26:02)

<details>
<summary>Changes</summary>

### Patch Changes

- 12293e7: Use localized Loader for widget preview instead of global (which blocks whole page)
</details>

## 0.9.0 (2025-04-03 19:16:59)

<details>
<summary>Changes</summary>

### Minor Changes

- 907e7e0: Add support to data actions tool for Array fields and operators (e.g. tags field)
</details>

## 0.8.3 (2025-04-03 07:12:14)

<details>
<summary>Changes</summary>

### Patch Changes

- 2786f93: Add a "Bank total" field and include it in the export. Also replace the file path, optimise the upload date type in the export with the filename.
</details>

## 0.8.2 (2025-04-02 05:34:40)

<details>
<summary>Changes</summary>

### Patch Changes

- 59bc916: Allow user to manually group commission line items
</details>

## 0.8.1 (2025-03-30 23:45:31)

<details>
<summary>Changes</summary>

### Patch Changes

- 920a29a: Get rid of reconciliation data source and replaced the widget
</details>

## 0.8.0 (2025-03-28 21:42:13)

<details>
<summary>Changes</summary>

### Minor Changes

- cdf74b2: Create new commission fields for agent comp calc linked with accounting transactions
</details>

## 0.7.2 (2025-03-27 15:12:44)

<details>
<summary>Changes</summary>

### Patch Changes

- 5b45471: Similarity match group by
</details>

## 0.7.1 (2025-03-26 15:51:41)

<details>
<summary>Changes</summary>

### Patch Changes

- 42a936c: Added 'Offset' and 'No payment' payout statuses
</details>

## 0.7.0 (2025-03-24 21:39:37)

<details>
<summary>Changes</summary>

### Minor Changes

- e53c65a: Modify the mapping between "companies" and "processors" and allow selecting "processors" on the "companies" page.
</details>

## 0.6.2 (2025-03-21 14:11:35)

<details>
<summary>Changes</summary>

### Patch Changes

- 33e0f17: Using bignumber rounding for percentage formatter.
</details>

## 0.6.1 (2025-03-20 20:06:01)

<details>
<summary>Changes</summary>

### Patch Changes

- 2ba163e: Fix payment date range query error in commission data view caused by passing Invalid Date string to server
</details>

## 0.6.0 (2025-03-20 20:06:01)

<details>
<summary>Changes</summary>

### Minor Changes

- 8d2ee05: Data update: Allow data actions to set fields based on other fields
</details>

## 0.5.2 (2025-03-18 15:34:10)

<details>
<summary>Changes</summary>

### Patch Changes

- f951276: Update data update tool to handle rounding when working with numeric operators
- 0096d75: Add support for matcher method: IS_WRITING_AGENT
</details>

## 0.5.1 (2025-02-25 07:30:51)

<details>
<summary>Changes</summary>

### Patch Changes

- 2b6c5eb: Feature: allow client uploading file with external_company_id instead of company name
</details>

## 0.5.0 (2025-02-18 20:52:41)

<details>
<summary>Changes</summary>

### Minor Changes

- cd0cdc8: Support 'use policy data' option for commission items in data update tool
</details>

## 0.4.2 (2025-02-17 20:41:52)

<details>
<summary>Changes</summary>

### Patch Changes

- 3a6ec58: Added new comp reports approval workflow and accounting transactions integration
</details>

## 0.4.1 (2025-02-11 21:19:19)

<details>
<summary>Changes</summary>

### Patch Changes

- e9ab407: Support for all operators in the data update tool
</details>

## 0.4.0 (2025-01-23 18:44:56)

<details>
<summary>Changes</summary>

### Minor Changes

- 299a16f: Feature: allow manually reconcile in the commission data view
</details>

## 0.3.2 (2025-01-07 08:06:27)

<details>
<summary>Changes</summary>

### Patch Changes

- a7e9a95: RiskTag data syncing now populates policy type to product sub type field base on product type
</details>

## 0.3.1 (2024-12-23 04:08:56)

<details>
<summary>Changes</summary>

### Patch Changes

- d26945a: AgencyIntegrator worker now supports syncing specified policies
</details>

## 0.3.0 (2024-12-16 07:22:50)

<details>
<summary>Changes</summary>

### Minor Changes

- bdef42d: Add openapi to allow user to upload a document via openapi calls
</details>

## 0.2.0 (2025-02-17 20:41:52)

<details>
<summary>Changes</summary>

### Minor Changes

- 8a5ccb0: Feature: add AwsS3Worker to sync documents from aws storage
</details>

## 0.1.0 (2025-02-17 20:41:52)

<details>
<summary>Changes</summary>

### Minor Changes

- 667d7c3: Feature: add compensation type condition
</details>

## 0.0.2 (2025-02-17 20:41:52)

<details>
<summary>Changes</summary>

### Patch Changes

- 681e06b: Add tags filter to commission page
</details>

## 0.0.1 (2025-02-17 20:41:52)

<details>
<summary>Changes</summary>

### Patch Changes

- ddaac06: Fix: agents filter in policy data view
</details>

## 0.0.0 (2025-02-17 20:41:52)

<details>
<summary>Changes</summary>

### Patch Changes

- e461619: Add Admin > Activity log, to view recent activity across accounts.
- b11e9bd: AgencyIntegrator worker enhancement:
  - Enable iterative syncing, can run a full sync by select the full run option
  - Policy syncing doens't need commission data anymore
</details>

</details>
