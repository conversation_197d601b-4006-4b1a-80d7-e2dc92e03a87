# common

## Releases on 2025-07-14

### Version 0.24.7
<details>

### Patch Changes
 - Updated the statement amount extractor to exclude date-like values such as 06.16 or 6.16, also enable extracting the negative amounts.
 - Fix E2E test failures caused by new EnhancedSelector.
</details>

### Version 0.24.6
<details>

### Patch Changes
 - Improved transaction management with pagination support and refactored backend logic
 - Added support for AI extraction(Gemini, ChatGPT, Claude, etc.) and enabled the ability to choose between multiple extraction methods in document processing.
 - - Introduced a new field 'processing_date' in the reconciliation configuration.
  - Enhanced filtering logic to handle fields specific to reconciliation version 2.
  - Added constants for reconciliation versions and fields to improve maintainability.
</details>

### Version 0.24.5
<details>

### Patch Changes
 - Skip requirement for comp grid criteria if calc method doesn't require comp grid rates.
 - Customers page:
  - Move config from web to common, and add textFormatter for use in export.
  - Updated the `ExportCustomersHandler` to export based on the new config.
  - Added unit tests for textFormatter.
</details>

## Releases on 2025-07-09

### Version 0.24.4
<details>

### Patch Changes
 - Fix failing test in DocumentTextFormatter
 - Export alignment on Policy page
  - Move config from web to common, and add textFormatter for use in export
  - Move DataTransformation from web to common to be used by textFormatter
  - Refactor the report_data export logic with the following steps: getData → get fields based on config and accountSetting → fetch dynamicSelect data → formatData → export CSV
 - - Introduced a new model `uploaded_saved_reports` to manage file metadata.
  - Updated existing `saved_reports` model to include a foreign key reference to `uploaded_saved_reports`.
  - Added new endpoints for uploading, downloading, listing and filtering saved report files.
  - Created DTOs for file upload and retrieval operations.
 - Refactor reconciliation_data v2 api
  - Remove paging when querying the database
  - Implement sorting and pagination in JS
  - Add tests for sorting and pagination
  - Implemented batch retrieval instead of retrieving all records in a single query
 - Fix grouping failure caused by null premium_amount & split_percent
 - - Introduced `policyDataIfEmptyFields` constant to manage fields sourced from policy data.
  - Updated `queryFieldValues` to utilize `findMany` for fetching statement data, enhancing flexibility in selecting fields.
  - Modified `StatementFilterService` to incorporate policy data fields in filter logic.
  - Refactored table formatting in `Statements.tsx` to improve tooltip handling for policy data discrepancies.
</details>

## Releases on 2025-07-07

### Version 0.24.3
<details>

### Patch Changes
 - - Updated the `is_saved_report` parameter in `shouldGetSelectedFields` to accept a string instead of a boolean.
  - Adjusted related function calls across various API endpoints to pass the `is_saved_report` as a string.
  - Removed unnecessary comments and TODOs from the codebase for clarity.
  - Introduced `ChildRelationshipWithContact` type for better type safety.
 - - Move config from web to common, and add textFormatter for use in export.
  - Implemented `getDocumentFieldConfig` to retrieve field configurations based on account mode and timezone.
  - Added `formatExportData` and `mapHeaderFromFieldConfigs` to process export data and headers.
  - Updated the `ExportDocumentsHandler` to include timezone as a parameter.
  - Introduced new constants for field types and labels to improve maintainability.
</details>

## Releases on 2025-07-04

### Version 0.24.2
<details>

### Patch Changes
 - In admin accounts, sort users by active first. Add user state formatter.
</details>

## Releases on 2025-07-03

### Version 0.24.1
<details>

### Patch Changes
 - Allow user to select manual grouping calculation method
</details>

## Releases on 2025-07-02

### Version 0.24.0
<details>

### Minor Changes
 - Update Companies & Global Companies Page: 1. Link Suggestions: Added a feature to suggest potential account companies that can be linked to each global company. It shows how many unlinked companies are available and allows linking them directly. 2. Merge Function: Introduced a merge feature that lets users merge selected fields from one global company into another, helping maintain cleaner and more consistent data.

### Patch Changes
 - Made export agent columns CSV and added Formatter.label
 - Replace comp reports data source from snapshotdata to accounting transactions in new endpoint and new FE component in '.../comp-reports/report_str_id' route.
 - Replace comp reports data source from snapshotdata to accounting transactions part IV
</details>

## Releases on 2025-06-27

### Version 0.23.8
<details>

### Patch Changes
 - Replace comp reports data source from snapshotdata to accounting transactions part IV
</details>

## Releases on 2025-06-26

### Version 0.23.7
<details>

### Patch Changes
 - Align formatter for consistent decimal place handling
 - Fixed agentCommissionPayout calculation on backend.
</details>

### Version 0.23.6
<details>

### Patch Changes
 - Enabled report processors for all roles.
</details>

### Version 0.23.5
<details>

### Patch Changes
 - Normalized formatting for commissions amount and rates.
 - Suggested change
Refactored payout rate formatter logic and zero-value filtering for commissions page.
</details>

## Releases on 2025-06-25

### Version 0.23.4
<details>

### Patch Changes
 - Fix agent receivables not showing up on commissions page
 - Replace comp reports data source from snapshotdata to accounting transactions part III
</details>

## Releases on 2025-06-24

### Version 0.23.3
<details>

### Patch Changes
 - Document edit modal now shows any valid status.
 - Added support for select statements based on processing date range when grouping
</details>

